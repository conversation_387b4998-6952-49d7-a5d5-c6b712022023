# PDC雷达盾颜色和渐变实现分析

## 概述

PDC (Park Distance Control) 雷达盾的颜色和渐变系统是一个复杂的多层次渲染系统，包含距离颜色映射、透明度渐变、边缘混合等多种视觉效果。

## 1. 颜色系统架构

### 1.1 颜色分类
PDC系统根据不同区域使用不同的颜色配置：

```cpp
// 三种区域类型，每种都有内外两套颜色
ColorValues m_colorsInside0;   // 前后区域内侧颜色
ColorValues m_colorsOutside0;  // 前后区域外侧颜色
ColorValues m_colorsInside1;   // 左右区域内侧颜色  
ColorValues m_colorsOutside1;  // 左右区域外侧颜色
ColorValues m_colorsInside2;   // 角落区域内侧颜色
ColorValues m_colorsOutside2;  // 角落区域外侧颜色
```

### 1.2 距离颜色映射
每种颜色配置包含3个颜色值，对应不同的距离范围：

```cpp
ColorValues() {
    Color(255, 0, 60),    // 近距离 - 红色 (危险)
    Color(255, 150, 50),  // 中距离 - 橙色 (警告)  
    Color(255, 240, 50)   // 远距离 - 黄色 (安全)
}
```

## 2. 颜色计算机制

### 2.1 距离颜色插值
系统使用线性插值器根据实际距离计算颜色：

```cpp
Color getDistColor(const PdcSector& f_sector, std::size_t f_handle) {
    const float l_distance = f_sector.getDistance(f_handle);
    const bool l_interpolate = g_pdcSettings->m_colorInterpolation;
    
    switch (f_sector.m_zone) {
        case PdcSector::FRONTREAR:
            return l_interpolate 
                ? lerp(getDistColorOut0(l_distance), getDistColorIn0(l_distance), 
                       f_sector.m_onPathState.getInterpolatedState())
                : getBlendedFixedColor(l_distance, m_colorsInside0, m_colorDistances0);
    }
}
```

### 2.2 颜色混合模式
- **插值模式** (`m_colorInterpolation = true`): 内外颜色根据路径状态动态混合
- **固定模式** (`m_colorInterpolation = false`): 使用固定颜色配置，支持边缘混合

### 2.3 边缘混合算法
```cpp
Color getBlendedFixedColor(float f_distance, const ColorValues& f_colorValues, 
                          const DistanceValues& f_distanceValues) {
    float l_blendMargin = g_pdcSettings->m_blendMargin;
    
    // 使用smoothstep函数实现平滑过渡
    if (f_distance <= f_distanceValues.getDistance(0) + l_blendMargin) {
        float t = pc::util::smoothstep(
            f_distanceValues.getDistance(0) - l_blendMargin,
            f_distanceValues.getDistance(0) + l_blendMargin, 
            f_distance);
        return lerp(f_colorValues.getColor(0), f_colorValues.getColor(1), t);
    }
}
```

## 3. 透明度渐变系统

### 3.1 多层透明度
PDC雷达盾的三个组件有不同的透明度配置：

```cpp
// BaseMesh (底部边缘)
Opacity m_baseOpacityIn;     // 内侧透明度 (90)
Opacity m_baseOpacityOut;    // 外侧透明度 (50)

// UprightMesh (垂直渐变面)  
Opacity m_uprightOpacityUpIn;     // 上部内侧透明度 (20)
Opacity m_uprightOpacityUpOut;    // 上部外侧透明度 (0)
Opacity m_uprightOpacityDownIn;   // 下部内侧透明度 (70)
Opacity m_uprightOpacityDownOut;  // 下部外侧透明度 (40)
```

### 3.2 透明度计算
```cpp
Opacity::value_type getUprightOpacityUp(const PdcSector& f_sector) {
    return lerp(
        g_pdcSettings->m_uprightOpacityUpOut.getValue(),
        g_pdcSettings->m_uprightOpacityUpIn.getValue(),
        f_sector.m_onPathState.getInterpolatedState());
}
```

### 3.3 垂直渐变实现
UprightMesh通过在顶点着色器中为上下顶点分配不同透明度实现垂直渐变：

```cpp
// 下部顶点 (贴地面)
(*m_colors)[l_upIdx] = toVec4ub(l_distColor, l_uprightAlphaDown * l_edgeBlendingValues[j]);

// 上部顶点 (高处)  
(*m_colors)[l_upIdx + 1] = toVec4ub(l_distColor, l_uprightAlphaUp * l_edgeBlendingValues[j]);
```

## 4. 边缘混合效果

### 4.1 扇区连接混合
相邻扇区之间通过插值状态实现平滑连接：

```cpp
l_edgeBlendingValues.front() = l_sectorPrev.m_snapToNext.getInterpolatedState();
l_edgeBlendingValues.back() = l_sector.m_snapToNext.getInterpolatedState();

// 应用到最终颜色
const osg::Vec4ub l_baseColor = toVec4ub(l_distColor, l_baseAlpha * l_edgeBlendingValues[j]);
```

### 4.2 样条线平滑
每个扇区内部使用6个样条点实现平滑的曲线过渡。

## 5. 渲染管线

### 5.1 顶点颜色计算流程
1. **距离获取**: 从超声波传感器获取实际距离
2. **颜色映射**: 根据距离和区域类型计算基础颜色
3. **透明度计算**: 根据路径状态和配置计算透明度
4. **边缘混合**: 应用扇区间和样条点间的混合
5. **最终输出**: 生成RGBA顶点颜色

### 5.2 GPU渲染
- 使用顶点颜色插值实现平滑渐变
- 透明混合模式支持半透明效果
- Z-buffer写入禁用避免深度冲突

## 6. 关键特性

### 6.1 动态响应
- 实时根据传感器数据更新颜色
- 支持路径状态变化的平滑过渡
- 参数可在运行时动态调整

### 6.2 视觉效果
- **距离编码**: 红→橙→黄的直观距离表示
- **垂直渐变**: 从不透明底部到透明顶部的自然过渡
- **边缘融合**: 扇区间无缝连接
- **深度感知**: 多层结构增强立体感

### 6.3 性能优化
- 预计算颜色插值器减少实时计算
- 顶点级颜色计算避免像素级开销
- 条件渲染支持组件选择性显示

这个系统通过精心设计的多层次颜色和透明度管理，创造出既美观又实用的雷达盾视觉效果。
