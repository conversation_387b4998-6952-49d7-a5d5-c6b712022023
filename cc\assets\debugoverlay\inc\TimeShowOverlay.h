//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD RPA
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  timeshowoverlay.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_TIMESHOW_OVERLAY_H
#define CC_ASSETS_TIMESHOW_OVERLAY_H

#include "cc/target/common/inc/commonInterface.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"

#include <osg/Group>
#include <osg/MatrixTransform>

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"

#include <osg/Depth>
#include <osg/Geode>
#include <osg/Geometry>
#include <osg/MatrixTransform>
#include <osg/Texture2D>
#include <osgDB/ReadFile>


namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc
namespace cc
{
namespace assets
{
namespace timeshowoverlay
{

//======================================================
// SWInfoOverlaySettings
//------------------------------------------------------
/// Setting class for timeshowoverlay
/// <AUTHOR>
//======================================================
// class SWInfoOverlaySettings : public pc::util::coding::ISerializable
// {
// public:

//   SWInfoOverlaySettings()
//     : m_offset_SwVersion(osg::Vec3f(1.4f, 0.0f, 0.0f))
//     , m_offset_HwVersion(osg::Vec3f(1.5f, 0.0f, 0.0f))
//     , m_fontType(CONCAT_PATH("cc/resources/HYQiHei_55S.ttf"))
//   {
//   }

//   SERIALIZABLE(SWInfoOverlaySettings)
//   {
//     ADD_MEMBER(osg::Vec3f, offset_SwVersion);
//     ADD_MEMBER(osg::Vec3f, offset_HwVersion);
//     ADD_STRING_MEMBER(fontType);
//   }
//   osg::Vec3f m_offset_SwVersion;
//   osg::Vec3f m_offset_HwVersion;
//   std::string  m_fontType;

// };
inline std::string getCurrentTimeString() {
      std::time_t now = std::time(nullptr);
    std::tm* tmPtr = std::localtime(&now);
    std::ostringstream oss;
    oss << std::put_time(tmPtr, "%Y-%m-%d %H:%M:%S");
    return oss.str();
}
// extern pc::util::coding::Item<SWInfoOverlaySettings> g_displaySettings;

class TimeShowOverlayUpdateCallback: public osg::NodeCallback
{

public:
  TimeShowOverlayUpdateCallback(
                                osg::ref_ptr<osg::Geode> f_SwVersionDisGeode,
                                pc::core::Framework* f_pFramework
                                );

  virtual void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override;

  void updateSwVersion(const osg::NodeVisitor& f_nv, osg::ref_ptr<osg::Geode> f_Geode);

protected:
  virtual ~TimeShowOverlayUpdateCallback();

private:

  //! Copy constructor is not permitted.
  TimeShowOverlayUpdateCallback (const TimeShowOverlayUpdateCallback& other); // = delete
  //! Copy assignment operator is not permitted.
  TimeShowOverlayUpdateCallback& operator=(const TimeShowOverlayUpdateCallback& other); // = delete

  osg::ref_ptr<osg::Geode> m_SwVersionDisGeode;


  pc::core::Framework* m_pFramework;
};

//!
//! DigitalDistanceOverlay
//!
class TimeShowOverlay : public osg::MatrixTransform
{
public:

    TimeShowOverlay(pc::core::Framework* f_framework);

    virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:

    virtual void init();

    virtual ~TimeShowOverlay();

    pc::core::Framework* m_framework;
    vfc::uint32_t m_settingsModifiedCount;
    osg::ref_ptr<osg::Geode> m_SwVersionDisGeode;

private:
    //! Copy constructor is not permitted.
    bool isInit = false;
    TimeShowOverlay (const TimeShowOverlay& other); // = delete
    //! Copy assignment operator is not permitted.
    TimeShowOverlay& operator=(const TimeShowOverlay& other); // = delete

};


} // namespace timeshowoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TIMESHOW_OVERLAY_H
