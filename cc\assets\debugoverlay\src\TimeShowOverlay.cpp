//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD RPA
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  TimeShowOverlay.cpp
/// @brief
//=============================================================================

#include "cc/assets/debugoverlay/inc/TimeShowOverlay.h"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/osgx/inc/Quantization.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/assets/tileoverlay/inc/TileSettings.h" // PRQA S 1060

#include "osg/Depth"
#include "osg/Geode"
#include "osg/Geometry"
#include "osg/MatrixTransform"
#include "osg/Texture2D"
#include "osgDB/ReadFile"

#include <string>

using pc::util::logging::g_AppContext;
namespace cc
{
namespace assets
{
namespace timeshowoverlay
{

// pc::util::coding::Item<SWInfoOverlaySettings> g_displaySettings("SWInfoOverlay");

//!
//! RctaOverlay
//!
TimeShowOverlay::TimeShowOverlay(pc::core::Framework* f_framework)
  : osg::MatrixTransform() // PRQA S 2323
  , m_framework(f_framework) // PRQA S 2323
  , m_settingsModifiedCount(~0u) // PRQA S 2323
  , m_SwVersionDisGeode() // PRQA S 2323
{
  setName("TimeShowOverlay");
  setNumChildrenRequiringUpdateTraversal(1u);
}


TimeShowOverlay::~TimeShowOverlay() = default;


void TimeShowOverlay::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    // if (g_displaySettings->getModifiedCount() != m_settingsModifiedCount)
    // {
    if (!isInit) {
        init();
        addUpdateCallback(new TimeShowOverlayUpdateCallback(m_SwVersionDisGeode, m_framework));
        isInit = true;
    }
    //   m_settingsModifiedCount = g_displaySettings->getModifiedCount();
    // }
  }
  osg::Group::traverse(f_nv);
}


void TimeShowOverlay::init()
{
  removeChildren(0u, getNumChildren()); // PRQA S 3803
  const osg::ref_ptr<osg::Camera> hudCamera = new osg::Camera;
  hudCamera->setReferenceFrame(osg::Transform::ABSOLUTE_RF);
  hudCamera->setClearMask(GL_DEPTH_BUFFER_BIT);
  hudCamera->setRenderOrder(osg::Camera::POST_RENDER);
  hudCamera->setAllowEventFocus(false);


  hudCamera->setProjectionMatrix(osg::Matrix::ortho2D(0.0, 1176.0, 0.0, 1080.0));
  hudCamera->setViewMatrix(osg::Matrix::identity());

  m_SwVersionDisGeode = new osg::Geode;
  hudCamera->addChild(m_SwVersionDisGeode); // PRQA S 3803
//   addChild(m_SwVersionDisGeode); // PRQA S 3803
//   {
//   }

  addChild(hudCamera.get()); // PRQA S 3803

  const osg::ref_ptr<osgText::Text> l_swVersion = new osgText::Text;
//   l_swVersion->setPosition(-g_displaySettings->m_offset_SwVersion);
  l_swVersion->setFont(CONCAT_PATH("cc/resources/HYQiHei_55S.ttf"));
  l_swVersion->setColor(osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f));
  l_swVersion->setDrawMode(osgText::TextBase::TEXT );
  l_swVersion->setCharacterSize(30.0f);
  l_swVersion->setCharacterSizeMode(osgText::TextBase::SCREEN_COORDS);
  l_swVersion->setAxisAlignment(osgText::Text::XY_PLANE);
  l_swVersion->setAlignment(osgText::Text::LEFT_TOP);
  m_SwVersionDisGeode->addDrawable(l_swVersion); // PRQA S 3803

//   const osg::ref_ptr<osgText::Text> l_hwVersion = new osgText::Text;
//   l_hwVersion->setFont(g_displaySettings->m_fontType);
//   l_hwVersion->setPosition(-g_displaySettings->m_offset_HwVersion);
//   l_hwVersion->setColor(osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f));
//   l_hwVersion->setDrawMode(osgText::TextBase::TEXT );
//   l_hwVersion->setCharacterSize(30.0f);
//   l_hwVersion->setCharacterSizeMode(osgText::TextBase::SCREEN_COORDS);
//   l_hwVersion->setAxisAlignment(osgText::Text::XY_PLANE);
//   l_hwVersion->setRotation(osg::Quat(osg::DegreesToRadians(-90.0f), osg::Z_AXIS));
//   l_hwVersion->setAlignment(osgText::Text::CENTER_TOP);
//   m_HwVersionDisGeode->addDrawable(l_hwVersion); // PRQA S 3803

  osg::StateSet* const l_stateSet = getOrCreateStateSet();
  l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);
  pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
  l_basicTexShader.apply(l_stateSet); // PRQA S 3803
}

TimeShowOverlayUpdateCallback::TimeShowOverlayUpdateCallback(
                                                          osg::ref_ptr<osg::Geode> f_SwVersionDisGeode,
                                                          pc::core::Framework* f_pFramework
                                                          )
  : m_SwVersionDisGeode(f_SwVersionDisGeode) // PRQA S 2323 // PRQA S 4052
  , m_pFramework(f_pFramework) // PRQA S 2323
{
}

TimeShowOverlayUpdateCallback::~TimeShowOverlayUpdateCallback() = default;

void TimeShowOverlayUpdateCallback::updateSwVersion(const osg::NodeVisitor& /*f_nv*/, osg::ref_ptr<osg::Geode> f_Geode)
{
  cc::target::common::TimeShowStyle l_timeStyle; // PRQA S 4102
  if (m_pFramework->asCustomFramework()->m_TimeShow_ReceiverPort.hasData())
  {
    const cc::daddy::TimeStyleDaddy_t* const l_timeStyleDaddy = m_pFramework->asCustomFramework()->m_TimeShow_ReceiverPort.getData();
    l_timeStyle = l_timeStyleDaddy->m_Data;
  } else {
    XLOG_ERROR(g_AppContext, "m_TimeShow_ReceiverPort has no data");
  }

  if (l_timeStyle.m_visible)
  {
    const std::string l_timeString = getCurrentTimeString();
    f_Geode->setNodeMask(~0u);
    osgText::Text* const l_swVersion = static_cast<osgText::Text*>(f_Geode->getDrawable(0u)); // PRQA S 3076
    l_swVersion->setText(l_timeString.c_str());
    l_swVersion->setPosition(osg::Vec3f{l_timeStyle.m_positionX , l_timeStyle.m_positionY , 0.0f});
    l_swVersion->setCharacterSize(l_timeStyle.m_FontSize); // PRQA S 3011
  }
  else
  {
    f_Geode->setNodeMask(0u);
  }

}



void TimeShowOverlayUpdateCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
{
    if ((f_node == nullptr) || (f_nv == nullptr))
    {
        XLOG_ERROR(g_AppContext, "TimeShowOverlayUpdateCallback::operator() - f_node or f_nv is nullptr");
        return;
    }
    updateSwVersion(*f_nv, m_SwVersionDisGeode);
    traverse(f_node, f_nv);
}

} // namespace rctaoverlay
} // namespace assets
} // namespace cc
