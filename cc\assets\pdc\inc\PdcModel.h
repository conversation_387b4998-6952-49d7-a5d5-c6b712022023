//-------------------------------------------------------------------------------
// Copyright (c) 2021 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_PDC_PDCMODEL_H
#define CC_ASSETS_PDC_PDCMODEL_H

#include "pc/svs/vehicle/inc/Ultrasonic.h"
#include "CustomSystemConf.h"

#include <osg/Vec2>
#include <array>
#include <chrono>

namespace cc
{
namespace core
{
class CustomZoneLayout;
} // namespace core

namespace assets
{
namespace pdc
{
class PdcUpdateVisitor;

///
/// StateTransition
///
class StateTransition
{
public:

  typedef std::chrono::time_point<std::chrono::steady_clock> TimePoint;

  enum class State
  {
    On,
    TransitionOn,
    TransitionOff,
    Off
  };

  StateTransition(float f_transitionDuration, bool f_initialState);

  ///
  /// @brief Updates the state if necessary and returns current transition value
  ///
  /// @param f_state new state
  /// @param f_time timepoint of update
  /// @return float current value 0 when off has been reached or 1 if on
  ///
  void setState(bool f_state, const TimePoint& f_time = TimePoint::clock::now());

  inline float getDuration() const
  {
    return m_duration;
  }

  inline float getProgress() const
  {
    return m_progress;
  }

  inline State getState() const
  {
    return m_state;
  }

  float getInterpolatedState() const;

private:

  void evaluateState(bool f_state, float f_elapsedTime);

  float m_duration;
  float m_progress;
  TimePoint m_lastUpdate;
  State m_state;
};


///
/// PdcSector
///
class PdcSector
{
public:
  enum Zone : std::size_t
  {
    FRONTREAR,
    LEFTRIGHT,
    CORNER
  };

  enum Handle : std::size_t
  {
    HANDLE_RIGHT,
    HANDLE_CENTER,
    HANDLE_LEFT,
    NUM_HANDLES
  };

  typedef std::array<float, NUM_HANDLES> FloatArray;
  typedef std::array<osg::Vec2, NUM_HANDLES> Vec2Array;

  PdcSector();

  bool isValid() const;

  bool isConnectedToNext() const;

  inline osg::Vec2 getControlPoint(std::size_t f_handle, float f_distance) const
  {
    return m_base[f_handle] + (m_normal[f_handle] * f_distance);
  }

  inline osg::Vec2 getControlPoint(std::size_t f_handle) const
  {
    return getControlPoint(f_handle, m_distance[f_handle]);
  }

  inline float getDistance(std::size_t f_handle = HANDLE_CENTER) const
  {
    return m_distance[f_handle];
  }

  inline void setDistance(std::size_t f_handle, float f_distance)
  {
    m_distance[f_handle] = f_distance;
  }

  FloatArray m_distance;
  Vec2Array m_base;
  Vec2Array m_normal;
  StateTransition m_onPathState;
  StateTransition m_snapToNext;
  float m_supportDistanceLeft;
  float m_supportDistanceRight;
  float m_maxDistance;  // we have for each sector a different maxDistance when the visu starts to show it, so its in sync with the acustic signal
  Zone m_zone;
};


///
/// PdcModel
///
class PdcModel : public osg::Referenced
{
public:

  static const float INF_DISTANCE;

  enum : std::size_t
  {
    NUM_SECTORS = 16,
    NUM_SPLINE_POINTS_PER_SECTOR = 12, // 增加到12个点，提高平滑度
    NUM_SPLINE_POINTS = NUM_SPLINE_POINTS_PER_SECTOR * NUM_SECTORS
  };

  static_assert(static_cast<std::size_t>(NUM_SPLINE_POINTS_PER_SECTOR) > static_cast<std::size_t>(PdcSector::NUM_HANDLES),
    "Spline interpolation only works if number of spline points is greater than the number of controll points");
  PdcModel(const cc::core::CustomZoneLayout& f_layout);

  void setSectorZoneParams(PdcSector& f_sector, unsigned int f_index);

  void update(PdcUpdateVisitor& f_visitor);

  void update(const pc::vehicle::UltrasonicData* f_usData);

  virtual bool hasActiveSectors() const;

  inline const PdcSector& getSector(std::size_t f_index) const
  {
    return m_sectors[f_index];
  }

  inline const osg::Vec2& getSplinePoint(std::size_t f_index) const
  {
    return m_splinePoints[f_index];
  }

  inline const osg::Vec2& getSplineNormal(std::size_t f_index) const
  {
    return m_splineNormals[f_index];
  }

private:
  bool belowSnappingThreshold(const float f_sectorDistance, const float f_sectorNextDistance, const float f_threshold) const;
  void updateControlPoints(const pc::vehicle::UltrasonicData& f_usData, const StateTransition::TimePoint& f_time);
  void updateSnapping(const StateTransition::TimePoint& f_time);
  void updateSpline();
  void updateSplineNormals();

  typedef std::array<PdcSector, NUM_SECTORS> PdcSectorArray;
  PdcSectorArray m_sectors;

  typedef std::array<osg::Vec2, NUM_SPLINE_POINTS> SplinePointArray;
  SplinePointArray m_splinePoints;

  typedef std::array<osg::Vec2, NUM_SPLINE_POINTS> SplineNormalArray;
  SplineNormalArray m_splineNormals;
};

} // namespace pdc
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_PDC_PDCMODEL_H