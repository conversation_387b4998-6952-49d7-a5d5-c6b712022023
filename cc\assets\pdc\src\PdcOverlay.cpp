//-------------------------------------------------------------------------------
// Copyright (c) 2021 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/pdc/inc/PdcOverlay.h"
#include "cc/assets/pdc/inc/PdcModel.h"
#include "cc/assets/pdc/inc/PdcSettings.h"
#include "pc/svs/core/inc/Asset.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "cc/assets/common/inc/Vehicle.h"

#include "pc/svs/core/inc/Scene.h" // g_renderOrder
#include "pc/svs/util/math/inc/Conversions.hpp"
#include "cc/assets/trajectory/inc/TrajectoryAssets.h"


#include <osg/Depth>
#include <osg/Geometry>

namespace cc
{
namespace assets
{
namespace pdc
{

///
/// PdcOverlayCullCallback
///
class PdcOverlayCullCallback : public osg::Drawable::CullCallback
{
public:

  PdcOverlayCullCallback() = default;

  PdcOverlayCullCallback(const PdcOverlayCullCallback& f_other, const osg::CopyOp& f_copyOp = osg::CopyOp::SHALLOW_COPY)
    : osg::Object(f_other, f_copyOp)
    , osg::Drawable::CullCallback(f_other, f_copyOp)
  {
  }

  META_Object(cc::assets::pdc, PdcOverlayCullCallback);

  bool cull(osg::NodeVisitor* /*f_nv*/, osg::Drawable* f_drawable, osg::RenderInfo* /*f_renderInfo*/) const override // PRQA S 2120
  {
    if (f_drawable == nullptr)
    {
      return false; // nothing to cull
    }
    const osg::Geometry* const l_geometry = f_drawable->asGeometry();
    if (l_geometry != nullptr)
    {
      const auto& l_primitiveSetList = l_geometry->getPrimitiveSetList();
      for (const auto& l_primitiveSet : l_primitiveSetList)
      {
        if (0 == l_primitiveSet->getNumIndices())
        {
          return true; // avoid drawing empty primitive sets
        }
      }
    }
    return false;
  }

protected:

  virtual ~PdcOverlayCullCallback() = default; // PRQA S 2135

private:
  PdcOverlayCullCallback& operator=(const PdcOverlayCullCallback&) = delete;
};

namespace
{

// HSV颜色空间结构
struct HSV {
    float h, s, v; // 色相、饱和度、明度
    HSV(float hue = 0, float sat = 0, float val = 0) : h(hue), s(sat), v(val) {}
};

// RGB转HSV
HSV rgbToHsv(const Color& rgb) {
    float r = rgb.m_r / 255.0f;
    float g = rgb.m_g / 255.0f;
    float b = rgb.m_b / 255.0f;

    float max_val = std::max({r, g, b});
    float min_val = std::min({r, g, b});
    float delta = max_val - min_val;

    HSV hsv;
    hsv.v = max_val;
    hsv.s = (max_val == 0) ? 0 : delta / max_val;

    if (delta == 0) {
        hsv.h = 0;
    } else if (max_val == r) {
        hsv.h = 60 * fmod((g - b) / delta, 6);
    } else if (max_val == g) {
        hsv.h = 60 * ((b - r) / delta + 2);
    } else {
        hsv.h = 60 * ((r - g) / delta + 4);
    }

    if (hsv.h < 0) hsv.h += 360;
    return hsv;
}

// HSV转RGB
Color hsvToRgb(const HSV& hsv) {
    float c = hsv.v * hsv.s;
    float x = c * (1 - std::abs(fmod(hsv.h / 60.0f, 2) - 1));
    float m = hsv.v - c;

    float r, g, b;
    if (hsv.h < 60) {
        r = c; g = x; b = 0;
    } else if (hsv.h < 120) {
        r = x; g = c; b = 0;
    } else if (hsv.h < 180) {
        r = 0; g = c; b = x;
    } else if (hsv.h < 240) {
        r = 0; g = x; b = c;
    } else if (hsv.h < 300) {
        r = x; g = 0; b = c;
    } else {
        r = c; g = 0; b = x;
    }

    return Color(
        static_cast<uint8_t>((r + m) * 255),
        static_cast<uint8_t>((g + m) * 255),
        static_cast<uint8_t>((b + m) * 255)
    );
}

// HSV空间的颜色插值
Color lerpHSV(const Color& color1, const Color& color2, float t) {
    HSV hsv1 = rgbToHsv(color1);
    HSV hsv2 = rgbToHsv(color2);

    // 处理色相环绕
    float h_diff = hsv2.h - hsv1.h;
    if (h_diff > 180) {
        hsv2.h -= 360;
    } else if (h_diff < -180) {
        hsv2.h += 360;
    }

    HSV result;
    result.h = hsv1.h + (hsv2.h - hsv1.h) * t;
    result.s = hsv1.s + (hsv2.s - hsv1.s) * t;
    result.v = hsv1.v + (hsv2.v - hsv1.v) * t;

    if (result.h < 0) result.h += 360;
    if (result.h >= 360) result.h -= 360;

    return hsvToRgb(result);
}

void triangulateQuadMesh(
  PdcOverlay::IndexArray* f_indices,
  std::size_t f_numQuads,
  std::size_t f_vertexOffset)
{
  if (f_indices == nullptr)
  {
    return;
  }
  for (std::size_t i = 0; i < f_numQuads; ++i)
  {
    const std::size_t j = i * 2;
    f_indices->push_back(static_cast<PdcOverlay::IndexType> (f_vertexOffset + j));
    f_indices->push_back(static_cast<PdcOverlay::IndexType> (f_vertexOffset + j + 1));
    f_indices->push_back(static_cast<PdcOverlay::IndexType> (f_vertexOffset + j + 2));

    f_indices->push_back(static_cast<PdcOverlay::IndexType> (f_vertexOffset + j + 2));
    f_indices->push_back(static_cast<PdcOverlay::IndexType> (f_vertexOffset + j + 1));
    f_indices->push_back(static_cast<PdcOverlay::IndexType> (f_vertexOffset + j + 3));
  }
}

template <typename T>
inline T getIndex(std::size_t f_globalOffset, std::size_t f_localOffset, std::size_t f_stride)
{
  return static_cast<T> (f_globalOffset + (f_localOffset % f_stride));
}


void triangulateQuadMesh(
  PdcOverlay::IndexArray* f_indices,
  std::size_t f_numQuads,
  std::size_t f_globalOffset,
  std::size_t f_localOffset,
  std::size_t f_stride,
  bool f_fuse)
{
  if (f_indices == nullptr)
  {
      return;
  }
  if (f_fuse)
  {
    triangulateQuadMesh(f_indices, f_numQuads - 1, f_globalOffset + f_localOffset);
    f_localOffset += ((f_numQuads - 1) * 2);
    f_indices->push_back(getIndex<PdcOverlay::IndexType>(f_globalOffset, f_localOffset + 0, f_stride));
    f_indices->push_back(getIndex<PdcOverlay::IndexType>(f_globalOffset, f_localOffset + 1, f_stride));
    f_indices->push_back(getIndex<PdcOverlay::IndexType>(f_globalOffset, f_localOffset + 4, f_stride));

    f_indices->push_back(getIndex<PdcOverlay::IndexType>(f_globalOffset, f_localOffset + 4, f_stride));
    f_indices->push_back(getIndex<PdcOverlay::IndexType>(f_globalOffset, f_localOffset + 1, f_stride));
    f_indices->push_back(getIndex<PdcOverlay::IndexType>(f_globalOffset, f_localOffset + 5, f_stride));
  }
  else
  {
    triangulateQuadMesh(f_indices, f_numQuads, f_globalOffset + f_localOffset);
  }
}


void triangulateBaseMesh(
  PdcOverlay::IndexArray* f_indices,
  std::size_t f_sector,
  bool f_fuse)
{
  triangulateQuadMesh(
    f_indices,
    PdcOverlay::NUM_QUADS_BASE,
    0,
    f_sector * static_cast<std::size_t>(PdcOverlay::NUM_VERTICES_BASE),
    static_cast<std::size_t>(PdcOverlay::NUM_VERTICES_BASE) * static_cast<std::size_t>(PdcModel::NUM_SECTORS),
    f_fuse);
}


void triangulateUprightMesh(
  PdcOverlay::IndexArray* f_indices,
  std::size_t f_sector,
  bool f_fuse)
{
  triangulateQuadMesh(
    f_indices,
    PdcOverlay::NUM_QUADS_UPRIGHT,
    static_cast<std::size_t>(PdcOverlay::NUM_VERTICES_BASE) * static_cast<std::size_t>(PdcModel::NUM_SECTORS),
    f_sector * static_cast<std::size_t>(PdcOverlay::NUM_VERTICES_UPRIGHT),
    static_cast<std::size_t>(PdcOverlay::NUM_VERTICES_UPRIGHT) * static_cast<std::size_t>(PdcModel::NUM_SECTORS),
    f_fuse);
}

void triangulateTopMesh(
  PdcOverlay::IndexArray* f_indices,
  std::size_t f_sector,
  bool f_fuse)
{
  triangulateQuadMesh(
    f_indices,
    PdcOverlay::NUM_QUADS_BASE,
    static_cast<std::size_t>(PdcOverlay::NUM_VERTICES_BASE + PdcOverlay::NUM_VERTICES_UPRIGHT) * static_cast<std::size_t>(PdcModel::NUM_SECTORS),
    f_sector * static_cast<std::size_t>(PdcOverlay::NUM_VERTICES_BASE),
    static_cast<std::size_t>(PdcOverlay::NUM_VERTICES_BASE) * static_cast<std::size_t>(PdcModel::NUM_SECTORS),
    f_fuse);
}


// 改进的颜色混合函数，使用更自然的缓动
Color getBlendedFixedColorSmooth(float f_distance, const ColorValues& f_colorValues, const DistanceValues& f_distanceValues)
{
  float l_blendMargin = g_pdcSettings->m_blendMargin;

  // 使用更大的混合区域实现更平滑的过渡
  float l_extendedMargin = l_blendMargin * 1.5f;

  if (f_distance <= f_distanceValues.getDistance(0) - l_extendedMargin)
  {
    return f_colorValues.getColor(0);
  }
  else if (f_distance <= f_distanceValues.getDistance(0) + l_extendedMargin)
  {
    // 使用smootherstep实现更自然的过渡
    float t = pc::util::smootherstep(
        f_distanceValues.getDistance(0) - l_extendedMargin,
        f_distanceValues.getDistance(0) + l_extendedMargin,
        f_distance);
    return lerpHSV(f_colorValues.getColor(0), f_colorValues.getColor(1), t);
  }

  if (f_distance <= f_distanceValues.getDistance(1) - l_extendedMargin)
  {
    return f_colorValues.getColor(1);
  }
  else if (f_distance <= f_distanceValues.getDistance(1) + l_extendedMargin)
  {
    // 使用smootherstep实现更自然的过渡
    float t = pc::util::smootherstep(
        f_distanceValues.getDistance(1) - l_extendedMargin,
        f_distanceValues.getDistance(1) + l_extendedMargin,
        f_distance);
    return lerpHSV(f_colorValues.getColor(1), f_colorValues.getColor(2), t);
  }

  if (f_distance <= f_distanceValues.getDistance(2))
  {
    return f_colorValues.getColor(2);
  }
  return Color(0.0f, 0.0f, 0.0f);
}

// 保留原函数作为备用
Color getBlendedFixedColor(float f_distance, const ColorValues& f_colorValues, const DistanceValues& f_distanceValues)
{
  float l_blendMargin = g_pdcSettings->m_blendMargin;

  if (f_distance <= f_distanceValues.getDistance(0) - l_blendMargin)
  {
    return f_colorValues.getColor(0);
  }
  else if (f_distance <= f_distanceValues.getDistance(0) + l_blendMargin)
  {
    float t = pc::util::smoothstep(f_distanceValues.getDistance(0) - l_blendMargin,
                                    f_distanceValues.getDistance(0) + l_blendMargin, f_distance);
    return lerp(f_colorValues.getColor(0), f_colorValues.getColor(1), t);
  }

  if (f_distance <= f_distanceValues.getDistance(1) - l_blendMargin)
  {
    return f_colorValues.getColor(1);
  }
  else if (f_distance <= f_distanceValues.getDistance(1) + l_blendMargin)
  {
    float t = pc::util::smoothstep(f_distanceValues.getDistance(1) - l_blendMargin,
                                    f_distanceValues.getDistance(1) + l_blendMargin, f_distance);
    return lerp(f_colorValues.getColor(1), f_colorValues.getColor(2), t);
  }

  if (f_distance <= f_distanceValues.getDistance(2))
  {
    return f_colorValues.getColor(2);
  }
  return Color(0.0f, 0.0f, 0.0f);
}

Color getDistColor(const PdcSector& f_sector, std::size_t f_handle)
{
  const float l_distance = f_sector.getDistance(f_handle);
  const bool l_interpolate = g_pdcSettings->m_colorInterpolation;
//   auto qqq = g_pdcSettings->m_colorDistances0; //unused
//   auto www = g_pdcSettings->m_colorDistances1;
//   auto eee = g_pdcSettings->m_colorDistances2;
  switch (f_sector.m_zone)
  {
    case PdcSector::FRONTREAR:
    {
      return l_interpolate
        ? lerp(PdcSettings::getDistColorOut0(l_distance),
               PdcSettings::getDistColorIn0(l_distance),
               f_sector.m_onPathState.getInterpolatedState())
        : getBlendedFixedColorSmooth(l_distance,
                               g_pdcSettings->m_colorsInside0,
                               g_pdcSettings->m_colorDistances0);
    }
    case PdcSector::LEFTRIGHT:
    {
      return l_interpolate
        ? lerp(PdcSettings::getDistColorOut1(l_distance),
               PdcSettings::getDistColorIn1(l_distance),
               f_sector.m_onPathState.getInterpolatedState())
        : getBlendedFixedColorSmooth(l_distance,
                               g_pdcSettings->m_colorsInside1,
                               g_pdcSettings->m_colorDistances1);
    }
    case PdcSector::CORNER:
    default:
    {
      return l_interpolate
        ? lerp(PdcSettings::getDistColorOut2(l_distance),
               PdcSettings::getDistColorIn2(l_distance),
               f_sector.m_onPathState.getInterpolatedState())
        : getBlendedFixedColorSmooth(l_distance,
                               g_pdcSettings->m_colorsInside2,
                               g_pdcSettings->m_colorDistances2);
    }
  }
}


Opacity::value_type getBaseOpacity(const PdcSector& f_sector)
{
  return lerp(
    g_pdcSettings->m_baseOpacityOut.getValue(),
    g_pdcSettings->m_baseOpacityIn.getValue(),
    f_sector.m_onPathState.getInterpolatedState());
}

Opacity::value_type getUprightOpacityUp(const PdcSector& f_sector)
{
  return lerp(
    g_pdcSettings->m_uprightOpacityUpOut.getValue(),
    g_pdcSettings->m_uprightOpacityUpIn.getValue(),
    f_sector.m_onPathState.getInterpolatedState());
}

Opacity::value_type getUprightOpacityDown(const PdcSector& f_sector)
{
  return lerp(
    g_pdcSettings->m_uprightOpacityDownOut.getValue(),
    g_pdcSettings->m_uprightOpacityDownIn.getValue(),
    f_sector.m_onPathState.getInterpolatedState());
}

} // namespace


///
/// PdcOverlay
///
PdcOverlay::PdcOverlay(pc::core::Framework *f_framework)
:m_framework(f_framework)
{

}

PdcOverlay::PdcOverlay(const PdcOverlay& f_other, const osg::CopyOp& f_copyOp)
  : osg::Geode(f_other, f_copyOp)
  , m_vertices{}
  , m_indicies{}
  , m_colors{}
  , m_renderBinOrder(500)
{
}

void PdcOverlay::update(const PdcModel& f_model)
{
  if (0 == getNumDrawables())
  {
    init();
  }

  m_indicies->clear(); // invalidate

  if (!f_model.hasActiveSectors())
  {
    return;
  }
  vfc::uint32_t l_displayMode = EScreenID_NO_CHANGE;
  if (m_framework->asCustomFramework()->m_HUDislayModeSwitchDaddyReceiver.hasData())
  {
      l_displayMode = m_framework->asCustomFramework()->m_HUDislayModeSwitchDaddyReceiver.getData()->m_Data;
      if (l_displayMode == EScreenID_FRONT_BUMPER || l_displayMode == EScreenID_REAR_BUMPER)
      {
        setTopSplineState(false);
        setUprightMeshState(false);
      }
      else
      {
        setTopSplineState(true);
        setUprightMeshState(true);
      }
  }

    PdcSettings::updateInterpolators();

  // Calculate number of indices based on enabled components
  std::size_t MAX_NUM_INDICES = static_cast<std::size_t>(NUM_INDICES_BASE) * static_cast<std::size_t>(PdcModel::NUM_SECTORS); // Always include BaseMesh

  if(m_showUprightMesh)
  {
    MAX_NUM_INDICES += static_cast<std::size_t>(NUM_INDICES_UPRIGHT) * static_cast<std::size_t>(PdcModel::NUM_SECTORS);
  }

  if(g_pdcSettings->m_showTopSpline && m_showTopSpline)
  {
    MAX_NUM_INDICES += static_cast<std::size_t>(NUM_INDICES_BASE) * static_cast<std::size_t>(PdcModel::NUM_SECTORS); // TopMesh has same index count as BaseMesh
  }

  m_indicies->reserve(static_cast<unsigned int> (MAX_NUM_INDICES)); // PRQA S 2427

  splineVisualization(f_model);
  //sectorVisualization(f_model);

  m_vertices->dirty();
  m_indicies->dirty();
  m_colors->dirty();

  osg::Drawable* const l_drawable = getDrawable(0u);
  l_drawable->dirtyBound();
  static_cast<void> (l_drawable->getBound()); // force re-computation of bounding sphere, useful if this geometry is computed asynchronously
}


void PdcOverlay::splineVisualization(const PdcModel& f_model) // PRQA S 4211
{

  constexpr std::size_t VERTEX_OFFSET_UPRIGHT = static_cast<std::size_t>(NUM_VERTICES_BASE) * static_cast<std::size_t>(PdcModel::NUM_SECTORS);
  constexpr std::size_t VERTEX_OFFSET_TOP = (static_cast<std::size_t>(NUM_VERTICES_BASE) + static_cast<std::size_t>(NUM_VERTICES_UPRIGHT)) * static_cast<std::size_t>(PdcModel::NUM_SECTORS);
  // copy parameters locally to avoid inconsistencies when parameters are changed on-the-fly
  const float l_baseHeight = g_pdcSettings->m_baseHeight; // PRQA S 2446
  const float l_baseWidth = pc::util::mm2m(g_pdcSettings->m_baseWidth); // PRQA S 2446
  const float l_topHeight = l_baseHeight + pc::util::mm2m(g_pdcSettings->m_uprightHeight);
  const osg::Vec3 l_upperVertexDiff = osg::Vec3(0.0f, 0.0f, pc::util::mm2m(g_pdcSettings->m_uprightHeight));

  std::array<float, PdcModel::NUM_SPLINE_POINTS_PER_SECTOR> l_edgeBlendingValues; // PRQA S 2446 // PRQA S 4102
  l_edgeBlendingValues.fill(1.0f);

  for (std::size_t i = 0; i < PdcModel::NUM_SECTORS; ++i)
  {
    const PdcSector& l_sector = f_model.getSector(i);
    if (!l_sector.isValid())
    {
      continue;
    }

    //const std::size_t l_vertexOffset = NUM_VERTICES_PER_SECTOR * i; //(unused variable)
    //side sectors that are disabled via the member variables still contain valid distances, and can still snap to neighbors despite not shown.
    const unsigned int l_nextSectorIndex = (i + 1) % static_cast<std::size_t>(PdcModel::NUM_SECTORS); // PRQA S 2427 // PRQA S 4209 // PRQA S 3000 // PRQA S 3010
    const bool l_isConnectedToNext = l_sector.isConnectedToNext();
    triangulateBaseMesh(m_indicies.get(), i, l_isConnectedToNext);
    if(m_showUprightMesh)
    {
      triangulateUprightMesh(m_indicies.get(), i, l_isConnectedToNext);
    }
    if(g_pdcSettings->m_showTopSpline && m_showTopSpline)
    {
      triangulateTopMesh(m_indicies.get(), i, l_isConnectedToNext);
    }

    const PdcSector& l_sectorPrev = f_model.getSector(
        (i + static_cast<std::size_t>(PdcModel::NUM_SECTORS) - 1) % static_cast<std::size_t>(PdcModel::NUM_SECTORS));
    // do alpha value computations in signed 16 bit (not unsigned 8-bit), so adding/subtraction doesn't cause issues
    const auto l_baseAlpha = Opacity::toAlpha<ColorComponent>(getBaseOpacity(l_sector));
    const auto l_uprightAlphaUp = Opacity::toAlpha<ColorComponent>(getUprightOpacityUp(l_sector));
    const auto l_uprightAlphaDown = Opacity::toAlpha<ColorComponent>(getUprightOpacityDown(l_sector));
    l_edgeBlendingValues.front() = l_sectorPrev.m_snapToNext.getInterpolatedState();
    l_edgeBlendingValues.back() = l_sector.m_snapToNext.getInterpolatedState();

    for (std::size_t j = 0; j < PdcModel::NUM_SPLINE_POINTS_PER_SECTOR; ++j)
    {
      const std::size_t l_si = (i * static_cast<std::size_t>(PdcModel::NUM_SPLINE_POINTS_PER_SECTOR)) + j;
      const osg::Vec2& l_splinePoint = f_model.getSplinePoint(l_si);
      const osg::Vec2& l_splineNormal = f_model.getSplineNormal(l_si);
      // we have here a for loop with NUM_SPLINE_POINTS_PER_SECTOR = 6
      // getDistColor needs as a second parameter the handle -> NUM_HANDLES = 3
      // so to get the correct mapping now for the color we use a division by 2 and a floor() call
      // j = 0/1 -> handle = 0 -> HANDLE_RIGHT
      // j = 2/3 -> handle = 1 -> HANDLE_CENTER
      // j = 4/5 -> handle = 2 -> HANDLE_LEFT
      const Color l_distColor = getDistColor(l_sector, 1); //set to the center fixed to prevent color alternation due to larger sectors (and therefore distance variation between the spline points)
      // --- compute base geometry ---
      const std::size_t l_baseIdx = (j * 2) + (i * static_cast<std::size_t>(NUM_VERTICES_BASE));
      // position
      const osg::Vec3 l_basePos = osg::Vec3(l_splinePoint, l_baseHeight);
      (*m_vertices)[l_baseIdx] = l_basePos;
      const osg::Vec2 l_baseInnerPoint = l_splinePoint + (l_splineNormal * l_baseWidth);
      (*m_vertices)[l_baseIdx + 1] = osg::Vec3(l_baseInnerPoint, l_baseHeight);
      // color
      const osg::Vec4ub l_baseColor = toVec4ub(l_distColor, l_baseAlpha * l_edgeBlendingValues[j]); // PRQA S 3011
      (*m_colors)[l_baseIdx]     = l_baseColor;
      (*m_colors)[l_baseIdx + 1] = l_baseColor;
      if(m_showUprightMesh)
      {
        // --- compute upright geometry ---
        const std::size_t l_upIdx = (j * 2) + (i * static_cast<std::size_t>(NUM_VERTICES_UPRIGHT)) + VERTEX_OFFSET_UPRIGHT;
        // position
        (*m_vertices)[l_upIdx]     = l_basePos;
        (*m_vertices)[l_upIdx + 1] = l_basePos + l_upperVertexDiff;
        // color
        (*m_colors)[l_upIdx]     = toVec4ub(l_distColor, l_uprightAlphaDown * l_edgeBlendingValues[j]); // PRQA S 3011
        (*m_colors)[l_upIdx + 1] = toVec4ub(l_distColor, l_uprightAlphaUp * l_edgeBlendingValues[j]); // PRQA S 3011
      }

      if(g_pdcSettings->m_showTopSpline && m_showTopSpline)
      {
        // --- compute top geometry ---
        const std::size_t l_topIdx = (j * 2) + (i * static_cast<std::size_t>(NUM_VERTICES_BASE)) + VERTEX_OFFSET_TOP;
        // position
        const osg::Vec3 l_topPos = osg::Vec3(l_splinePoint, l_topHeight);
        (*m_vertices)[l_topIdx] = l_topPos;
        const osg::Vec2 l_topInnerPoint = l_splinePoint + (l_splineNormal * l_baseWidth);
        (*m_vertices)[l_topIdx + 1] = osg::Vec3(l_topInnerPoint, l_topHeight);
        // color
        const osg::Vec4ub l_topColor = toVec4ub(l_distColor, l_baseAlpha * l_edgeBlendingValues[j]); // PRQA S 3011
        (*m_colors)[l_topIdx]     = l_topColor;
        (*m_colors)[l_topIdx + 1] = l_topColor;
      }

    }
  }
  for (std::size_t i = 0; i < PdcModel::NUM_SECTORS; ++i)
  {
      const PdcSector& l_sectorPrev = f_model.getSector(
          (i + static_cast<std::size_t>(PdcModel::NUM_SECTORS) - 1) % static_cast<std::size_t>(PdcModel::NUM_SECTORS));
      /**
       * This condition is to resolve the PDC splines overlap/separated
       * (Ticket: https://rb-tracker.bosch.com/tracker08/browse/NRCSTWOAUDI-57202)
       *
       * When the vertices of the Base of a sector is created the baseInnerPoint is not getting snapped with the
       * previous or next sector. So, what I did was for every current sector being rendered I check if it has a
       * previous valid sector and both current and previous sectors are connected, if yes then I calculate the average
       * of current sectors 2nd vertex and previous sectors last vertex and update the current sectors 2nd vertex and
       * the previous sectors last vertex to the found average, this way we make the splines look connected.
       */
      if (l_sectorPrev.isValid() && l_sectorPrev.isConnectedToNext())
      {
        const std::size_t l_currentSectorFirstIdx = (i * static_cast<std::size_t>(NUM_VERTICES_BASE));
        const std::size_t l_previousSectorLastIdx =
              ((i + static_cast<std::size_t>(PdcModel::NUM_SECTORS) - 1)
              % static_cast<std::size_t>(PdcModel::NUM_SECTORS))
              * static_cast<std::size_t>(NUM_VERTICES_BASE) + static_cast<std::size_t>(NUM_VERTICES_BASE) - 1;

        const osg::Vec3 l_current = (*m_vertices)[l_currentSectorFirstIdx + 1];
        const osg::Vec3 l_prev    = (*m_vertices)[l_previousSectorLastIdx];

        // 改进的平滑连接算法 - 使用加权平均和切线连续性
        const float l_blendWeight = l_sectorPrev.m_snapToNext.getInterpolatedState();

        // 计算切线方向
        const osg::Vec3 l_prevTangent = l_prev - (*m_vertices)[l_previousSectorLastIdx - 2];
        const osg::Vec3 l_currentTangent = (*m_vertices)[l_currentSectorFirstIdx + 3] - l_current;

        // 使用Hermite插值保证切线连续性
        const float t = 0.5f; // 中点
        const osg::Vec3 l_smoothedPoint =
            l_prev * (2*t*t*t - 3*t*t + 1) +
            l_current * (-2*t*t*t + 3*t*t) +
            l_prevTangent * (t*t*t - 2*t*t + t) +
            l_currentTangent * (t*t*t - t*t);

        // 应用混合权重
        const osg::Vec3 l_finalPoint = lerp((l_current + l_prev) / 2.0f, l_smoothedPoint, l_blendWeight);

        (*m_vertices)[l_currentSectorFirstIdx + 1] = l_finalPoint;
        (*m_vertices)[l_previousSectorLastIdx]     = l_finalPoint;
    }
  }
}


void PdcOverlay::sectorVisualization(const PdcModel& f_model) // PRQA S 4211
{
  // copy parameters locally to avoid inconsistencies when parameters are changed on-the-fly
  const float l_baseHeight = g_pdcSettings->m_baseHeight; // PRQA S 2446
  const float l_baseWidth = pc::util::mm2m(g_pdcSettings->m_baseWidth); // PRQA S 2446
  const osg::Vec3 l_upperVertexDiff = osg::Vec3(0.0f, 0.0f, pc::util::mm2m(g_pdcSettings->m_uprightHeight));
  for (std::size_t i = 0; i < PdcModel::NUM_SECTORS; ++i)
  {
    const PdcSector& l_sector = f_model.getSector(i);
    if (!l_sector.isValid())
    {
      continue;
    }
    const std::size_t l_vertexOffset = static_cast<std::size_t>(NUM_VERTICES_PER_SECTOR) * i;
    triangulateQuadMesh(m_indicies.get(), NUM_QUADS_BASE, l_vertexOffset);
    triangulateQuadMesh(m_indicies.get(), NUM_QUADS_UPRIGHT, l_vertexOffset + static_cast<std::size_t>(NUM_VERTICES_BASE));

    const ColorComponent l_baseAlpha = Opacity::toAlpha<ColorComponent>(getBaseOpacity(l_sector));
    const ColorComponent l_uprightAlphaUp = Opacity::toAlpha<ColorComponent>(getUprightOpacityUp(l_sector));
    const ColorComponent l_uprightAlphaDown = Opacity::toAlpha<ColorComponent>(getUprightOpacityDown(l_sector));
    for (std::size_t j = 0; j < 3; ++j)
    {
      // --- compute base geometry ---
      const std::size_t k = (j * 2) + l_vertexOffset;
      // position
      const osg::Vec3 l_basePos = osg::Vec3(l_sector.getControlPoint(j), l_baseHeight);
      (*m_vertices)[k] = l_basePos;
      (*m_vertices)[k + 1] = osg::Vec3(l_sector.getControlPoint(j, l_sector.getDistance(j) - l_baseWidth), l_baseHeight);
      // color
      const Color l_distColor = getDistColor(l_sector, j);
      const osg::Vec4ub l_baseColor = toVec4ub(l_distColor, l_baseAlpha);
      (*m_colors)[k] = l_baseColor;
      (*m_colors)[k + 1] = l_baseColor;

      // --- compute upright geometry ---
      // position
      (*m_vertices)[static_cast<std::size_t>(NUM_VERTICES_BASE) + k] = l_basePos;
      (*m_vertices)[static_cast<std::size_t>(NUM_VERTICES_BASE) + k + 1] = l_basePos + l_upperVertexDiff;
      // color
      (*m_colors)[static_cast<std::size_t>(NUM_VERTICES_BASE) + k] = toVec4ub(l_distColor, l_uprightAlphaDown);
      (*m_colors)[static_cast<std::size_t>(NUM_VERTICES_BASE) + k + 1] = toVec4ub(l_distColor, l_uprightAlphaUp);
    }
  }
}


void PdcOverlay::init()
{
  osg::Geometry* const l_geometry = new osg::Geometry;
  l_geometry->setName("PdcOverlayGeometry");
  l_geometry->setCullCallback(new PdcOverlayCullCallback);
  l_geometry->setUseDisplayList(false);
  l_geometry->setUseVertexBufferObjects(true);

  osg::StateSet* const l_stateSet = l_geometry->getOrCreateStateSet();
  l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);
  l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);
  l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
  l_stateSet->setRenderBinDetails(500, "RenderBin");

  osg::Depth* const l_depth = new osg::Depth;
  l_depth->setWriteMask(false);
  l_stateSet->setAttribute(l_depth);

  // Calculate number of vertices based on enabled components
  unsigned int l_numVertices = static_cast<std::size_t>(NUM_VERTICES_BASE) * static_cast<std::size_t>(PdcModel::NUM_SECTORS); // Always include BaseMesh

  if(m_showUprightMesh)
  {
    l_numVertices += static_cast<std::size_t>(NUM_VERTICES_UPRIGHT) * static_cast<std::size_t>(PdcModel::NUM_SECTORS);
  }

  if(g_pdcSettings->m_showTopSpline && m_showTopSpline)
  {
    l_numVertices += static_cast<std::size_t>(NUM_VERTICES_BASE) * static_cast<std::size_t>(PdcModel::NUM_SECTORS); // TopMesh has same vertex count as BaseMesh
  }

  m_vertices = new VertexArray(l_numVertices);
  l_geometry->setVertexArray(m_vertices.get());

  m_colors = new ColorArray(l_numVertices);
  m_colors->setNormalize(true);
  l_geometry->setColorArray(m_colors.get(), osg::Array::BIND_PER_VERTEX);

  m_indicies = new IndexArray(osg::PrimitiveSet::TRIANGLES);
  l_geometry->addPrimitiveSet(m_indicies.get()); // PRQA S 3803

  addDrawable(l_geometry);  // PRQA S 3803

}

PdcAsset::PdcAsset(const cc::core::AssetId f_assetId, osg::Geode* f_asset)
  : pc::core::Asset(f_assetId, f_asset) // PRQA S 2323
{
  osg::StateSet* const l_stateset = getOrCreateStateSet();

  l_stateset->setMode(GL_BLEND, osg::StateAttribute::ON);
  l_stateset->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);
  l_stateset->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
  l_stateset->setRenderBinDetails(500, "RenderBin");

  //addCullCallback(new cc::assets::trajectory::MaskBlendingCullCallback());
}

} // namespace pdc
} // namespace assets
} // namespace cc
