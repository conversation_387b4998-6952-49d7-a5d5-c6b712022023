//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: LIA1LR David Liepelt (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomScene.h
/// @brief
//=============================================================================

#ifndef CC_CORE_CUSTOMSCENE_H
#define CC_CORE_CUSTOMSCENE_H

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/core/inc/Scene.h"
#include "pc/svs/core/inc/View.h"

// #include "cc/assets/trajectory/inc/ExpModeTrajectory.h"
// #include "cc/assets/trajectory/inc/GeneralTrajectoryLine.h"
// #include "cc/assets/trajectory/inc/TrajectoryAssets.h"
// #include "cc/assets/trajectory/inc/OutermostLine.h"
// #include "cc/assets/trajectory/inc/WheelTrack.h"
// #include "cc/assets/trajectory/inc/CoverPlate.h"
// #include "cc/assets/augmentedview/inc/AugmentedViewTransition.h"
// #include "cc/views/planview/inc/PlanView.h"

#define ENABLE_VERTICAL_MODE 0 // also defined in cc/virtcam/inc/CameraPositions.h. shoule be modified together.

namespace pc
{
namespace worker
{
namespace bowlshaping
{
class PolarBowlLayoutGenerator;
class SuperEllipsePolarBowlLayoutGenerator;
} // namespace bowlshaping
} // namespace worker
} // namespace pc

namespace cc
{
namespace assets
{
namespace augmentedview
{
class AugmentedViewTransition;
} // namespace augmentedview
} // namespace assets

namespace core
{

class CustomSceneSetting : public pc::util::coding::ISerializable
{
public:
  CustomSceneSetting()
    : m_apaBackgroundColor( 0.3f, 0.3f, 0.3f, 1.0f )
    , m_objectWallVariant(0)    // 0 - None, 1 - pts , 2 - pdc
  {
  }
  SERIALIZABLE(CustomSceneSetting)
  {
    ADD_MEMBER(osg::Vec4f, apaBackgroundColor);
    ADD_MEMBER(vfc::uint16_t, objectWallVariant);
  }
  osg::Vec4f m_apaBackgroundColor;
  vfc::uint16_t m_objectWallVariant;
};


extern pc::util::coding::Item<CustomSceneSetting> g_customerScene;

//! leave some space between each render bin definition
//! to allow internal ordering of each render item
enum RenderBinOrder
{
    RENDERBIN_ORDER_FLOOR                  = 0,
    RENDERBIN_ORDER_BASEPLATE              = 20,
    RENDERBIN_ORDER_WALL                   = 100,
    RENDERBIN_ORDER_TRAJECTORY_COVER_PLATE = 108,
    // RENDERBIN_ORDER_TRAJECTORY_STOPLINE             = 109,
    RENDERBIN_ORDER_TRAJECTORY_LEFT_OUTERMOST_LINE  = 110,
    RENDERBIN_ORDER_TRAJECTORY_RIGHT_OUTERMOST_LINE = 111,
    RENDERBIN_ORDER_TRAJECTORY_LEFT_WHEELTRACK      = 112,
    RENDERBIN_ORDER_TRAJECTORY_RIGHT_WHEELTRACK     = 113,
    RENDERBIN_ORDER_TRAJECTORY_TRAILER_TRAJECTORY   = 114,
    RENDERBIN_ORDER_TRAJECTORY_TRAILER_HITCH_LINE   = 115,
    RENDERBIN_ORDER_STB_SHADOW                      = 116,
    RENDERBIN_ORDER_STB_LINE                        = 117,
    // Add missing
    RENDERBIN_ORDER_TRAJECTORY_EXTRA_OUTERMOST_LINE = 118,
    RENDERBIN_ORDER_TRAJECTORY_DL1                  = 119,
    RENDERBIN_ORDER_TRAJECTORY_REFLINE              = 120,
    RENDERBIN_ORDER_TRAJECTORY_TRAILER_HITCH_CIRCLE = 121,
    RENDERBIN_ORDER_TRAJECTORY_EXTRA_WHEELTRACK     = 122,
    RENDERBIN_ORDER_SPLINEOVERLAY                   = 130,
    RENDERBIN_ORDER_POSITIONHELP_OVERLAYS           = 140,
    RENDERBIN_ORDER_CAR_IMPOSTOR                    = 160,
    RENDERBIN_ORDER_CAR_OPAQUE                      = 180,
    RENDERBIN_ORDER_TV2D_VEHICLE_IMPOSTOR           = 200,
    RENDERBIN_ORDER_TV2D_VEHICLE_DOORS              = 210,
    RENDERBIN_ORDER_OVERLAYS                        = 220,
    RENDERBIN_ORDER_DOOR_OVERLAYS                   = 240,
    RENDERBIN_ORDER_CURBWARNING                     = 260,
    RENDERBIN_ORDER_OBSTACLE_WARNING                = 280,
    RENDERBIN_ORDER_CAR_GLASS                       = 300,
    RENDERBIN_ORDER_MESH_SOUP                       = 320,
    RENDERBIN_ORDER_OVERSPEED_ICON                  = 340,
    RENDERBIN_ORDER_WHEEL_00                        = 400,
    RENDERBIN_ORDER_WHEEL_01                        = 401,
    RENDERBIN_ORDER_WHEEL_02                        = 402,
    RENDERBIN_ORDER_WHEEL_03                        = 403,
    RENDERBIN_ORDER_WHEEL_04                        = 404,
    RENDERBIN_ORDER_WHEEL_05                        = 405,
    RENDERBIN_ORDER_WHEEL_06                        = 406,
    RENDERBIN_ORDER_WHEEL_07                        = 407,
    RENDERBIN_ORDER_WHEEL_08                        = 408,
    RENDERBIN_ORDER_WHEEL_09                        = 409,
    RENDERBIN_ORDER_WHEEL_10                        = 410,
    RENDERBIN_ORDER_WHEEL_11                        = 411,
    RENDERBIN_ORDER_RCTA_OVERLAY                    = 450,
    RENDERBIN_ORDER_FREEPARKING_OVERLAY             = 500
};

enum PlanViewRenderBinOrder
{
    BYD_PLANVIEW_RENDERBIN_ORDER_FLOOR = 10, // magic number to make it stay above rendering order of snapshoot camera
                                             // and history camera of moving baseplate
    BYD_PLANVIEW_RENDERBIN_ORDER_UNDER_VEHICLE_2D = 99,
    BYD_PLANVIEW_RENDERBIN_ORDER_VEHICLE_2D       = 100,
    BYD_PLANVIEW_RENDERBIN_ORDER_ABOVE_VEHICLE_2D = 101, // reserve, only using when vehicle 2D icon is available
    BYD_PLANVIEW_RENDERBIN_ORDER_USS              = 110  // uss always above everything
};

enum class AssetId : vfc::uint32_t
{
    //! platform assets
    EASSETS_FLOOR,
    EASSETS_BOWL,
    EASSETS_PLANETARY_BOWL,
    EASSETS_VEHICLE,
    EASSETS_TV2D_IMPOSTOR, // this already includes the vehicle model
    EASSETS_TV2D_VERT_IMPOSTOR,
    EASSETS_TV2D_TRANSPARENT_IMPOSTOR,
    EASSETS_TV3D_TRANSPARENT_IMPOSTOR,
    EASSETS_IDLE_ICON,
    EASSETS_BASEPLATE,

    EASSETS_OBSTACLE_OVERLAY,
    EASSETS_OBSTACLE_OVERLAY_VEH_OFFSET,
    EASSETS_SPLINE_OVERLAY,
    EASSETS_SPLINE_OVERLAY_SHADOW,
    EASSETS_CALIB_OVERLAY,
    EASSETS_DEBUGOVERLAY,

    EASSETS_FRONT_WHEELS,
    EASSETS_ALL_WHEELS,
    EASSETS_VEHICLE_DOORS,

    //! customer specific assets
    EASSETS_AUGMENTED_VIEW_TRANSITION,
    EASSETS_AUGMENTED_VIEW_TRANSITION_VERT,
    EASSETS_DRIVABLE_PATH,
    EASSETS_DRIVABLE_PATH_AVM,
    EASSETS_SEE_THROUGH_BONNET,
    EASSETS_TRAJECTORY_CRAB_TRAJECTORY,
    EASSETS_TRAJECTORY_OUTERMOST_LINES,
    EASSETS_TRAJECTORY_OUTERMOST_LINES_COLORFUL,
    EASSETS_TRAJECTORY_WHEEL_TRACKS,
    EASSETS_TRAJECTORY_ACTION_POINTS,
    EASSETS_TRAJECTORY_DL1,
    // EASSETS_TRAJECTORY_STOPLINE_FOR_OMLS,
    // EASSETS_TRAJECTORY_STOPLINE_FOR_WTS,
    EASSETS_TRAJECTORY_TRAILER_HITCH,
    EASSETS_TRAJECTORY_TRAILER_TRAJECTORY,
    EASSETS_TRAJECTORY_COVERPLATE,
    EASSETS_TRAJECTORY_REFLINE,
    EASSETS_FISHEYE_OUTERMOST_LINES,
    EASSETS_FISHEYE_OUTERMOST_LINES_COLORFUL,
    EASSETS_FISHEYE_WHEELTRACKS,
    EASSETS_FISHEYE_COVERPLATE,
    EASSETS_FISHEYE_DL1,
    EASSETS_FISHEYE_DL1_COLORFUL,
    EASSETS_FISHEYE_RCTAOVERLAY,
    EASSETS_HITCH_ASSIST,
    EASSETS_TOW_ASSIST_DUAL,
    EASSETS_TOW_ASSIST_TRIPLE,
    EASSETS_TOW_ASSIST_TRIANGLE,
    EASSETS_RCTA_OVERLAY,
    EASSETS_DIGITAL_DISTANCE_DISPLAY,
    EASSETS_BUMPER_DIGITAL_DISTANCE_DISPLAY,
    EASSETS_SWINFO_OVERLAY,
    EASSETS_TIMESHOW_OVERLAY,
    EASSETS_SPEED_OVERLAY,
    EASSETS_SUPER_TRANSPARENT_OVERLAY,

    EASSETS_OBSTACLE_OVERLAY_DAI,

    EASSETS_PDCOVERLAY,

    //! bowl cam zones
    EASSETS_BOWL_FRONT_CAM,
    EASSETS_BOWL_REAR_CAM,
    EASSETS_BOWL_LEFT_CAM,
    EASSETS_BOWL_RIGHT_CAM,

    EASSETS_TV2D_PARKING_SPACE,

    EASSETS_UI_SETTINGBAR_ICON,
    EASSETS_UI_CAMERA_ICON,
    EASSETS_UI_FREEPARKING_BUTTON,
    EASSETS_UI_QUIT_BUTTON,
    EASSETS_UI_CONFIRM_BUTTON,
    EASSETS_UI_CONTINUE_BUTTON,
    EASSETS_UI_PARKOUT_LEFT_BUTTON,
    EASSETS_UI_PARKOUT_RIGHT_BUTTON,

    EASSETS_UI_WARNSYMBOL_USS,

    EASSETS_UI_PARKING_ICON,

    EASSETS_UI_PARKING_MODE,

    EASSETS_UI_PARKING_SEARCHING,
    EASSETS_UI_PARKING_BACKGROUND,

    EASSETS_UI_PARKING_E3_TOPVIEW,

    EASSETS_PARKINGSPOTS,
    EASSETS_STREETOVERLAY,
    EASSETS_BACKGROUND,
    // EASSETS_PARKINGTYPECONFIRM,
    EASSETS_PARKINGCONFIRMINTERFACE,
    EASSETS_UI_WHEELSEPARATOR_HORIZONTAL,
    EASSETS_UI_WHEELSEPARATOR_VERTICAL,
    EASSETS_UI_WARNSYMBOL_TEXT,
    EASSETS_APA_CORNER_ICONS,
    EASSETS_AVM_BACKGROUND_ICONS,
    EASSETS_UI_PARKINGPLANICON,

    EASSETS_FREEPARKING_OVERLAY,

    EASSETS_ECAL_PROGRESS_OVERLAY,

    EASSETS_VEHICLE_2D_ICON,

    EASSETS_DYNAMIC_GEAR_OVERLAYS,

    EASSETS_DYNAMIC_DISTANCE_OVERLAYS,

    EASSETS_VEHICLE_2D_WHEELS,

    EASSETS_LOWPOLY_VEHICLE_MODEL,

    EASSETS_COMBINED_FRONT_CAMERA_ASSET,
    EASSETS_COMBINED_REAR_CAMERA_ASSET,

    //! CORNER ASSETS
    EASSETS_CORNER_PLAN_VIEW,
    EASSETS_CORNER_SURROUND_VIEW,
    EASSETS_CORNER_FRONT_VIEW,
    EASSETS_CORNER_RIGHT_VIEW,
    EASSETS_CORNER_REAR_VIEW,
    EASSETS_CORNER_LEFT_VIEW,
    EASSETS_CORNER_LEFT_VIEW_5x,
    EASSETS_CORNER_RIGHT_VIEW_5x,
    EASSETS_CORNER_LEFT_REAR_VIEW_5x,
    EASSETS_CORNER_RIGHT_REAR_VIEW_5x,
    EASSETS_SEPARATOR_VIEW_5x,
    EASSETS_CORNER_ASSIST_FRONT_VIEW,
    EASSETS_CORNER_ASSIST_REAR_VIEW,
    EASSETS_CORNER_FRONT_JUNCTION_VIEW,
    EASSETS_CORNER_REAR_JUNCTION_VIEW,
    EASSETS_CORNER_FRONT_LEFT_WHEEL_VIEW,
    EASSETS_CORNER_FRONT_RIGHT_WHEEL_VIEW,
    EASSETS_CORNER_REAR_LEFT_WHEEL_VIEW,
    EASSETS_CORNER_REAR_RIGHT_WHEEL_VIEW,
    EASSETS_CORNER_BOTH_LEFT_WHEEL_VIEW,
    EASSETS_CORNER_BOTH_RIGHT_WHEEL_VIEW,

    //! CORNER VERTICAL ASSETS
    EASSETS_CORNER_PLAN_VIEW_VERT,
    EASSETS_CORNER_SURROUND_VIEW_VERT,
    EASSETS_CORNER_FRONT_VIEW_VERT,
    EASSETS_CORNER_RIGHT_VIEW_VERT,
    EASSETS_CORNER_REAR_VIEW_VERT,
    EASSETS_CORNER_LEFT_VIEW_VERT,
    EASSETS_CORNER_FRONT_JUNCTION_VIEW_VERT,
    EASSETS_CORNER_REAR_JUNCTION_VIEW_VERT,
    EASSETS_CORNER_FRONT_LEFT_WHEEL_VIEW_VERT,
    EASSETS_CORNER_FRONT_RIGHT_WHEEL_VIEW_VERT,
    EASSETS_CORNER_LEFT_WHEEL_VIEW_VERT,
    EASSETS_CORNER_RIGHT_WHEEL_VIEW_VERT,
    EASSETS_CORNER_REAR_LEFT_WHEEL_VIEW_VERT,
    EASSETS_CORNER_REAR_RIGHT_WHEEL_VIEW_VERT,
    EASSETS_CORNER_SIDE_FRONT_VIEW_VERT,
    EASSETS_CORNER_SIDE_REAR_VIEW_VERT,
    EASSETS_CORNER_SIDE_LEFT_VIEW_VERT,
    EASSETS_CORNER_SIDE_RIGHT_VIEW_VERT,
    EASSETS_CORNER_FULLSCREEN_VIEW_VERT,
    EASSETS_RIM_PROTECTION_LINE,
    EASSETS_VIRTUAL_REALITY,
    EASSETS_FISHEYE_TRANSITION,
    EASSETS_DYNAMIC_WHEEL_MASK,
    NUMBER_OF_ASSETS
};

//======================================================
// CustomViews
//------------------------------------------------------
/// List of existing views in the system.
/// Declares all available customer-specific view(ports)
/// and attaches them to the coding manager by inheriting from the ISerializable class.
/// <AUTHOR>
//======================================================
class CustomViews : public pc::util::coding::ISerializable
{
public:
    enum EView : vfc::uint32_t
    {
        CUSTOMVIEW_NO_VIEW = 0, // everything disabled
        DEBUG_VIEW         = 1,
        //
        JAPANESE_SIDE_FWD_VIEW = 2,
        JAPANESE_SIDE_BWD_VIEW = 3,
        JAPANESE_FRONT_VIEW    = 4,
        JAPANESE_REAR_VIEW     = 5,
        //
        PLAN_VIEW             = 6,
        PARK_ASSIST_PLAN_VIEW = 7,
        LSMG_VIEW             = 8,
        //
        FRONT_VIEW          = 9,
        REAR_VIEW           = 10,
        FRONT_JUNCTION_VIEW = 11,
        REAR_JUNCTION_VIEW  = 12,
        //
        HITCHASSIST_VIEW = 13,
        //
        BONNET_VIEW                     = 14,
        DRIVE_ASSIST_LEFT_VIEW          = 15,
        DRIVE_ASSIST_MAIN_VIEW          = 16,
        DRIVE_ASSIST_RIGHT_VIEW         = 17,
        DRIVE_ASSIST_LEFT_DUAL_VIEW     = 18,
        DRIVE_ASSIST_FRONT_FL_DUAL_VIEW = 19,
        DRIVE_ASSIST_FRONT_FR_DUAL_VIEW = 20,
        DRIVE_ASSIST_RIGHT_DUAL_VIEW    = 21,
        //
        SURROUND_VIEW = 22,
        //
        TOW_ASSIST_REAR_VIEW   = 23,
        TOW_ASSIST_DUAL_VIEW   = 24,
        TOW_ASSIST_TRIPLE_VIEW = 25,
        TOW_ASSIST_SINGLE_VIEW = 26,
        //
        FRONT_THREAT_VIEW = 27,
        REAR_THREAT_VIEW  = 28,
        //
        PROFILING_VIEW    = 29,
        ENGINEERING_VIEW  = 30,
        RAW_FISHEYE_VIEW  = 31,
        SIGNAL_DEBUG_VIEW = 32,
        //
        DAY_NIGHT_VIEW                 = 33,
        DAY_NIGHT_TA_DUAL_VIEW         = 34,
        DAY_NIGHT_TA_TRIPLE_LEFT_VIEW  = 35,
        DAY_NIGHT_TA_TRIPLE_RIGHT_VIEW = 36,
        DAY_NIGHT_DA_DUAL_RIGHT_VIEW   = 37,
        DAY_NIGHT_DA_DUAL_LEFT_VIEW    = 38,
        DAY_NIGHT_DA_TRIPLE_RIGHT_VIEW = 39,
        DAY_NIGHT_DA_TRIPLE_LEFT_VIEW  = 40,
        DAY_NIGHT_JAPANESE_VIEW        = 41,
        //
        RAW_FISHEYE_LEFT  = 42,
        RAW_FISHEYE_RIGHT = 43,
        RAW_FISHEYE_FRONT = 44,
        RAW_FISHEYE_REAR  = 45,
        RAW_FISHEYE_QUAD  = 46,
        //
        ECUSTOMVIEW_FUSI_VIEW = 47,
        //
        CALIB_ENGINEERING_VIEW      = 48,
        LSMGLSAEB_ENGINEERING_VIEW  = 49,
        LSMGLSAEB_IMPOSTORPLAN_VIEW = 50,
        TOWASSIST_ENGINEERING_VIEW  = 51,
        SINGLE_LEFT_VIEW            = 52,
        SINGLE_RIGHT_VIEW           = 53,
        FULL_SCREEN_VIEW            = 54,
        FRONT_WHEEL_LEFT_VIEW       = 55,
        FRONT_WHEEL_RIGHT_VIEW      = 56,
        REAR_WHEEL_LEFT_VIEW        = 57,
        REAR_WHEEL_RIGHT_VIEW       = 58,
        SETTING_BAR_VIEW            = 59,
        PARK_MODE_SELECT_VIEW       = 60,
        PARK_SEARCHING_VIEW         = 61,
        PARK_CONFIRMING_VIEW        = 62,
        // #if ENABLE_VERTICAL_MODE
        // Vertical mode
        VERTICAL_PLAN_VIEW              = 63,
        VERTICAL_FRONT_VIEW             = 64,
        VERTICAL_REAR_VIEW              = 65,
        VERTICAL_LEFT_VIEW              = 66,
        VERTICAL_RIGHT_VIEW             = 67,
        VERTICAL_FRONT_JUNCTION_VIEW    = 68,
        VERTICAL_REAR_JUNCTION_VIEW     = 69,
        VERTICAL_FRONT_WHEEL_LEFT_VIEW  = 70,
        VERTICAL_FRONT_WHEEL_RIGHT_VIEW = 71,
        VERTICAL_SURROUND_VIEW          = 72,
        WHEEL_SEPARATOR_HORIZONTAL_VIEW = 73,
        WHEEL_SEPARATOR_VERTICAL_VIEW   = 74,
        CPC_DEBUG_OVERLAY_VIEW          = 75,
        HORI_PARKING_PLAN_VIEW          = 76,
        HORI_PARKING_FLOOR_PLAN_VIEW    = 77,
        VERT_PARKING_PLAN_VIEW          = 78,
        VERT_PARKING_FLOOR_PLAN_VIEW    = 79,
        PARKING_VIEW                    = 80,
        PLAN_VIEW_VEHICLE2D             = 81,
        VERTICAL_PLAN_VIEW_VEHICLE2D    = 82,

        // Hori only for DENZA project
        //  WHEEL_SEPARATOR_HORIZONTAL_VIEW  = 83 ,
        //  CPC_DEBUG_OVERLAY_VIEW           = 84 ,
        //  HORI_PARKING_PLAN_VIEW           = 85 ,
        //  HORI_PARKING_FLOOR_PLAN_VIEW     = 86 ,
        //  PARKING_VIEW                     = 87 ,
        //  PLAN_VIEW_VEHICLE2D              = 88 ,
        PLAN_VIEW_USS_OVERLAYS = 83,
        PARK_DYNAMIC_GEAR_VIEW = 84,

        // For HC 23 project
        PARK_ASSIST_FRONT_VIEW          = 85,
        PARK_ASSIST_REAR_VIEW           = 86,
        HORI_SEPARATOR_VIEW             = 87,
        VERTICAL_BONNET_VIEW            = 88,
        VERTICAL_PLAN_VIEW_USS_OVERLAYS = 89,
        VERT_SEPARATOR_VIEW             = 90,
        VERT_PARKING_VIEW               = 91,
        VERTICAL_REAR_WHEEL_LEFT_VIEW   = 92,
        VERTICAL_REAR_WHEEL_RIGHT_VIEW  = 93,
        // #endif
        // Remote mode
        REMOTE_PLAN_VIEW           = 94,
        REMOTE_FRONT_VIEW          = 95,
        REMOTE_REAR_VIEW           = 96,
        REMOTE_LEFT_VIEW           = 97,
        REMOTE_RIGHT_VIEW          = 98,
        REMOTE_PLAN_VIEW_VEHICLE2D = 99,
        FRONT_COMBINED_VIEW        = 100,
        REAR_COMBINED_VIEW         = 101,

        // Custom
        HORI_PARKING_PLAN_VIEW_VEHICLE2D = 102,

        // VERT SIDE VIEW
        VERTICAL_SIDE_FRONT_VIEW = 103,
        VERTICAL_SIDE_REAR_VIEW  = 104,
        VERTICAL_SIDE_LEFT_VIEW  = 105,
        VERTICAL_SIDE_RIGHT_VIEW = 106,

        // VERT ADDITIONAL VIEW
        VERTICAL_WHEEL_LEFT_VIEW  = 107,
        VERTICAL_WHEEL_RIGHT_VIEW = 108,

        // VERT FULLSCREEN
        VERTICAL_PLAN_VIEW_FULLSCREEN              = 109,
        VERTICAL_PLAN_VIEW_FULLSCREEN_VEHICLE2D    = 110,
        VERTICAL_PLAN_VIEW_FULLSCREEN_USS_OVERLAYS = 111,

        REAR_LEFTVIEW_IN_FOUR_WINDOW  = 112,
        REAR_RIGHTVIEW_IN_FOUR_WINDOW = 113,
        BOTH_WHEEL_LEFT_VIEW          = 114,
        BOTH_WHEEL_RIGHT_VIEW         = 115,

        VERT_PARKING_PLAN_VIEW_VEHICLE2D    = 116,
        VERT_PARKING_PLAN_VIEW_USS_OVERLAYS = 117,
        HORI_PARKING_PLAN_VIEW_USS_OVERLAYS = 118,
        SUPER_TRANSPARENT_VIEW              = 119,

        // FULLSCREEN
        PLAN_VIEW_FULLSCREEN              = 120,
        PLAN_VIEW_FULLSCREEN_VEHICLE2D    = 121,
        PLAN_VIEW_FULLSCREEN_USS_OVERLAYS = 122,

        FRONT_BUMPER_VIEW                = 123,
        REAR_BUMPER_VIEW                 = 124,
        ULTRA_WIDE_SURROUND_VIEW         = 125,
        PLANETARY_VIEW                   = 126,
        IMAGE_IMAGE_LEFT                 = 127,
        IMAGE_IMAGE_RIGHT                = 128,
        IMAGE_IMAGE_PLANVIEW             = 129,
        IMAGE_IN_IMAGE_VEHICLE_2D        = 130,
        IMAGE_IN_IMAGE_USS_OVERLAY       = 131,
        FULL_SCREEN_TURN_AROUND_IN_PLACE = 132,
        FULL_SCREEN_SURROUND_VIEW        = 133,
        FRONT_VIEW_PANO                  = 134,
        REAR_VIEW_PANO                   = 135,

        IMAGE_IN_IMAGE_FRONT            = 136,
        IMAGE_IN_IMAGE_REAR             = 137,
        IMAGE_IN_IMAGE_PARK_TOP         = 138,
        IMAGE_IN_IMAGE_PARK_USS_OVERLAY = 139,
        IMAGE_IN_IMAGE_PARK_VEHICLE_2D  = 140,

        FULL_SCREEN_HORIZONTAL_MOVE  = 141,
        PLANETRY_VIEW_VEHICLE_2D     = 142,
        COMPASS_TURN_AROUND_IN_PLACE = 143,
        CARB_TURN_AROUND_IN_PLACE    = 144,
        BOTH_WHEEL_LEFT_ENLARGED_VIEW        = 145,
        BOTH_WHEEL_RIGHT_ENLARGED_VIEW       = 146,
        FRONT_WHEEL_LEFT_ENLARGED_VIEW       = 147,
        FRONT_WHEEL_RIGHT_ENLARGED_VIEW      = 148,
        REAR_WHEEL_LEFT_ENLARGED_VIEW        = 149,
        REAR_WHEEL_RIGHT_ENLARGED_VIEW       = 150,
        SINGLE_REAR_LEFT_VIEW                = 151,
        SINGLE_REAR_RIGHT_VIEW               = 152,
        IMAGE_IMAGE_LEFT_REAR                = 153,
        IMAGE_IMAGE_RIGHT_REAR               = 154,
        SURROUND_VIEW_FIND_CAR               = 155,
        // IMGUI
        IMGUI_VIEW = 200
    };

    CustomViews()
    {
    }

    SERIALIZABLE(CustomViews)
    {
        ADD_MEMBER(pc::core::Viewport, mainViewport);
        ADD_MEMBER(pc::core::Viewport, fourWindowLeftViewport);
        ADD_MEMBER(pc::core::Viewport, fourWindowRightViewport);
        ADD_MEMBER(pc::core::Viewport, fourWindowCombinedViewport);
        ADD_MEMBER(pc::core::Viewport, apaPlanViewport);
        ADD_MEMBER(pc::core::Viewport, apaParkingPlanViewport);
        ADD_MEMBER(pc::core::Viewport, apaAssistViewport);
        ADD_MEMBER(pc::core::Viewport, japaneseViewport);
        ADD_MEMBER(pc::core::Viewport, japaneseMainViewport);
        ADD_MEMBER(pc::core::Viewport, driveAssistMain);
        ADD_MEMBER(pc::core::Viewport, driveAssistLeft);
        ADD_MEMBER(pc::core::Viewport, driveAssistRight);
        ADD_MEMBER(pc::core::Viewport, driveAssistDualFrontViewportFL);
        ADD_MEMBER(pc::core::Viewport, driveAssistDualFrontViewportFR);
        ADD_MEMBER(pc::core::Viewport, driveAssistDualLeftViewport);
        ADD_MEMBER(pc::core::Viewport, driveAssistDualRightViewport);
        ADD_MEMBER(pc::core::Viewport, wideViewport);
        ADD_MEMBER(pc::core::Viewport, planViewport);
        ADD_MEMBER(pc::core::Viewport, dayNightViewport);
        ADD_MEMBER(pc::core::Viewport, dayNightTADualViewport);
        ADD_MEMBER(pc::core::Viewport, dayNightTATripleLeftViewport);
        ADD_MEMBER(pc::core::Viewport, dayNightTATripleRightViewport);
        ADD_MEMBER(pc::core::Viewport, dayNightDADualLeftViewport);
        ADD_MEMBER(pc::core::Viewport, dayNightDADualRightViewport);
        ADD_MEMBER(pc::core::Viewport, dayNightDATripleLeft);
        ADD_MEMBER(pc::core::Viewport, dayNightDATripleRight);
        ADD_MEMBER(pc::core::Viewport, dayNightJapanese);
        ADD_MEMBER(pc::core::Viewport, usableCanvasViewport);
        ADD_MEMBER(pc::core::Viewport, stbViewport);
        ADD_MEMBER(pc::core::Viewport, fusiView);
        ADD_MEMBER(pc::core::Viewport, fisheyeViewport);
        ADD_MEMBER(pc::core::Viewport, fullScreenViewport);
        ADD_MEMBER(pc::core::Viewport, frontWheelLeft);
        ADD_MEMBER(pc::core::Viewport, frontWheelRight);
        ADD_MEMBER(pc::core::Viewport, frontWheelLeftNarrow);
        ADD_MEMBER(pc::core::Viewport, frontWheelRightNarrow);
        ADD_MEMBER(pc::core::Viewport, rearWheelLeft);
        ADD_MEMBER(pc::core::Viewport, rearWheelRight);
        ADD_MEMBER(pc::core::Viewport, settingBarViewport);
        ADD_MEMBER(pc::core::Viewport, parkView);
        ADD_MEMBER(pc::core::Viewport, vertParkingPlanViewport);
        ADD_MEMBER(pc::core::Viewport, vertFullScreenViewport);
        ADD_MEMBER(pc::core::Viewport, vertMainViewport);
        ADD_MEMBER(pc::core::Viewport, vertPlanViewport);
        ADD_MEMBER(pc::core::Viewport, vertSideViewport);
        ADD_MEMBER(pc::core::Viewport, vertParkingUIViewport);
        ADD_MEMBER(pc::core::Viewport, vertWheelLeftViewport);
        ADD_MEMBER(pc::core::Viewport, vertWheelRightViewport);
        ADD_MEMBER(
            pc::core::Viewport, realApaParkingPlanViewport); // realApaParkingPlanViewport for freeparking hot zone
        ADD_MEMBER(pc::core::Viewport, realVertApaParkingPlanViewport);
        ADD_MEMBER(pc::core::Viewport, realMainViewport); // realMainViewport for 3D model rotation
        ADD_MEMBER(pc::core::Viewport, realMainViewportVert);
        ADD_MEMBER(pc::core::Viewport, realVirtualPlanviewPort); // realVirtualPlanviewPort for parking spot selection
        ADD_MEMBER(pc::core::Viewport, realVirtualPlanviewPortVert);
        ADD_MEMBER(pc::core::Viewport, vertBasePlanViewport);
        ADD_MEMBER(pc::core::Viewport, vertBaseMainViewport);
        ADD_MEMBER(pc::core::Viewport, remotePlanViewport);
        ADD_MEMBER(pc::core::Viewport, remoteMainViewport);
        ADD_MEMBER(pc::core::Viewport, wheelSeparator);
        ADD_MEMBER(pc::core::Viewport, vertWheelSeparator);
        ADD_MEMBER(pc::core::Viewport, cpcViewport);
        ADD_MEMBER(pc::core::Viewport, imageInimageLeftViewport);
        ADD_MEMBER(pc::core::Viewport, imageInimageRightViewport);
        ADD_MEMBER(pc::core::Viewport, imageInimagePlanviewViewport);
        ADD_MEMBER(pc::core::Viewport, wheelSeparatorAtRightBottom);
        ADD_MEMBER(pc::core::Viewport, wheelLeftAtRightBottom);
        ADD_MEMBER(pc::core::Viewport, wheelRightAtRightBottom);
        ADD_MEMBER(pc::core::Viewport, wheelLeftAtRightBottomNoSeparator);
        ADD_MEMBER(pc::core::Viewport, wheelRightAtRightBottomNoSeparator);
        ADD_MEMBER(pc::core::Viewport, parkSingleViewport);
        // ADD_MEMBER(pc::core::Viewport, parkSingleViewportRear);
        ADD_MEMBER(pc::core::Viewport, parkTop);
        ADD_MEMBER(pc::core::Viewport, sideFrontRearViewport);
        ADD_MEMBER(pc::core::Viewport, planViewportTR);
        ADD_MEMBER(pc::core::Viewport , frontWheelLeftEnlarged);
        ADD_MEMBER(pc::core::Viewport , frontWheelRightEnlarged);
        ADD_MEMBER(pc::core::Viewport , bothWheelLeftEnlarged);
        ADD_MEMBER(pc::core::Viewport , bothWheelRightEnlarged);
        ADD_MEMBER(pc::core::Viewport , rearWheelLeftEnlarged);
        ADD_MEMBER(pc::core::Viewport , rearWheelRightEnlarged);
        ADD_MEMBER(pc::core::Viewport , frontWheelLeftImageInImage);
        ADD_MEMBER(pc::core::Viewport , frontWheelRightImageInImage);
        ADD_MEMBER(pc::core::Viewport , singleViewImageInImage);
    }

    pc::core::Viewport m_mainViewport;
    pc::core::Viewport m_fourWindowLeftViewport;
    pc::core::Viewport m_fourWindowRightViewport;
    pc::core::Viewport m_fourWindowCombinedViewport;
    pc::core::Viewport m_apaPlanViewport;
    pc::core::Viewport m_apaParkingPlanViewport;
    pc::core::Viewport m_apaAssistViewport;
    pc::core::Viewport m_japaneseViewport;
    pc::core::Viewport m_japaneseMainViewport;
    pc::core::Viewport m_driveAssistMain;
    pc::core::Viewport m_driveAssistLeft;
    pc::core::Viewport m_driveAssistRight;
    pc::core::Viewport m_driveAssistDualFrontViewportFL;
    pc::core::Viewport m_driveAssistDualFrontViewportFR;
    pc::core::Viewport m_driveAssistDualLeftViewport;
    pc::core::Viewport m_driveAssistDualRightViewport;
    pc::core::Viewport m_wideViewport;
    pc::core::Viewport m_planViewport;
    pc::core::Viewport m_dayNightViewport;
    pc::core::Viewport m_dayNightTADualViewport;
    pc::core::Viewport m_dayNightTATripleLeftViewport;
    pc::core::Viewport m_dayNightTATripleRightViewport;
    pc::core::Viewport m_dayNightDADualLeftViewport;
    pc::core::Viewport m_dayNightDADualRightViewport;
    pc::core::Viewport m_dayNightDATripleLeft;
    pc::core::Viewport m_dayNightDATripleRight;
    pc::core::Viewport m_dayNightJapanese;
    pc::core::Viewport m_usableCanvasViewport;
    pc::core::Viewport m_stbViewport;
    pc::core::Viewport m_fusiView;
    pc::core::Viewport m_fisheyeViewport;
    pc::core::Viewport m_vertParkingPlanViewport;
    pc::core::Viewport m_fullScreenViewport;
    pc::core::Viewport m_frontWheelLeft;
    pc::core::Viewport m_frontWheelRight;
    pc::core::Viewport m_frontWheelLeftNarrow;
    pc::core::Viewport m_frontWheelRightNarrow;
    pc::core::Viewport m_rearWheelLeft;
    pc::core::Viewport m_rearWheelRight;
    pc::core::Viewport m_settingBarViewport;
    pc::core::Viewport m_parkView;
    pc::core::Viewport m_vertFullScreenViewport;
    pc::core::Viewport m_vertMainViewport;
    pc::core::Viewport m_vertPlanViewport;
    pc::core::Viewport m_vertSideViewport;
    pc::core::Viewport m_vertParkingUIViewport;
    pc::core::Viewport m_vertWheelLeftViewport;
    pc::core::Viewport m_vertWheelRightViewport;
    pc::core::Viewport m_wheelSeparator;
    pc::core::Viewport m_vertWheelSeparator;
    pc::core::Viewport m_cpcViewport;
    pc::core::Viewport m_imageInimageLeftViewport;
    pc::core::Viewport m_imageInimageRightViewport;
    pc::core::Viewport m_imageInimagePlanviewViewport;

    // ! BYD real displayed resolutions before resize
    pc::core::Viewport m_realApaParkingPlanViewport;
    pc::core::Viewport m_realVertApaParkingPlanViewport;
    pc::core::Viewport m_realMainViewport;
    pc::core::Viewport m_realMainViewportVert;
    pc::core::Viewport m_realVirtualPlanviewPort;
    pc::core::Viewport m_realVirtualPlanviewPortVert;

    pc::core::Viewport
        m_vertBasePlanViewport; // use temporarily to scale position of new project icons based on base project
    pc::core::Viewport
        m_vertBaseMainViewport; // use temporarily to scale position of new project icons based on base project

    pc::core::Viewport m_remotePlanViewport;
    pc::core::Viewport m_remoteMainViewport;

    pc::core::Viewport m_wheelSeparatorAtRightBottom;
    pc::core::Viewport m_wheelLeftAtRightBottom;
    pc::core::Viewport m_wheelRightAtRightBottom;
    pc::core::Viewport m_wheelLeftAtRightBottomNoSeparator;
    pc::core::Viewport m_wheelRightAtRightBottomNoSeparator;

    pc::core::Viewport m_parkSingleViewport;
    // pc::core::Viewport m_parkSingleViewportRear;
    pc::core::Viewport m_parkTop;
    pc::core::Viewport m_sideFrontRearViewport;
    pc::core::Viewport m_planViewportTR;
    pc::core::Viewport m_frontWheelLeftEnlarged;
    pc::core::Viewport m_frontWheelRightEnlarged;
    pc::core::Viewport m_bothWheelLeftEnlarged;
    pc::core::Viewport m_bothWheelRightEnlarged;
    pc::core::Viewport m_rearWheelLeftEnlarged;
    pc::core::Viewport m_rearWheelRightEnlarged;
    pc::core::Viewport m_frontWheelLeftImageInImage;
    pc::core::Viewport m_frontWheelRightImageInImage;
    pc::core::Viewport m_singleViewImageInImage;
};

extern pc::util::coding::Item<CustomViews> g_views;
//======================================================
// CustomScene
//------------------------------------------------------
/// Responsible for the arrangement of the graphical scene.
/// Manages the organization of the graphical scene with customer-specific elements.
/// Handles the different views and attaches the assets (visual entities) and rendering/culling callbacks to them.
/// Inherits from the Scene class.
/// <AUTHOR>
//======================================================
class CustomScene : public pc::core::Scene
{
public:
    CustomScene(bool f_enableImgui = false);

    virtual void init();

    const pc::core::Asset* getBowlAsset() const;
    const pc::core::Asset* getFloorAsset() const;
    const std::string      getVehicleModelName() const;
    void                   setVehicleModelName(const std::string& f_nodeName);

private:
    static pc::worker::bowlshaping::SuperEllipsePolarBowlLayoutGenerator* createSuperEllipseLayoutGenerator();

    pc::core::Asset* m_pBowlAsset;
    pc::core::Asset* m_pFloorAsset;

    // osg::ref_ptr<assets::augmentedview::AugmentedViewTransition> m_augmentedViewTransition;
    osg::ref_ptr<cc::assets::augmentedview::AugmentedViewTransition> m_augmentedViewTransitionHori;
    osg::ref_ptr<cc::assets::augmentedview::AugmentedViewTransition> m_augmentedViewTransitionVert;

    std::string m_vehicleModelName;

    bool m_enableImgui;
};

} // namespace core
} // namespace cc

#endif // CC_CORE_CUSTOMSCENE_H
