//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: LIA1LR David Liepelt (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomFramework.cpp
/// @brief
//=============================================================================

#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"

#include "ViewModeStateTransitionManager.h"
#include "cc/views/parkview/inc/ParkViewAnimationHandler.h"

#include "pc/svs/texfloor/odometry/inc/OdometrySimulatorNode.h"

#include "cc/assets/freeparkingoverlay/inc/FreeparkingOverlayManipulator.h"
#include "cc/assets/common/inc/VehicleTransparentHandler.h"

#ifdef TARGET_STANDALONE
#include "cc/util/touchmanipulator/inc/TouchManipulator.h"
#endif
#if defined(TARGET_STANDALONE) && defined(ENABLE_IMGUI)
#include "cc/imgui/inc/imgui_manager.h"
#include "cc/imgui/inc/imgui_view.h"
#include "cc/imgui/inc/imgui.h"
#include "cc/imgui/inc/imgui_impl_opengl3.h"
#endif

namespace cc
{
namespace core
{

#if defined(TARGET_STANDALONE) && defined(ENABLE_IMGUI)
#include "cc/imgui/inc/imgui_manager.h"
static cc::views::imgui::ImGuiView* getImguiView(pc::core::Scene* f_scene)
{
  if (!f_scene)
  {
    return nullptr;
  }
  auto imguiView =
    static_cast<cc::views::imgui::ImGuiView*>(f_scene->getView(cc::core::CustomViews::IMGUI_VIEW));
  return imguiView;
}
#endif


void CustomFramework::preFrame()
{
  Framework::preFrame();
#if defined(TARGET_STANDALONE) && defined(ENABLE_IMGUI)
  auto imguiView = getImguiView(getScene());
  if (imguiView)
  {
    imguiView->newFrame();
    imguiView->renderDockspace();
  }
#endif
}


void CustomFramework::postFrame()
{
  Framework::postFrame();
#if defined(TARGET_STANDALONE) && defined(ENABLE_IMGUI)
  auto imguiView = getImguiView(getScene());
  if (!imguiView)
  {
    return;
  }
  if (imguiView->getNodeMask() == 0u)
  {
    imguiView->render();
  }
#endif
}

bool CustomFramework::isImguiEnabled()
{
#if defined(TARGET_STANDALONE) && defined(ENABLE_IMGUI)
  auto imguiView = getImguiView(getScene());
  if (!imguiView)
  {
    return false;
  }
  return (imguiView->getNodeMask() != 0u) && ImGui::GetCurrentContext();
#else
    return false;
#endif
}


// The constructor takes the scene pointer, which will set up the 3D scene, the viewports and everything for rendering
// This constructor connects the receivers to the corresponding ports.
CustomFramework::CustomFramework(pc::core::Scene* f_pScene)
  : pc::core::Framework(f_pScene) // PRQA S 4050
  , m_socketCmdReceiver{}
  , m_dayNightModeReceiver{}
  , m_alsDippedBeamStateReceiver{}
  , m_trailerSvsReceiver{}
  , m_ViewModeState_ReceiverPort{}
  , m_distanceToStop_ReceiverPort{}
  , m_stopLineLocation_ReceiverPort{}
  , m_automaticParking_ReceiverPort{}
  , m_piviManualVideoSetup_ReceiverPort{}
  , m_movementDirection_ReceiverPort{}
  , m_powerMode_ReceiverPort{}
  , m_FCTARight_ReceiverPort{}
  , m_FCTALeft_ReceiverPort{}
  , m_RCTALeft_ReceiverPort{}
  , m_RCTARight_ReceiverPort{}
  , m_VRSwitchSVM_ReceiverPort{}
  , m_FreeParkingParkable_ReceiverPort{}
  , m_FCP_SVMButtonPressed_ReceiverPort{}
  , m_CcfReceiver{}
  , m_VehicleLightsReceiver{}
  , m_NFSM_ViewBufferStatus_ReceiverPort{}
  , m_GbcVehicleTransparency_ReceiverPort{}
  , m_GbcWheelTransparency_ReceiverPort{}
  , m_aebVmcOpMode_ReceiverPort{}
  , m_camCalibrationStatus_ReceiverPort{}
  , m_CpcNorminalCalibration_ReceiverPort{}
  , m_ObjectOverlayReq_ReceiverPort{}
  , m_hmiDataReceiver{}
  , m_HUCameraCommandDaddyReceiver{}
  , m_HUTouchTypeReceiver{}
  , m_HUTwoFingerTouchTypeReceiver{}
  , m_parkHmiParkingStatusReceiver{}
  , m_parkHmiParkingTypeReceiver{}
  , m_parkHmiParkParkngTypeSeldReceiver{}
  , m_parkHmiParkAPAPARKMODEReceiver{}
  , m_parkHmiParkingTypeVariantReceiver{}
  , m_parkHmiParkingTypeSelectedReceiver{}
  , m_AutoCamActivateButtonPressedStsReceiver{}
  , m_VehTransparenceStsFromSM_Receiver{}
  , m_VehTransparenceStsInternalReceiver{}
  , m_CpcOverlaySwitch_Receiver{}
  , m_parkHmiParkingModeReceiver{}
  , m_parkHmiParkDriverIndReceiver{}
  , m_parkHmiParkDriverIndExtReceiver{}
  , m_parkHmiParkingSideReceiver{}
  , m_parkHmiParkingFuncIndReceiver{}
  , m_parkHmiParkingQuitIndReceiver{}
  , m_parkHmiParkingQuitIndExtReceiver{}
  , m_parkHmiParkingRecoverIndReceiver{}
  , m_parkHmiParkingSpaceReceiver{}
  , m_parkHmiParkingReqReleaseBtnReceiver{}
  , m_parkHmiParkingFrontObjReceiver{}
  , m_parkEndPositionSearchingReceiver{}
  , m_parkFinalEndPositionReceiver{}
  , m_parkCurMoveTargetPositionReceiver{}
  , m_parkCarmoveNumberReceiver{}
  , m_parkCartravelDistDesiredReceiver{}
  , m_parkIsLastMoveReceiver{}
  , m_basePlateReq_RecieverPort{}
  , m_vehTransReq_RecieverPort{}
  , m_displayedView_ReceiverPort{}
  , m_freeModeSt_ReceiverPort{}
  , m_currentView_ReceiverPort{}
  , m_viewModeStatus_ReceiverPort{}
  , m_pasButtonStatus_ReceiverPort{}
  , m_parkConfirmButton_ReceiverPort{}
  , m_parkPauseButton_ReceiverPort{}
  , m_parkDirection_ReceiverPort{}
  , m_pasWarnToneStatus_ReceiverPort{}
  , m_pasStatus_ReceiverPort{}
  , m_sdwStatus_ReceiverPort{}
  , m_sdwStatusF_ReceiverPort{}
  , m_sdwStatusFM_ReceiverPort{}
  , m_sdwStatusRM_ReceiverPort{}
  , m_sdwStatusR_ReceiverPort{}
  , m_parkingInSpotsReceiver{}
  , m_parkAPASlotsReceiver{}
  , m_parkAPASlotsMarkReceiver{}
  , m_parkPSDirectionSelectedReceiver{}
  , m_ParkDriverIndSearchReceiver{}
  , m_freeparkingReceiver{}
  , m_freeparkingSocketReceiver{}
  , m_freeparkingRectInfoReceiver{}
  , m_freeparkingActiveReceiver{}
  , m_freeparkingConfirmButtonReceiver{}
  , m_freeparkingSpaceTypeReceiver{}
  , m_parkHmiRPAAvailableReceiver{}
  , m_vehColorSts_ReceiverPort{}
  , m_swVersionShowSwitch_ReceiverPort{}
  , m_swInfo_ReceiverPort{}
  , m_driverSteeringWheelAngleReceiver{}
  , m_customResetGuidanceVhmReceiver{}
  , m_vehiclePathAPGReceiver{}
  , m_dynamicGearStatus_ReceiverPort{}
  , m_customUltrasonicDataReceiver{}
  , m_DoorLockSts_ReceiverPort{}
  , m_tileSplineInterpolateArrayReceiver{}
  , m_solidBaseplateState_ReceiverPort{}
  , m_ParkDispLowPolyModelStsReceiver{}
  , m_HURemoteScreenReceiver{}
  , m_noticeRolling_ReceiverPort{}
  , m_HUvehTrans5xReceiver{}
  , m_HURadarWallButtonReceiver{}
  , m_HUTrailerButtonReceiver{}
  , m_HUvehTransCCReceiver{}
  , m_HUvehTransLevelReceiver{}
  , m_HUfreemodeAngleAzimuthReceiver{}
  , m_HUfreemodeAngleElevationReceiver{}
  , m_parkSlotReceiver{}
  , m_parkSlotRefinedReceiver{}
  , m_fusionObject_ReceiverPort{}
  , m_sitOcp_ReceiverPort{}
  , m_pedestrianObj_ReceiverPort{}
  , m_animationDaddy_ReceiverPort{}
  , m_HURotationPadReceiver{}
  , m_SVSRotateStatusDaddy_Receiver{}
  , m_dayNightThemeDaddy_Receiver{}
  , m_pdm3DCruSts_Receiver{}
  , m_pdmIntegtOpenSts_Receiver{}
  , m_pdmAutoCamSts_Receiver{}
  , m_pdmDynOverlaySts_Receiver{}
  , m_pdmOverlayDistSts_Receiver{}
  , m_pdmBasePlateSts_Receiver{}
  , m_pdmVehTransSts_Receiver{}
  , m_degradationFid_ReceiverPort{}
  , m_impostorTranspReceiver{}
  , m_PlanViewEnlargeStatusReceiver{}
  , m_sideViewEnableStatusReceiver{}
  , m_topViewEnableStatusReceiver{}
  , m_HUDislayModeSwitchDaddyReceiver{}
  , m_cpcToCpcWrapper_ReceiverPort{}
  , m_cpcWrapperToCpc_ReceiverPort{}
  , m_cpcToSvsOverlay_ReceiverPort{}
  , m_svsOverlayToCpc_ReceiverPort{}
  , m_parkHmiParkBreakPedalBeenReleasedBfReceiver{}
  , m_parkHmiParkParkbrkPedlAppldReceiver{}
  , m_ParkhmiToSvs_ReceiverPort{}
  , m_RemoveDistortion_ReceiverPort{}
  , m_ZoomLevel_ReceiverPort{}

  , m_BirdEyeViewSwitch_ReceiverPort{}

  , m_SurroundViewRotateAngle_ReceiverPort{}
  , m_CrabGuideline_ReceiverPort{}
  , m_viewModeStateTransitionManager{}
{
  cc::daddy::CustomDaddyPorts::sm_SocketCmdDaddy_SenderPort.connect(m_socketCmdReceiver);
  cc::daddy::CustomDaddyPorts::sm_PIVI_DayNightThemeReq_SenderPort.connect(m_dayNightModeReceiver);
  cc::daddy::CustomDaddyPorts::sm_AlsDippedBeamState_SenderPort.connect(m_alsDippedBeamStateReceiver);
  cc::daddy::CustomDaddyPorts::sm_TrailerSvs_SenderPort.connect(m_trailerSvsReceiver);
  cc::daddy::CustomDaddyPorts::sm_ViewModeState_SenderPort.connect( m_ViewModeState_ReceiverPort );
  cc::daddy::CustomDaddyPorts::sm_DistanceToStopDaddy_SenderPort.connect( m_distanceToStop_ReceiverPort );
  cc::daddy::CustomDaddyPorts::sm_StopLineLocationDaddy_SenderPort.connect( m_stopLineLocation_ReceiverPort );
  cc::daddy::CustomDaddyPorts::sm_AutomaticParkingDaddy_SenderPort.connect( m_automaticParking_ReceiverPort );
  cc::daddy::CustomDaddyPorts::sm_PIVI_ManualVideoSetupReq_SenderPort.connect(m_piviManualVideoSetup_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_NFSM_MovementDirectionUpdate_SenderPort.connect(m_movementDirection_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_CCF_SenderPort.connect( m_CcfReceiver );
  cc::daddy::CustomDaddyPorts::sm_CustomVehicleLights_SenderPort.connect( m_VehicleLightsReceiver );
  cc::daddy::CustomDaddyPorts::sm_PowerModeDaddy_SenderPort.connect(m_powerMode_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_FCTALeftDaddy_SenderPort.connect(m_FCTALeft_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_FCTARightDaddy_SenderPort.connect(m_FCTARight_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_RCTALeftDaddy_SenderPort.connect(m_RCTALeft_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_RCTARightDaddy_SenderPort.connect(m_RCTARight_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_VRSwitchSVMDaddy_SenderPort.connect(m_VRSwitchSVM_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_FreeParkingParkableDaddy_SenderPort.connect(m_FreeParkingParkable_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_FCP_SVMButtonPressedDaddy_SenderPort.connect(m_FCP_SVMButtonPressed_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_ViewBufferStatus_SenderPort.connect(m_NFSM_ViewBufferStatus_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_GbcVehicleTransparency_SenderPort.connect(m_GbcVehicleTransparency_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_GbcWheelTransparency_SenderPort.connect(m_GbcWheelTransparency_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_HUoverlayDistReqDaddy_SenderPort.connect(m_ObjectOverlayReq_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_ParkStatusDaddy_SenderPort.connect(m_parkHmiParkingStatusReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkTypeDaddy_SenderPort.connect(m_parkHmiParkingTypeReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkParkngTypeSeldDaddy_SenderPort.connect(m_parkHmiParkParkngTypeSeldReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkAPAPARKMODE_SenderPort.connect(m_parkHmiParkAPAPARKMODEReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkTypeVariantDaddy_SenderPort.connect(m_parkHmiParkingTypeVariantReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkTypeSelectedStDaddy_SenderPort.connect(m_parkHmiParkingTypeSelectedReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkModeDaddy_SenderPort.connect(m_parkHmiParkingModeReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkDriverIndDaddy_SenderPort.connect(m_parkHmiParkDriverIndReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkDriverIndExtDaddy_SenderPort.connect(m_parkHmiParkDriverIndExtReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkSideDaddy_SenderPort.connect(m_parkHmiParkingSideReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkFunctionIndDaddy_SenderPort.connect(m_parkHmiParkingFuncIndReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkQuitIndDaddy_SenderPort.connect(m_parkHmiParkingQuitIndReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkQuitIndExtDaddy_SenderPort.connect(m_parkHmiParkingQuitIndExtReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkRecoverIndDaddy_SenderPort.connect(m_parkHmiParkingRecoverIndReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkSpaceDaddy_SenderPort.connect(m_parkHmiParkingSpaceReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkReqReleaseBtnDaddy_SenderPort.connect(m_parkHmiParkingReqReleaseBtnReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkFrontObjDaddy_SenderPort.connect(m_parkHmiParkingFrontObjReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkEndPositionSearchingDaddy_SenderPort.connect(m_parkEndPositionSearchingReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkFinalEndPositionDaddy_SenderPort.connect(m_parkFinalEndPositionReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkCurMoveTargetPositionDaddy_SenderPort.connect(m_parkCurMoveTargetPositionReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkCarmoveNumberDaddy_SenderPort.connect(m_parkCarmoveNumberReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkCartravelDistDesiredDaddy_SenderPort.connect(m_parkCartravelDistDesiredReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkIsLastMoveDaddy_SenderPort.connect(m_parkIsLastMoveReceiver);
  cc::daddy::CustomDaddyPorts::sm_LSAEBVmcOpModeDaddy_SenderPort.connect( m_aebVmcOpMode_ReceiverPort );
  cc::daddy::CustomDaddyPorts::sm_CamCalibrationStatusDaddy_SenderPort.connect( m_camCalibrationStatus_ReceiverPort );
  cc::daddy::CustomDaddyPorts::sm_CpcNorminalCalibDaddy_SenderPort.connect( m_CpcNorminalCalibration_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SVSDisplayedViewDaddy_SenderPort.connect(m_displayedView_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SVSFreeModeStDaddy_SenderPort.connect(m_freeModeSt_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SVSCurrentViewDaddy_SenderPort.connect(m_currentView_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SVSViewModeStsDaddy_SenderPort.connect(m_viewModeStatus_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_PasButtonStatusDaddy_SenderPort.connect(m_pasButtonStatus_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_AutoCamActivButtonPressedStDaddy_SenderPort.connect(m_AutoCamActivateButtonPressedStsReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkConfirmButtonStDaddy_SenderPort.connect(m_parkConfirmButton_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_ParkPauseButtonStDaddy_SenderPort.connect(m_parkPauseButton_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_ParkDirectionStDaddy_SenderPort.connect(m_parkDirection_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_PasStatusDaddy_SenderPort.connect(m_pasStatus_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SdwStatusDaddy_SenderPort.connect(m_sdwStatus_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SdwFStatusDaddy_SenderPort.connect(m_sdwStatusF_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SdwFMStatusDaddy_SenderPort.connect(m_sdwStatusFM_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SdwRMStatusDaddy_SenderPort.connect(m_sdwStatusRM_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SdwRStatusDaddy_SenderPort.connect(m_sdwStatusR_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_parkingInSpots_SenderPort.connect(m_parkingInSpotsReceiver);
  cc::daddy::CustomDaddyPorts::sm_SVSVehTransStsDaddy_SenderPort.connect(m_VehTransparenceStsFromSM_Receiver);
  cc::daddy::CustomDaddyPorts::sm_SVSVehTransStsInternalDaddy_SenderPort.connect(m_VehTransparenceStsInternalReceiver);
  cc::daddy::CustomDaddyPorts::sm_CpcOverlaySwitchDaddy_SenderPort.connect(m_CpcOverlaySwitch_Receiver);
  cc::daddy::CustomDaddyPorts::sm_ParkAPA_ParkSpaceDaddy_SenderPort.connect(m_parkAPASlotsReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkAPA_ParkSpaceMarkDaddy_SenderPort.connect(m_parkAPASlotsMarkReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkPSDirectionSelectedDaddy_SenderPort.connect(m_parkPSDirectionSelectedReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkDriverIndSearchDaddy_SenderPort.connect(m_ParkDriverIndSearchReceiver);
  cc::daddy::CustomDaddyPorts::sm_SVSVehColorAckDaddy_SenderPort.connect(m_vehColorSts_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SwVersionShowSwitchDaddy_SenderPort.connect(m_swVersionShowSwitch_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SWInfoDaddy_SenderPort.connect(m_swInfo_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_DriverSteeringWheelAngleDaddy_SenderPort.connect(m_driverSteeringWheelAngleReceiver);
  cc::daddy::CustomDaddyPorts::sm_customVhmAbstRawDataDaddy_SenderPort.connect(m_customResetGuidanceVhmReceiver);
  cc::daddy::CustomDaddyPorts::sm_PMA_TravelDistDesired_SenderPort.connect(m_vehiclePathAPGReceiver);
  cc::daddy::CustomDaddyPorts::sm_customUltrasonicDataDaddySenderPort.connect(m_customUltrasonicDataReceiver);
  cc::daddy::CustomDaddyPorts::sm_DoorLockSts_SenderPort.connect(m_DoorLockSts_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_TimeShow_SenderPort.connect(m_TimeShow_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_tileSplineInterpolateArrayDaddySenderPort.connect(m_tileSplineInterpolateArrayReceiver);
  cc::daddy::CustomDaddyPorts::sm_groundplane_triangle_SenderPort.connect( m_solidBaseplateState_ReceiverPort );
  cc::daddy::CustomDaddyPorts::sm_ParkDispLowPolyModelStsDaddy_SenderPort.connect( m_ParkDispLowPolyModelStsReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_animationDaddy_SenderPort.connect( m_animationDaddy_ReceiverPort ) ;


  // //BYD HU ValOut
  // cc::daddy::CustomDaddyPorts::sm_SVSViewStateDaddy_SenderPort            .connect( m_SVSViewStateDaddy_Receiver );
  // cc::daddy::CustomDaddyPorts::sm_SVSWorkModeDaddy_SenderPort             .connect( m_SVSWorkModeDaddy_Receiver );
  cc::daddy::CustomDaddyPorts::sm_SVSRotateStatusDaddy_SenderPort         .connect( m_SVSRotateStatusDaddy_Receiver );
  cc::daddy::CustomDaddyPorts::sm_dayNightThemeDaddy_SenderPort         .connect( m_dayNightThemeDaddy_Receiver );
  // cc::daddy::CustomDaddyPorts::sm_SVSExpandedViewStateDaddy_SenderPort    .connect( m_SVSExpandedViewStateDaddy_Receiver );
  // cc::daddy::CustomDaddyPorts::sm_SVSNewExpandedViewStateDaddy_SenderPort .connect( m_SVSNewExpandedViewStateDaddy_Receiver );
  cc::daddy::CustomDaddyPorts::sm_HURemoteScreenReqDaddy_SenderPort. connect(m_HURemoteScreenReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUTransparentModeDaddy_SenderPort. connect(m_HUvehTrans5xReceiver);
  cc::daddy::CustomDaddyPorts::sm_HURadarWallButtonDaddy_SenderPort. connect(m_HURadarWallButtonReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUTrailerButtonDaddy_SenderPort. connect(m_HUTrailerButtonReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUvehTransReqDaddy_SenderPort. connect(m_HUvehTransCCReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUvehTransLevelDaddy_SenderPort. connect(m_HUvehTransLevelReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleAzimuthDaddy_SenderPort. connect(m_HUfreemodeAngleAzimuthReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleElevationDaddy_SenderPort. connect(m_HUfreemodeAngleElevationReceiver);
  //! HMI related
  cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.connect(m_hmiDataReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUCameraCommandsDaddySenderPort.connect(m_HUCameraCommandDaddyReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_SenderPort.connect(m_HUTouchTypeReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUbasePlateReqDaddy_SenderPort.connect(m_basePlateReq_RecieverPort);
  cc::daddy::CustomDaddyPorts::sm_HURotateStatusDaddy_SenderPort.connect(m_HURotationPadReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUTwoFingerTouchEvenTypeDaddy_SenderPort.connect(m_HUTwoFingerTouchTypeReceiver);

  //! pdm
  cc::daddy::CustomDaddyPorts::sm_PdmR5ToLinux_DynOverlayStsDaddy_SenderPort.connect(m_pdmDynOverlaySts_Receiver);
  cc::daddy::CustomDaddyPorts::sm_PdmR5ToLinux_OverlayDistStsDaddy_SenderPort.connect(m_pdmOverlayDistSts_Receiver);
  cc::daddy::CustomDaddyPorts::sm_PdmR5ToLinux_BasePlateStsDaddy_SenderPort.connect(m_pdmBasePlateSts_Receiver);
  cc::daddy::CustomDaddyPorts::sm_PdmR5ToLinux_VehTransStsDaddy_SenderPort.connect(m_pdmVehTransSts_Receiver);

  //! degradation
  cc::daddy::CustomDaddyPorts::sm_degradationFid_SenderPort.connect(m_degradationFid_ReceiverPort);

  cc::daddy::CustomDaddyPorts::sm_impostorTransparencySenderPort.connect(m_impostorTranspReceiver);

  //! Freeparking
  cc::daddy::CustomDaddyPorts::sm_freeparkingDataSenderPort.connect( m_freeparkingReceiver );
  cc::daddy::CustomDaddyPorts::sm_freeparkingSocketSenderPort.connect( m_freeparkingSocketReceiver );
  cc::daddy::CustomDaddyPorts::sm_freeparkingRectInfoSenderPort.connect( m_freeparkingRectInfoReceiver );
  cc::daddy::CustomDaddyPorts::sm_ParkFreeParkingActiveDaddy_SenderPort.connect( m_freeparkingActiveReceiver );
  cc::daddy::CustomDaddyPorts::sm_FreeParkingConfirmButtonStDaddy_SenderPort.connect( m_freeparkingConfirmButtonReceiver );
  cc::daddy::CustomDaddyPorts::sm_FreeParkingSpaceTypeButtonStDaddy_SenderPort.connect( m_freeparkingSpaceTypeReceiver );

  //! RPA
  cc::daddy::CustomDaddyPorts::sm_ParkRPAAvaliableDaddy_SenderPort.connect( m_parkHmiRPAAvailableReceiver );

  // cpc Extrinsic to cpc debug overlay
  // cc::daddy::CustomDaddyPorts::sm_cpcCornerDetectionDataDaddy_SenderPort.connect(m_cpcCornerDetection_Receiverport);

  // cpc status to cpc debug overlay
  // cc::daddy::CustomDaddyPorts::sm_cpcStatusDaddy_SenderPort.connect(m_cpcStatus_Receiverport);
  cc::daddy::CustomDaddyPorts::sm_cpcToCpcWrapper_SenderPort.connect(m_cpcToCpcWrapper_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_cpcWrapperToCpc_SenderPort.connect(m_cpcWrapperToCpc_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_cpcToSvsOverlay_SenderPort.connect(m_cpcToSvsOverlay_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_svsOverlayToCpc_SenderPort.connect(m_svsOverlayToCpc_ReceiverPort);

  //! Dynamic Gear
  cc::daddy::CustomDaddyPorts::sm_dynamicGearStatus_SenderPort.connect(m_dynamicGearStatus_ReceiverPort);

  // Notice Rolling
  cc::daddy::CustomDaddyPorts::sm_noticeRolling_SenderPort.connect(m_noticeRolling_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_planViewEnlargeStatus_SenderPort.connect(m_PlanViewEnlargeStatusReceiver);
  cc::daddy::CustomDaddyPorts::sm_sideViewEnableStatus_SenderPort.connect(m_sideViewEnableStatusReceiver);
  cc::daddy::CustomDaddyPorts::sm_topViewEnableStatus_SenderPort.connect(m_topViewEnableStatusReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.connect(m_HUDislayModeSwitchDaddyReceiver);

  //! Brake Pedal
  cc::daddy::CustomDaddyPorts::sm_brakePedalBeenReleasedBeforeDaddy_SenderPort.connect(m_parkHmiParkBreakPedalBeenReleasedBfReceiver);
  cc::daddy::CustomDaddyPorts::sm_brakePedalAppliedDaddy_SenderPort.connect(m_parkHmiParkParkbrkPedlAppldReceiver);

  //! Virtual Reality
  cc::daddy::CustomDaddyPorts::sm_ParkSlotDaddy_SenderPort.connect(m_parkSlotReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkSlotRefinedDaddy_SenderPort.connect(m_parkSlotRefinedReceiver);
  cc::daddy::CustomDaddyPorts::sm_fusionObject_SenderPort.connect(m_fusionObject_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_sitOcp_SenderPort.connect(m_sitOcp_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_pedestrianObj_SenderPort.connect(m_pedestrianObj_ReceiverPort);

  //! ParkhmiToSvs
  cc::daddy::CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort.connect(m_ParkhmiToSvs_ReceiverPort);

  //! RemoveDistortion
  cc::daddy::CustomDaddyPorts::sm_RemoveDistortion_SenderPort.connect(m_RemoveDistortion_ReceiverPort);

  //! ZoomLevel
  cc::daddy::CustomDaddyPorts::sm_ZoomLevel_SenderPort.connect(m_ZoomLevel_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_BirdEyeViewSwitch_SenderPort.connect(m_BirdEyeViewSwitch_ReceiverPort);

  cc::daddy::CustomDaddyPorts::sm_SurroundViewRotateAngle_SenderPort.connect(m_SurroundViewRotateAngle_ReceiverPort);
  //! CrabGuideline
  cc::daddy::CustomDaddyPorts::sm_CrabGuideline_SenderPort.connect(m_CrabGuideline_ReceiverPort);

  //! GoldenEmblem
  cc::daddy::CustomDaddyPorts::sm_GoldenEmblem_SenderPort.connect(m_GoldenEmblem_ReceiverPort);
  
}

// The destructor unbinds all the receivers from the corresponding ports.
CustomFramework::~CustomFramework()
{
  try
  {
  cc::daddy::CustomDaddyPorts::sm_SocketCmdDaddy_SenderPort.disconnect(m_socketCmdReceiver);
  cc::daddy::CustomDaddyPorts::sm_PIVI_DayNightThemeReq_SenderPort.disconnect(m_dayNightModeReceiver);
  cc::daddy::CustomDaddyPorts::sm_AlsDippedBeamState_SenderPort.disconnect(m_alsDippedBeamStateReceiver);
  cc::daddy::CustomDaddyPorts::sm_TrailerSvs_SenderPort.disconnect(m_trailerSvsReceiver);
  cc::daddy::CustomDaddyPorts::sm_ViewModeState_SenderPort.disconnect( m_ViewModeState_ReceiverPort );
  cc::daddy::CustomDaddyPorts::sm_DistanceToStopDaddy_SenderPort.disconnect( m_distanceToStop_ReceiverPort );
  cc::daddy::CustomDaddyPorts::sm_StopLineLocationDaddy_SenderPort.disconnect( m_stopLineLocation_ReceiverPort );
  cc::daddy::CustomDaddyPorts::sm_AutomaticParkingDaddy_SenderPort.disconnect( m_automaticParking_ReceiverPort );
  cc::daddy::CustomDaddyPorts::sm_PIVI_ManualVideoSetupReq_SenderPort.disconnect(m_piviManualVideoSetup_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_NFSM_MovementDirectionUpdate_SenderPort.disconnect(m_movementDirection_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_CCF_SenderPort.disconnect( m_CcfReceiver );
  cc::daddy::CustomDaddyPorts::sm_CustomVehicleLights_SenderPort.disconnect( m_VehicleLightsReceiver );
  cc::daddy::CustomDaddyPorts::sm_PowerModeDaddy_SenderPort.disconnect(m_powerMode_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_FCTALeftDaddy_SenderPort.disconnect(m_FCTALeft_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_FCTARightDaddy_SenderPort.disconnect(m_FCTARight_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_RCTALeftDaddy_SenderPort.disconnect(m_RCTALeft_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_RCTARightDaddy_SenderPort.disconnect(m_RCTARight_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_VRSwitchSVMDaddy_SenderPort.disconnect(m_VRSwitchSVM_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_FreeParkingParkableDaddy_SenderPort.disconnect(m_FreeParkingParkable_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_FCP_SVMButtonPressedDaddy_SenderPort.disconnect(m_FCP_SVMButtonPressed_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_ViewBufferStatus_SenderPort.disconnect(m_NFSM_ViewBufferStatus_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_GbcVehicleTransparency_SenderPort.disconnect(m_GbcVehicleTransparency_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_HUoverlayDistReqDaddy_SenderPort.disconnect(m_ObjectOverlayReq_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_ParkStatusDaddy_SenderPort.disconnect(m_parkHmiParkingStatusReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkTypeDaddy_SenderPort.disconnect(m_parkHmiParkingTypeReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkParkngTypeSeldDaddy_SenderPort.disconnect(m_parkHmiParkParkngTypeSeldReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkAPAPARKMODE_SenderPort.disconnect(m_parkHmiParkAPAPARKMODEReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkTypeVariantDaddy_SenderPort.disconnect(m_parkHmiParkingTypeVariantReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkTypeSelectedStDaddy_SenderPort.disconnect(m_parkHmiParkingTypeSelectedReceiver);
  cc::daddy::CustomDaddyPorts::sm_AutoCamActivButtonPressedStDaddy_SenderPort.disconnect(m_AutoCamActivateButtonPressedStsReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkModeDaddy_SenderPort.disconnect(m_parkHmiParkingModeReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkDriverIndDaddy_SenderPort.disconnect(m_parkHmiParkDriverIndReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkDriverIndExtDaddy_SenderPort.disconnect(m_parkHmiParkDriverIndExtReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkSideDaddy_SenderPort.disconnect(m_parkHmiParkingSideReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkFunctionIndDaddy_SenderPort.disconnect(m_parkHmiParkingFuncIndReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkQuitIndDaddy_SenderPort.disconnect(m_parkHmiParkingQuitIndReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkQuitIndExtDaddy_SenderPort.disconnect(m_parkHmiParkingQuitIndExtReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkRecoverIndDaddy_SenderPort.disconnect(m_parkHmiParkingRecoverIndReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkSpaceDaddy_SenderPort.disconnect(m_parkHmiParkingSpaceReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkReqReleaseBtnDaddy_SenderPort.disconnect(m_parkHmiParkingReqReleaseBtnReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkFrontObjDaddy_SenderPort.disconnect(m_parkHmiParkingFrontObjReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkEndPositionSearchingDaddy_SenderPort.disconnect(m_parkEndPositionSearchingReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkFinalEndPositionDaddy_SenderPort.disconnect(m_parkFinalEndPositionReceiver);
  cc::daddy::CustomDaddyPorts::sm_PMA_TravelDistDesired_SenderPort.disconnect(m_vehiclePathAPGReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkCurMoveTargetPositionDaddy_SenderPort.disconnect(m_parkCurMoveTargetPositionReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkCarmoveNumberDaddy_SenderPort.disconnect(m_parkCarmoveNumberReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkCartravelDistDesiredDaddy_SenderPort.disconnect(m_parkCartravelDistDesiredReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkIsLastMoveDaddy_SenderPort.disconnect(m_parkIsLastMoveReceiver);
  cc::daddy::CustomDaddyPorts::sm_LSAEBVmcOpModeDaddy_SenderPort.disconnect( m_aebVmcOpMode_ReceiverPort );
  cc::daddy::CustomDaddyPorts::sm_CamCalibrationStatusDaddy_SenderPort.disconnect( m_camCalibrationStatus_ReceiverPort );
  cc::daddy::CustomDaddyPorts::sm_CpcNorminalCalibDaddy_SenderPort.disconnect( m_CpcNorminalCalibration_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SVSDisplayedViewDaddy_SenderPort.disconnect(m_displayedView_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SVSFreeModeStDaddy_SenderPort.disconnect(m_freeModeSt_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SVSCurrentViewDaddy_SenderPort.disconnect(m_currentView_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SVSViewModeStsDaddy_SenderPort.disconnect(m_viewModeStatus_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_PasButtonStatusDaddy_SenderPort.disconnect(m_pasButtonStatus_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_ParkConfirmButtonStDaddy_SenderPort.disconnect(m_parkConfirmButton_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_ParkPauseButtonStDaddy_SenderPort.disconnect(m_parkPauseButton_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_ParkDirectionStDaddy_SenderPort.disconnect(m_parkDirection_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_PasStatusDaddy_SenderPort.disconnect(m_pasStatus_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SdwStatusDaddy_SenderPort.disconnect(m_sdwStatus_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SdwFStatusDaddy_SenderPort.disconnect(m_sdwStatusF_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SdwFMStatusDaddy_SenderPort.disconnect(m_sdwStatusFM_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SdwRMStatusDaddy_SenderPort.disconnect(m_sdwStatusRM_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SdwRStatusDaddy_SenderPort.disconnect(m_sdwStatusR_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_parkingInSpots_SenderPort.disconnect(m_parkingInSpotsReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkAPA_ParkSpaceDaddy_SenderPort.disconnect(m_parkAPASlotsReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkAPA_ParkSpaceMarkDaddy_SenderPort.disconnect(m_parkAPASlotsMarkReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkPSDirectionSelectedDaddy_SenderPort.disconnect(m_parkPSDirectionSelectedReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkDriverIndSearchDaddy_SenderPort.disconnect(m_ParkDriverIndSearchReceiver);
  cc::daddy::CustomDaddyPorts::sm_SVSVehColorAckDaddy_SenderPort.disconnect(m_vehColorSts_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SwVersionShowSwitchDaddy_SenderPort.disconnect(m_swVersionShowSwitch_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SWInfoDaddy_SenderPort.disconnect(m_swInfo_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_DriverSteeringWheelAngleDaddy_SenderPort.disconnect(m_driverSteeringWheelAngleReceiver);
  cc::daddy::CustomDaddyPorts::sm_customVhmAbstRawDataDaddy_SenderPort.disconnect(m_customResetGuidanceVhmReceiver);
  cc::daddy::CustomDaddyPorts::sm_customUltrasonicDataDaddySenderPort.disconnect(m_customUltrasonicDataReceiver);
  cc::daddy::CustomDaddyPorts::sm_DoorLockSts_SenderPort.disconnect(m_DoorLockSts_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_TimeShow_SenderPort.disconnect(m_TimeShow_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_tileSplineInterpolateArrayDaddySenderPort.disconnect(m_tileSplineInterpolateArrayReceiver);
  cc::daddy::CustomDaddyPorts::sm_groundplane_triangle_SenderPort.disconnect( m_solidBaseplateState_ReceiverPort );
  cc::daddy::CustomDaddyPorts::sm_ParkDispLowPolyModelStsDaddy_SenderPort.disconnect( m_ParkDispLowPolyModelStsReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_animationDaddy_SenderPort.disconnect( m_animationDaddy_ReceiverPort ) ;

  // //BYD HU ValOut
  // cc::daddy::CustomDaddyPorts::sm_SVSViewStateDaddy_SenderPort            .disconnect( m_SVSViewStateDaddy_Receiver );
  // cc::daddy::CustomDaddyPorts::sm_SVSWorkModeDaddy_SenderPort             .disconnect( m_SVSWorkModeDaddy_Receiver );
  cc::daddy::CustomDaddyPorts::sm_SVSRotateStatusDaddy_SenderPort         .disconnect( m_SVSRotateStatusDaddy_Receiver );
  cc::daddy::CustomDaddyPorts::sm_dayNightThemeDaddy_SenderPort         .disconnect( m_dayNightThemeDaddy_Receiver );
  // cc::daddy::CustomDaddyPorts::sm_SVSExpandedViewStateDaddy_SenderPort    .disconnect( m_SVSExpandedViewStateDaddy_Receiver );
  // cc::daddy::CustomDaddyPorts::sm_SVSNewExpandedViewStateDaddy_SenderPort .disconnect( m_SVSNewExpandedViewStateDaddy_Receiver );
  cc::daddy::CustomDaddyPorts::sm_HURemoteScreenReqDaddy_SenderPort. disconnect(m_HURemoteScreenReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUTransparentModeDaddy_SenderPort. disconnect(m_HUvehTrans5xReceiver);
  cc::daddy::CustomDaddyPorts::sm_HURadarWallButtonDaddy_SenderPort. disconnect(m_HURadarWallButtonReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUTrailerButtonDaddy_SenderPort. disconnect(m_HUTrailerButtonReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUvehTransReqDaddy_SenderPort. disconnect(m_HUvehTransCCReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUvehTransLevelDaddy_SenderPort. disconnect(m_HUvehTransLevelReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleAzimuthDaddy_SenderPort. disconnect(m_HUfreemodeAngleAzimuthReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleElevationDaddy_SenderPort. disconnect(m_HUfreemodeAngleElevationReceiver);

  //! HMI related
  cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.disconnect(m_hmiDataReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUCameraCommandsDaddySenderPort.disconnect(m_HUCameraCommandDaddyReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_SenderPort.disconnect(m_HUTouchTypeReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUbasePlateReqDaddy_SenderPort.disconnect(m_basePlateReq_RecieverPort);
  cc::daddy::CustomDaddyPorts::sm_SVSVehTransStsDaddy_SenderPort.disconnect(m_VehTransparenceStsFromSM_Receiver);
  cc::daddy::CustomDaddyPorts::sm_SVSVehTransStsInternalDaddy_SenderPort.disconnect(m_VehTransparenceStsInternalReceiver);
  cc::daddy::CustomDaddyPorts::sm_CpcOverlaySwitchDaddy_SenderPort.disconnect(m_CpcOverlaySwitch_Receiver);
  cc::daddy::CustomDaddyPorts::sm_HURotateStatusDaddy_SenderPort.disconnect(m_HURotationPadReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUTwoFingerTouchEvenTypeDaddy_SenderPort.disconnect(m_HUTwoFingerTouchTypeReceiver);

  //! pdm
  cc::daddy::CustomDaddyPorts::sm_PdmR5ToLinux_DynOverlayStsDaddy_SenderPort.disconnect(m_pdmDynOverlaySts_Receiver);
  cc::daddy::CustomDaddyPorts::sm_PdmR5ToLinux_OverlayDistStsDaddy_SenderPort.disconnect(m_pdmOverlayDistSts_Receiver);
  cc::daddy::CustomDaddyPorts::sm_PdmR5ToLinux_BasePlateStsDaddy_SenderPort.disconnect(m_pdmBasePlateSts_Receiver);
  cc::daddy::CustomDaddyPorts::sm_PdmR5ToLinux_VehTransStsDaddy_SenderPort.disconnect(m_pdmVehTransSts_Receiver);

  //! degradation
  cc::daddy::CustomDaddyPorts::sm_degradationFid_SenderPort.disconnect(m_degradationFid_ReceiverPort);

  cc::daddy::CustomDaddyPorts::sm_impostorTransparencySenderPort.disconnect(m_impostorTranspReceiver);

  //! Free parking
  cc::daddy::CustomDaddyPorts::sm_freeparkingDataSenderPort.disconnect( m_freeparkingReceiver );
  cc::daddy::CustomDaddyPorts::sm_freeparkingSocketSenderPort.disconnect( m_freeparkingSocketReceiver );
  cc::daddy::CustomDaddyPorts::sm_freeparkingRectInfoSenderPort.disconnect( m_freeparkingRectInfoReceiver );
  cc::daddy::CustomDaddyPorts::sm_ParkFreeParkingActiveDaddy_SenderPort.disconnect( m_freeparkingActiveReceiver );
  cc::daddy::CustomDaddyPorts::sm_FreeParkingConfirmButtonStDaddy_SenderPort.disconnect( m_freeparkingConfirmButtonReceiver );
  cc::daddy::CustomDaddyPorts::sm_FreeParkingSpaceTypeButtonStDaddy_SenderPort.disconnect( m_freeparkingSpaceTypeReceiver );

  //! RPA
  cc::daddy::CustomDaddyPorts::sm_ParkRPAAvaliableDaddy_SenderPort.disconnect( m_parkHmiRPAAvailableReceiver );

  // cpc Extrinsic
  // cc::daddy::CustomDaddyPorts::sm_cpcCornerDetectionDataDaddy_SenderPort.disconnect(m_cpcCornerDetection_Receiverport);

  // cpc status to cpc debug overlay
  // cc::daddy::CustomDaddyPorts::sm_cpcStatusDaddy_SenderPort.disconnect(m_cpcStatus_Receiverport);
  //! CPC
  cc::daddy::CustomDaddyPorts::sm_cpcToCpcWrapper_SenderPort.disconnect(m_cpcToCpcWrapper_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_cpcWrapperToCpc_SenderPort.disconnect(m_cpcWrapperToCpc_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_cpcToSvsOverlay_SenderPort.disconnect(m_cpcToSvsOverlay_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_svsOverlayToCpc_SenderPort.disconnect(m_svsOverlayToCpc_ReceiverPort);

  //! Dynamic Gear
  cc::daddy::CustomDaddyPorts::sm_dynamicGearStatus_SenderPort.disconnect(m_dynamicGearStatus_ReceiverPort);

  // Notice Rolling
  cc::daddy::CustomDaddyPorts::sm_noticeRolling_SenderPort.disconnect(m_noticeRolling_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_planViewEnlargeStatus_SenderPort.disconnect(m_PlanViewEnlargeStatusReceiver);
  cc::daddy::CustomDaddyPorts::sm_sideViewEnableStatus_SenderPort.disconnect(m_sideViewEnableStatusReceiver);
  cc::daddy::CustomDaddyPorts::sm_topViewEnableStatus_SenderPort.disconnect(m_topViewEnableStatusReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.disconnect(m_HUDislayModeSwitchDaddyReceiver);

  //! Virtual Reality
  cc::daddy::CustomDaddyPorts::sm_ParkSlotDaddy_SenderPort.disconnect(m_parkSlotReceiver);
  cc::daddy::CustomDaddyPorts::sm_ParkSlotRefinedDaddy_SenderPort.disconnect(m_parkSlotRefinedReceiver);
  cc::daddy::CustomDaddyPorts::sm_fusionObject_SenderPort.disconnect(m_fusionObject_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_sitOcp_SenderPort.disconnect(m_sitOcp_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_pedestrianObj_SenderPort.disconnect(m_pedestrianObj_ReceiverPort);

  //! ParkHmiToSvs
  cc::daddy::CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort.disconnect(m_ParkhmiToSvs_ReceiverPort);

  //! RemoveDistortion
  cc::daddy::CustomDaddyPorts::sm_RemoveDistortion_SenderPort.disconnect(m_RemoveDistortion_ReceiverPort);

  //! ZoomLevel
  cc::daddy::CustomDaddyPorts::sm_ZoomLevel_SenderPort.disconnect(m_ZoomLevel_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_BirdEyeViewSwitch_SenderPort.disconnect(m_BirdEyeViewSwitch_ReceiverPort);
  }
  catch(...)
  {
  }
}

bool CustomFramework::init(osg::GraphicsContext* f_pGc)
{
  // Initialize base class
  if (!Framework::init(f_pGc))
  {
    return false;
  }

  osgViewer::View* const l_view = getView();

  // Register view mode handler, which reacts to ViewMode state changes
  m_viewModeStateTransitionManager = new cc::core::ViewModeStateTransitionManager(this);
//   l_view->addEventHandler(m_viewModeStateTransitionManager);
  auto& eventHandlerList = l_view->getEventHandlers();
  eventHandlerList.push_front(m_viewModeStateTransitionManager);

  // auto parkView = getScene()->getView(CustomViews::PARK_SEARCHING_VIEW);
  // if (parkView)
  // {
  //   parkView->addEventCallback(new cc::core::ParkViewAnimationHandler{this});
  // }
  // l_view->addEventHandler(new cc::core::ParkViewAnimationHandler{this});
  l_view->addEventHandler(new cc::core::VehicleTransparentHandler{this});

  // odometrySimulator - just used in the SIL
  if(pc::texfloor::odometry::g_odometrySimulator->m_useOdometrySimulation)
  {
    pc::texfloor::odometry::OdometrySimulatorNode* const l_odometrySimulator = new pc::texfloor::odometry::OdometrySimulatorNode();
    l_view->addEventHandler(l_odometrySimulator);
  }

  // cc::assets::freeparkingoverlay::FreeparkingOverlayManipulator* l_fpoverlaymanipulator = new cc::assets::freeparkingoverlay::FreeparkingOverlayManipulator(this);
  // l_view->addEventHandler(l_fpoverlaymanipulator);

#ifdef TARGET_STANDALONE
  auto* l_touchManipulator = new cc::util::touchmanipulator::TouchManipulator(this);
  getView()->addEventHandler(l_touchManipulator);
#endif
  return true;
}

// Take the data from the through receivers
void CustomFramework::updateReceiverPorts()
{
  //! update pc receiver ports first
  pc::core::Framework::updateReceiverPorts();

  m_socketCmdReceiver.update();
  m_dayNightModeReceiver.update();
  m_alsDippedBeamStateReceiver.update();
  m_trailerSvsReceiver.update();
  m_ViewModeState_ReceiverPort.update();
  m_distanceToStop_ReceiverPort.update();
  m_stopLineLocation_ReceiverPort.update();
  m_automaticParking_ReceiverPort.update();
  m_piviManualVideoSetup_ReceiverPort.update();
  m_movementDirection_ReceiverPort.update();
  m_CcfReceiver.update();
  m_VehicleLightsReceiver.update();
  m_powerMode_ReceiverPort.update();
  m_FCTALeft_ReceiverPort.update();
  m_FCTARight_ReceiverPort.update();
  m_RCTALeft_ReceiverPort.update();
  m_RCTARight_ReceiverPort.update();
  m_VRSwitchSVM_ReceiverPort.update();
  m_FreeParkingParkable_ReceiverPort.update();
  m_FCP_SVMButtonPressed_ReceiverPort.update();
  m_NFSM_ViewBufferStatus_ReceiverPort.update();
  m_GbcVehicleTransparency_ReceiverPort.update();
  m_GbcWheelTransparency_ReceiverPort.update();
  m_ObjectOverlayReq_ReceiverPort.update();
  m_parkHmiParkingStatusReceiver.update();
  m_parkHmiParkingTypeReceiver.update();
  m_parkHmiParkParkngTypeSeldReceiver.update();
  m_parkHmiParkAPAPARKMODEReceiver.update();
  m_parkHmiParkingTypeVariantReceiver.update();
  m_parkHmiParkingTypeSelectedReceiver.update();
  m_AutoCamActivateButtonPressedStsReceiver.update();
  m_VehTransparenceStsFromSM_Receiver.update();
  m_VehTransparenceStsInternalReceiver.update();
  m_CpcOverlaySwitch_Receiver.update();
  m_parkHmiParkingModeReceiver.update();
  m_parkHmiParkDriverIndReceiver.update();
  m_parkHmiParkDriverIndExtReceiver.update();
  m_parkHmiParkingSideReceiver.update();
  m_parkHmiParkingFuncIndReceiver.update();
  m_parkHmiParkingQuitIndReceiver.update();
  m_parkHmiParkingQuitIndExtReceiver.update();
  m_parkHmiParkingRecoverIndReceiver.update();
  m_parkHmiParkingSpaceReceiver.update();
  m_parkHmiParkingReqReleaseBtnReceiver.update();
  m_parkHmiParkingFrontObjReceiver.update();
  m_parkEndPositionSearchingReceiver.update();
  m_parkFinalEndPositionReceiver.update();
  m_parkCurMoveTargetPositionReceiver.update();
  m_parkCarmoveNumberReceiver.update();
  m_parkCartravelDistDesiredReceiver.update();
  m_parkIsLastMoveReceiver.update();
  m_aebVmcOpMode_ReceiverPort.update();
  m_camCalibrationStatus_ReceiverPort.update();
  m_CpcNorminalCalibration_ReceiverPort.update();
  m_displayedView_ReceiverPort.update();
  m_freeModeSt_ReceiverPort.update();
  m_currentView_ReceiverPort.update();
  m_viewModeStatus_ReceiverPort.update();
  m_pasButtonStatus_ReceiverPort.update();
  m_parkConfirmButton_ReceiverPort.update();
  m_parkPauseButton_ReceiverPort.update();
  m_parkDirection_ReceiverPort.update();
  m_pasStatus_ReceiverPort.update();
  m_sdwStatus_ReceiverPort.update();
  m_sdwStatusF_ReceiverPort.update();
  m_sdwStatusFM_ReceiverPort.update();
  m_sdwStatusRM_ReceiverPort.update();
  m_sdwStatusR_ReceiverPort.update();
  m_parkingInSpotsReceiver.update();
  m_parkAPASlotsReceiver.update();
  m_parkAPASlotsMarkReceiver.update();
  m_parkPSDirectionSelectedReceiver.update();
  m_ParkDriverIndSearchReceiver.update();
  m_driverSteeringWheelAngleReceiver.update();
  m_customResetGuidanceVhmReceiver.update();
  m_customUltrasonicDataReceiver.update();
  m_vehiclePathAPGReceiver.update();
  m_DoorLockSts_ReceiverPort.update();
  m_TimeShow_ReceiverPort.update();
  m_tileSplineInterpolateArrayReceiver.update();
  m_solidBaseplateState_ReceiverPort.update();
  m_ParkDispLowPolyModelStsReceiver.update();

  // // BYD ValOut
  // m_SVSViewStateDaddy_Receiver.update();
  // m_SVSWorkModeDaddy_Receiver.update();
  m_SVSRotateStatusDaddy_Receiver.update();
  m_dayNightThemeDaddy_Receiver.update();
  // m_SVSExpandedViewStateDaddy_Receiver.update();
  // m_SVSNewExpandedViewStateDaddy_Receiver.update();
  m_HURemoteScreenReceiver.update();
  m_HUvehTrans5xReceiver.update();
  m_HURadarWallButtonReceiver.update();
  m_HUTrailerButtonReceiver.update();
  m_HUvehTransCCReceiver.update();
  m_HUvehTransLevelReceiver.update();
  m_HUfreemodeAngleAzimuthReceiver.update();
  m_HUfreemodeAngleElevationReceiver.update();
  //! HMI related
  m_hmiDataReceiver.update();
  m_HUCameraCommandDaddyReceiver.update();
  m_HUTouchTypeReceiver.update();
  m_basePlateReq_RecieverPort.update();
  m_vehTransReq_RecieverPort.update();
  m_HURotationPadReceiver.update();
  m_HUTwoFingerTouchTypeReceiver.update();
  //! pdm
  m_pdmDynOverlaySts_Receiver.update();
  m_pdmOverlayDistSts_Receiver.update();
  m_pdmBasePlateSts_Receiver.update();
  m_pdmVehTransSts_Receiver.update();
  m_vehColorSts_ReceiverPort.update();
  m_swVersionShowSwitch_ReceiverPort.update();
  m_swInfo_ReceiverPort.update();


  //! degradation
  m_degradationFid_ReceiverPort.update();

  m_impostorTranspReceiver.update();

  //! Freeparking
  m_freeparkingReceiver.update();
  m_freeparkingSocketReceiver.update();
  m_freeparkingRectInfoReceiver.update();
  m_freeparkingActiveReceiver.update();
  m_freeparkingConfirmButtonReceiver.update();
  m_freeparkingSpaceTypeReceiver.update();

  //! RPA
  m_parkHmiRPAAvailableReceiver.update();
  // cpc Extrinsic
  // m_cpcCornerDetection_Receiverport.update();

  // cpc status
  // m_cpcStatus_Receiverport.update();
  //! CPC
  m_cpcToCpcWrapper_ReceiverPort.update();
  m_cpcWrapperToCpc_ReceiverPort.update();
  m_cpcToSvsOverlay_ReceiverPort.update();
  m_svsOverlayToCpc_ReceiverPort.update();

  //! dynamic gear
  m_dynamicGearStatus_ReceiverPort.update();

  // Notice Rolling
  m_noticeRolling_ReceiverPort.update();
  m_PlanViewEnlargeStatusReceiver.update();
  m_sideViewEnableStatusReceiver.update();
  m_topViewEnableStatusReceiver.update();
  m_HUDislayModeSwitchDaddyReceiver.update();

  //! Brake Pedal
  m_parkHmiParkBreakPedalBeenReleasedBfReceiver.update();
  m_parkHmiParkParkbrkPedlAppldReceiver.update();

  //! Virtual Reality
  m_parkSlotReceiver.update();
  m_parkSlotRefinedReceiver.update();
  m_fusionObject_ReceiverPort.update();
  m_sitOcp_ReceiverPort.update();
  m_pedestrianObj_ReceiverPort.update();

  //! Animation
  m_animationDaddy_ReceiverPort.update();

  m_ParkhmiToSvs_ReceiverPort.update();
  m_RemoveDistortion_ReceiverPort.update();

  //! ZoomLevel
  m_ZoomLevel_ReceiverPort.update();
  m_BirdEyeViewSwitch_ReceiverPort.update();

  m_SurroundViewRotateAngle_ReceiverPort.update();
  m_CrabGuideline_ReceiverPort.update();
  m_GoldenEmblem_ReceiverPort.update();
}

// Clears the data inside the receivers
void CustomFramework::cleanupReceiverPorts()
{
  //! cleanup pc receiver ports first
  pc::core::Framework::cleanupReceiverPorts();

  m_socketCmdReceiver.cleanup();
  m_dayNightModeReceiver.cleanup();
  m_alsDippedBeamStateReceiver.cleanup();
  m_trailerSvsReceiver.cleanup();
  m_ViewModeState_ReceiverPort.cleanup();
  m_distanceToStop_ReceiverPort.cleanup();
  m_stopLineLocation_ReceiverPort.cleanup();
  m_automaticParking_ReceiverPort.cleanup();
  m_piviManualVideoSetup_ReceiverPort.cleanup();
  m_movementDirection_ReceiverPort.cleanup();
  m_CcfReceiver.cleanup();
  m_VehicleLightsReceiver.cleanup();
  m_powerMode_ReceiverPort.cleanup();
  m_FCTALeft_ReceiverPort.cleanup();
  m_FCTARight_ReceiverPort.cleanup();
  m_RCTALeft_ReceiverPort.cleanup();
  m_RCTARight_ReceiverPort.cleanup();
  m_VRSwitchSVM_ReceiverPort.cleanup();
  m_FreeParkingParkable_ReceiverPort.cleanup();
  m_FCP_SVMButtonPressed_ReceiverPort.cleanup();
  m_NFSM_ViewBufferStatus_ReceiverPort.cleanup();
  m_GbcVehicleTransparency_ReceiverPort.cleanup();
  m_GbcWheelTransparency_ReceiverPort.cleanup();
  m_ObjectOverlayReq_ReceiverPort.cleanup();
  m_parkHmiParkingStatusReceiver.cleanup();
  m_parkHmiParkingTypeReceiver.cleanup();
  m_parkHmiParkParkngTypeSeldReceiver.cleanup();
  m_parkHmiParkAPAPARKMODEReceiver.cleanup();
  m_parkHmiParkingTypeVariantReceiver.cleanup();
  m_parkHmiParkingTypeSelectedReceiver.cleanup();
  m_AutoCamActivateButtonPressedStsReceiver.cleanup();
  m_VehTransparenceStsFromSM_Receiver.cleanup();
  m_VehTransparenceStsInternalReceiver.cleanup();
  m_CpcOverlaySwitch_Receiver.cleanup();
  m_parkHmiParkingModeReceiver.cleanup();
  m_parkHmiParkDriverIndReceiver.cleanup();
  m_parkHmiParkDriverIndExtReceiver.cleanup();
  m_parkHmiParkingSideReceiver.cleanup();
  m_parkHmiParkingFuncIndReceiver.cleanup();
  m_parkHmiParkingQuitIndReceiver.cleanup();
  m_parkHmiParkingQuitIndExtReceiver.cleanup();
  m_parkHmiParkingRecoverIndReceiver.cleanup();
  m_parkHmiParkingSpaceReceiver.cleanup();
  m_parkHmiParkingReqReleaseBtnReceiver.cleanup();
  m_parkHmiParkingFrontObjReceiver.cleanup();
  m_parkEndPositionSearchingReceiver.cleanup();
  m_parkFinalEndPositionReceiver.cleanup();
  m_parkCurMoveTargetPositionReceiver.cleanup();
  m_parkCarmoveNumberReceiver.cleanup();
  m_parkCartravelDistDesiredReceiver.cleanup();
  m_parkIsLastMoveReceiver.cleanup();
  m_aebVmcOpMode_ReceiverPort.cleanup();
  m_camCalibrationStatus_ReceiverPort.cleanup();
  m_CpcNorminalCalibration_ReceiverPort.cleanup();
  m_displayedView_ReceiverPort.cleanup();
  m_freeModeSt_ReceiverPort.cleanup();
  m_currentView_ReceiverPort.cleanup();
  m_viewModeStatus_ReceiverPort.cleanup();
  m_pasButtonStatus_ReceiverPort.cleanup();
  m_parkConfirmButton_ReceiverPort.cleanup();
  m_parkPauseButton_ReceiverPort.cleanup();
  m_parkDirection_ReceiverPort.cleanup();
  m_pasStatus_ReceiverPort.cleanup();
  m_sdwStatus_ReceiverPort.cleanup();
  m_sdwStatusF_ReceiverPort.cleanup();
  m_vehiclePathAPGReceiver.cleanup();
  m_sdwStatusFM_ReceiverPort.cleanup();
  m_sdwStatusRM_ReceiverPort.cleanup();
  m_sdwStatusR_ReceiverPort.cleanup();
  m_parkingInSpotsReceiver.cleanup();
  m_parkAPASlotsReceiver.cleanup();
  m_parkAPASlotsMarkReceiver.cleanup();
  m_parkPSDirectionSelectedReceiver.cleanup();
  m_ParkDriverIndSearchReceiver.cleanup();
  m_driverSteeringWheelAngleReceiver.cleanup();
  m_customResetGuidanceVhmReceiver.cleanup();
  m_customUltrasonicDataReceiver.cleanup();
  m_DoorLockSts_ReceiverPort.cleanup();
  m_TimeShow_ReceiverPort.cleanup();
  m_tileSplineInterpolateArrayReceiver.cleanup();
  m_solidBaseplateState_ReceiverPort.cleanup();
  m_ParkDispLowPolyModelStsReceiver.cleanup();

  // // BYD ValOut
  // m_SVSViewStateDaddy_Receiver.cleanup();
  // m_SVSWorkModeDaddy_Receiver.cleanup();
  m_SVSRotateStatusDaddy_Receiver.cleanup();
  m_dayNightThemeDaddy_Receiver.cleanup();
  // m_SVSExpandedViewStateDaddy_Receiver.cleanup();
  // m_SVSNewExpandedViewStateDaddy_Receiver.cleanup();
  m_HURemoteScreenReceiver.cleanup();
  m_HUvehTrans5xReceiver.cleanup();
  m_HURadarWallButtonReceiver.cleanup();
  m_HUTrailerButtonReceiver.cleanup();
  m_HUvehTransCCReceiver.cleanup();
  m_HUvehTransLevelReceiver.cleanup();
  m_HUfreemodeAngleAzimuthReceiver.cleanup();
  m_HUfreemodeAngleElevationReceiver.cleanup();
  //! HMI related
  m_hmiDataReceiver.cleanup();
  m_HUCameraCommandDaddyReceiver.cleanup();
  m_HUTouchTypeReceiver.cleanup();
  m_basePlateReq_RecieverPort.cleanup();
  m_vehTransReq_RecieverPort.cleanup();
  m_HURotationPadReceiver.cleanup();
  m_HUTwoFingerTouchTypeReceiver.cleanup();

  //! pdm
  m_pdmDynOverlaySts_Receiver.cleanup();
  m_pdmOverlayDistSts_Receiver.cleanup();
  m_pdmBasePlateSts_Receiver.cleanup();
  m_pdmVehTransSts_Receiver.cleanup();
  m_vehColorSts_ReceiverPort.cleanup();
  m_swVersionShowSwitch_ReceiverPort.cleanup();
  m_swInfo_ReceiverPort.cleanup();

  //! degradation
  m_degradationFid_ReceiverPort.cleanup();

  m_impostorTranspReceiver.cleanup();

  //! Freeparking
  m_freeparkingReceiver.cleanup();
  m_freeparkingSocketReceiver.cleanup();
  m_freeparkingRectInfoReceiver.cleanup();
  m_freeparkingActiveReceiver.cleanup();
  m_freeparkingConfirmButtonReceiver.cleanup();
  m_freeparkingSpaceTypeReceiver.cleanup();

  //! RPA
  m_parkHmiRPAAvailableReceiver.cleanup();
  // cpc Extrinsic
  // m_cpcCornerDetection_Receiverport.cleanup();

  //cpc Status
  // m_cpcStatus_Receiverport.cleanup();
  //! CPC
  m_cpcToCpcWrapper_ReceiverPort.cleanup();
  m_cpcWrapperToCpc_ReceiverPort.cleanup();
  m_cpcToSvsOverlay_ReceiverPort.cleanup();
  m_svsOverlayToCpc_ReceiverPort.cleanup();

  //! dynamic gear
  m_dynamicGearStatus_ReceiverPort.cleanup();

  //Notice Rolling
  m_noticeRolling_ReceiverPort.cleanup();

  m_PlanViewEnlargeStatusReceiver.cleanup();
  m_sideViewEnableStatusReceiver.cleanup();
  m_topViewEnableStatusReceiver.cleanup();
  m_HUDislayModeSwitchDaddyReceiver.cleanup();

  //! Brake Pedal
  m_parkHmiParkBreakPedalBeenReleasedBfReceiver.cleanup();
  m_parkHmiParkParkbrkPedlAppldReceiver.cleanup();

  //! Virtual Reality
  m_parkSlotReceiver.cleanup();
  m_parkSlotRefinedReceiver.cleanup();
  m_fusionObject_ReceiverPort.cleanup();
  m_sitOcp_ReceiverPort.cleanup();
  m_pedestrianObj_ReceiverPort.cleanup();

  //! Animation
  m_animationDaddy_ReceiverPort.cleanup();

  m_ParkhmiToSvs_ReceiverPort.cleanup();
  m_RemoveDistortion_ReceiverPort.cleanup();

  //! ZoomLevel
  m_ZoomLevel_ReceiverPort.cleanup();
  m_BirdEyeViewSwitch_ReceiverPort.cleanup();

  m_SurroundViewRotateAngle_ReceiverPort.cleanup();
  m_CrabGuideline_ReceiverPort.cleanup();
  m_GoldenEmblem_ReceiverPort.cleanup();
}

} // namespace core
} // namespace cc

