//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD ST/HC23
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: XC-DX/EPF2-SVS
//  Department: XC-DX/EPF2
//=============================================================================
/// @swcomponent SVS BYD
/// @file  CustomScene.cpp
/// @brief
//=============================================================================

#include "osgDB/ReadFile"
#include "osg/PositionAttitudeTransform"
#include "osg/Scissor"

#include "cc/core/inc/CustomScene.h"

#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/assets/vehiclemodel/inc/VehicleModel.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/SystemConf.h"
#include "pc/svs/core/inc/View.h"
#include "pc/svs/factory/inc/RenderManager.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/views/fusiview/inc/FuSiView.h"
#include "pc/svs/views/perfview/inc/ProfilingView.h"
#include "pc/svs/views/rawfisheyeview/inc/RawFisheyeView.h"
#include "pc/svs/virtcam/inc/CameraSteeringUpdateCallback.h"
#include "pc/svs/worker/bowlshaping/inc/BowlShaperTask.h"
#include "pc/svs/worker/bowlshaping/inc/PolarBowlLayoutGenerator.h"
#include "pc/svs/worker/core/inc/TaskManager.h"
#include <cassert>

#include "cc/assets/augmentedview/inc/AugmentedViewTransition.h"
#include "cc/assets/augmentedview/inc/ParkViewRenderManager.h"
#include "cc/assets/common/inc/Bowl.h"
#include "cc/assets/common/inc/Floor.h"
#include "cc/assets/trajectory/inc/MainLogic.h"
#include "cc/factory/inc/CustomRenderManager.h"
// #include "cc/assets/common/inc/FloorHistoryTextureOnly.h"
#include "cc/assets/common/inc/FloorSingleCam.h"
#include "cc/assets/common/inc/SingleCam.h"
#include "cc/assets/common/inc/Vehicle.h"
#include "cc/assets/common/inc/Vehicle2D.h"
#include "cc/assets/e3parkingoverlay/inc/e3parkingoverlay.h"
#include "cc/assets/fisheyeassets/inc/FisheyeTrajectories.h"
#include "cc/assets/impostor/inc/FixedImpostor.h"
#include "cc/assets/pdc/inc/PdcOverlay.h"
#include "cc/assets/pdc/inc/PdcUpdateVisitor.h"
#include "cc/assets/splineoverlay/inc/SplineOverlay.h"
#include "cc/assets/streetoverlay/inc/StreetOverlay.h"
#include "cc/assets/tileoverlay/inc/TileOverlay.h"
#include "cc/assets/trajectory/inc/ExpModeTrajectory.h"
#include "cc/assets/trajectory/inc/TrajectoryAssets.h"
// #include "cc/assets/caliboverlay/inc/CalibOverlay.h"
#include "cc/assets/tileoverlay/inc/TileCallback.h"
// #include "cc/assets/rctaoverlay/inc/RCTAOverlay.h"
// #include "cc/assets/debugoverlay/inc/SwInfoOverlay.h"
#include "cc/assets/debugoverlay/inc/TimeShowOverlay.h"
// #include "cc/assets/drivablepath/inc/DrivablePathManager.h"
#include "cc/assets/splineoverlay/inc/DistanceDigitalDisplay.h"
#include "cc/assets/splineoverlay/inc/DistanceOverlay.h"
#include "cc/assets/stb/inc/SeeThroughBonnet.h"
#include "cc/assets/trajectory/inc/ArrowTrajectory.h"
#include "cc/assets/uielements/inc/DynamicDistance.h"
// #include "cc/assets/uielements/inc/SpeedOverlay.h"
#include "cc/assets/uielements/inc/TurnArroundOverlay.h"
// #include "cc/assets/uielements/inc/HorizontalMoveOverlay.h"
#include "cc/views/bonnetview/inc/BonnetView.h"
#include "cc/views/combinedview/inc/CombinedView.h"
#include "cc/views/customrawfisheyeview/inc/CustomRawFisheyeView.h"
#include "cc/views/customwarpfisheyeview/inc/CustomWarpFisheyeView.h"
#include "cc/views/daynightview/inc/DayNightView.h"
#include "cc/views/nfsengineeringview/inc/CpcEngineeringScreen.h"
#include "cc/views/nfsengineeringview/inc/NfsEngineeringView.h"
#include "cc/views/panoramaview/inc/PanoramaView.h"
#include "cc/views/parkview/inc/ParkView.h"
// #include "cc/views/planview/inc/FixedImpostorPlanView.h"
#include "cc/views/surroundview/inc/SurroundView.h"

#include "cc/virtcam/inc/CameraPositions.h"
#include "cc/virtcam/inc/HeadUnitHemisphereCameraUpdater.h"

#include "cc/assets/common/inc/CustomRenderManager.h"
#include "cc/assets/ptsoverlay/inc/PtsOverlay.h"

#include "cc/daddy/inc/CustomDaddyPorts.h"

#include "cc/assets/overlaycallback/inc/OverlayCallback.h"

#include "cc/assets/ECALprogressoverlay/inc/ECALprogressoverlay.h"
#include "cc/assets/button/inc/ContinueButton.h"
#include "cc/assets/button/inc/FreeparkingButton.h"
#include "cc/assets/button/inc/ParkoutButton.h"
#include "cc/assets/button/inc/QuitButton.h"
#include "cc/assets/button/inc/StartPauseConfirmButton.h"
// #include "cc/assets/dynamicgearoverlay/inc/dynamicgearoverlay.h"
#include "cc/assets/fisheyetransition/inc/FisheyeTransitionOverlay.h"
#include "cc/assets/freeparkingoverlay/inc/FreeparkingManager.h"
#include "cc/assets/parkingslot/inc/ParkingSlotCornersManager.h"
#include "cc/assets/parkingslot/inc/ParkingSlotManager.h"
#include "cc/assets/parkingspace/inc/ParkingSpace.h"
#include "cc/assets/parkingspace/inc/ParkingSpaceMark.h"
#include "cc/assets/parkingspots/inc/ParkingSpotManager.h"
#include "cc/assets/rimprotectionline/inc/RimProtectionLine.h"
#include "cc/assets/uielements/inc/AVMBackground.h"
#include "cc/assets/uielements/inc/ApaCornerIcon.h"
#include "cc/assets/uielements/inc/ParkingConfirmInterface.h"
#include "cc/assets/uielements/inc/ParkingSearching.h"
#include "cc/assets/uielements/inc/ParkingSlot.h"
#include "cc/assets/uielements/inc/ParkingView.h"
#include "cc/assets/uielements/inc/TextSymbols.h"
#include "cc/assets/uielements/inc/Vehicle2DOverlay.h"
#include "cc/assets/uielements/inc/WarnSymbols.h"
#include "cc/assets/uielements/inc/WheelSeparator.h"
#include "cc/assets/vehicle2dwheels/inc/Vehicle2DWheels.h"
#include "cc/assets/vehicle2dwheels/inc/Vehicle2DCrabWheels.h"
// #include "cc/assets/virtualreality/inc/VirtualRealityManager.h"
#include "cc/assets/dynamicwheelmask/inc/DynamicWheelMask.h"
#include "cc/assets/parkingslot/inc/ParkingSlotCornersPlanView.h"

#include "cc/assets/corner/inc/ViewportCorner.h"
#include "cc/views/planview/inc/PlanViewEnlargeCallback.h"

#include "cc/imgui/inc/imgui_view.h"

#include "cc/views/customwarpfisheyeview/inc/FisheyeEnableCallback.h"
#include "cc/views/callback/inc/TrailerTrajectoryUpdateCallback.h"

#include "cc/virtcam/inc/VirtualCameraUpdater.h"

#include "CustomSystemConf.h" //target+cc specific

#ifdef CHAMAELEON
#include "pc/svs/imp/chamaeleon/inc/chamaeleon.hpp"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_tabs.hpp"
#include "pc/svs/imp/cwd/inc/custom_window_dump.hpp"
#include "pc/svs/imp/iq/inc/image_quality_root.hpp"
#include "pc/svs/imp/sh/inc/sharpness_harmonization.hpp"
#include "pc/svs/imp/tnf/inc/temporal_noise_filter_root.hpp"
#endif

#include "cc/assets/superTransparentOverlay/inc/SuperTransparentOverlay.h"

using pc::util::logging::g_EngineContext;

#define USE_FISHEYE_VIEWS 1
#define USE_FISHEYE_VIEWS_SIDE 1
#define USE_RESIZE_2DVEHICLEMODEL_PLANVIEW 1
#define USE_PARKSPACE_MARK 0
// #define USE_TRADITIONAL_BASEPLATE 1
#ifdef TARGET_ANDROID
#define USE_VIRTUAL_OBJECT_SEARCHING
#endif
namespace cc
{

namespace virtcam
{
pc::util::coding::Item<cc::virtcam::CarCenter> g_carCenter("CarCenter");
} // namespace virtcam

namespace core
{

pc::util::coding::Item<CustomViews> g_views("Views");
pc::util::coding::Item<CustomSceneSetting> g_customerScene("CustomScene");

using cc::assets::trajectory::g_DIDescriptor;
using cc::assets::trajectory::g_trajParams;
using cc::views::planview::g_planView;

using cc::assets::trajectory::g_pDL1;
using cc::assets::trajectory::g_leftOutermostLine;
using cc::assets::trajectory::g_rightOutermostLine;
using cc::assets::trajectory::g_leftOutermostLineColorful;
using cc::assets::trajectory::g_rightOutermostLineColorful;

using cc::assets::trajectory::g_height;
using cc::assets::trajectory::g_numVerticesWheelTracks;
using cc::assets::trajectory::g_numVerticesCoverPlate;
using cc::assets::trajectory::g_numLayoutPointsDL1;
using cc::assets::trajectory::g_numVerticesTrailerAssistLines;

// static const cc::assets::trajectory::DL1*           g_pDL1;
// static const cc::assets::trajectory::OutermostLine* g_leftOutermostLine;
// static const cc::assets::trajectory::OutermostLine* g_rightOutermostLine;
// static const cc::assets::trajectory::OutermostLine* g_leftOutermostLineColorful;
// static const cc::assets::trajectory::OutermostLine* g_rightOutermostLineColorful;

// const vfc::float32_t g_height                        = 0.005f;
// const vfc::uint32_t  g_numVerticesWheelTracks        = 48u;
// const vfc::uint32_t  g_numVerticesCoverPlate         = 48u;
// const vfc::uint32_t  g_numLayoutPointsDL1            = 48u;
// const vfc::uint32_t  g_numVerticesTrailerAssistLines = 48u;
// const vfc::float32_t g_height                        = 0.005f;
// const vfc::uint32_t  g_numVerticesWheelTracks        = 48u;
// const vfc::uint32_t  g_numVerticesCoverPlate         = 48u;
// const vfc::uint32_t  g_numLayoutPointsDL1            = 48u;
// const vfc::uint32_t  g_numVerticesTrailerAssistLines = 48u;

//! Viewports are defined for LHD per default. This function checks if they need to be mirrored for RHD vehicles and
//! performs it.
static pc::core::Viewport
getCorrectDriveHandSideViewport(const pc::core::Viewport& f_viewport, const pc::core::Viewport& f_usableCanvasViewport)
{
    const bool         lhd = pc::vehicle::g_mechanicalData->m_leftHandDrive;
    pc::core::Viewport l_out(f_viewport);

    if (true == lhd)
    {
        return l_out;
    }

    // otherwise it is a rhd...
    l_out.m_origin.x() = f_usableCanvasViewport.m_size.x() - f_viewport.m_size.x() - f_viewport.m_origin.x();

    return l_out;
}

//! Viewports are defined for LHD per default. This function checks if they need to be mirrored for RHD vehicles and
//! performs it (just for DA views).
static pc::core::Viewport
getCorrectDriveHandSideViewportDA(const pc::core::Viewport& f_viewport, vfc::float32_t f_origin)
{
    const bool         lhd = pc::vehicle::g_mechanicalData->m_leftHandDrive;
    pc::core::Viewport l_out(f_viewport);

    if (true == lhd)
    {
        return l_out;
    }

    // otherwise it is a rhd...

    l_out.m_origin.x() = static_cast<vfc::int32_t>(f_origin); // PRQA S 3016

    return l_out;
}

//!
//! CustomScene
//!
CustomScene::CustomScene(bool f_enableImgui)
    : pc::core::Scene(CustomViews::SURROUND_VIEW) // PRQA S 4050
    , m_pBowlAsset(nullptr)
    , m_pFloorAsset(nullptr)
    , m_augmentedViewTransitionHori(nullptr)
    , m_augmentedViewTransitionVert(nullptr)
    , m_vehicleModelName("")
    , m_enableImgui(f_enableImgui)
{
}

const std::string CustomScene::getVehicleModelName() const
{
    return m_vehicleModelName;
}

void CustomScene::setVehicleModelName(const std::string& f_nodeName)
{
    m_vehicleModelName = f_nodeName;
}

#if USE_FISHEYE_VIEWS

// Horizontal Fisheye Settings
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_rearAssistSettings("RearFisheyeAssist");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_frontAssistSettings("FrontFisheyeAssist");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_rearSettings("RearFisheye");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_frontSettings("FrontFisheye");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_frontSettingsUndistored("FrontFisheyeUndistored");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_rearSettingsUndistored("RearFisheyeUndistored");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_remoteFrontSettings("RemoteFrontFisheye");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_remoteRearSettings("RemoteRearFisheye");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_remoteLeftSettings("RemoteLeftFisheye");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_remoteRightSettings("RemoteRightFisheye");
// pc::util::coding::Item<pc::views::warpfisheye::FisheyeModelSettings> l_rearModelSettings("RearFisheyeModel");
// pc::util::coding::Item<pc::views::warpfisheye::FisheyeModelSettings> l_frontModelSettings("FrontFisheyeModel");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_leftSettings("LeftFisheye");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_rightSettings("RightFisheye");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_singleRearLeftSettings("SingleRearLeftFisheye");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_singleRearRightSettings("SingleRearRightFisheye");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_leftSettings5x("LeftFisheye5x");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_rightSettings5x("RightFisheye5x");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_leftRearSettings5x("LeftRearFisheye5x");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_rightRearSettings5x("RightRearFisheye5x");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_rearLeftSettings("RearLeftFisheye");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_rearRightSettings("RearRightFisheye");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_leftWheelSettings("LeftFisheyeWheel");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_rightWheelSettings("RightFisheyeWheel");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_leftRearWheelSettings("LeftRearFisheyeWheel");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_rightRearWheelSettings("RightRearFisheyeWheel");

static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings>
    l_partUnfishFrontAssist("PartialUnfishFrontAssist");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings>
    l_partUnfishRearAssist("PartialUnfishRearAssist");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishFront("PartialUnfishFront");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishRear("PartialUnfishRear");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishLeft("PartialUnfishLeft");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishRight("PartialUnfishRight");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishSingleRearLeft("PartialUnfishSingleRearLeft");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishSingleRearRight("PartialUnfishSingleRearRight");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishLeft5x("PartialUnfishLeft5x");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishLeftWheel("PartialUnfishLeftWheel");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishRightWheel("PartialUnfishRightWheel");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishLeftRearWheel("PartialUnfishLeftRearWheel");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishRightRearWheel("PartialUnfishRightRearWheel");

static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings>
    l_partUnfishRight5x("PartialUnfishRight5x");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings>
    l_partUnfishRearLeft5x("PartialUnfishRearLeft5x");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings>
    l_partUnfishRearRight5x("PartialUnfishRearRight5x");

static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings>
    l_partUnfishRemoteFront("PartialUnfishRemoteFront");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings>
    l_partUnfishRemoteRear("PartialUnfishRemoteRear");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings>
    l_partUnfishRemoteLeft("PartialUnfishRemoteLeft");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings>
    l_partUnfishRemoteRight("PartialUnfishRemoteRight");

// Horizontal Fisheye junction view settings
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_rearPanoSettings("RearPanoFisheye");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_frontPanoSettings("FrontPanoFisheye");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings>
    l_partUnfishFrontPano("PartialUnfishFrontPano");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings>
    l_partUnfishRearPano("PartialUnfishRearPano");

#if ENABLE_VERTICAL_MODE
// Vertical Fisheye settings
namespace
{
pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>   l_vertRearSettings("VertRearFisheye");
pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>   l_vertFrontSettings("VertFrontFisheye");
pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>   l_vertLeftSettings("VertLeftFisheye");
pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>   l_vertRightSettings("VertRightFisheye");
pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishFrontVert("PartialUnfishFrontVert");
pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishRearVert("PartialUnfishRearVert");
pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishLeftVert("PartialUnfishLeftVert");
pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishRightVert("PartialUnfishRightVert");

pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_vertRearSettings5x("VertRearFisheye5x");
pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_vertFrontSettings5x("VertFrontFisheye5x");
pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_vertLeftSettings5x("VertLeftFisheye5x");
pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_vertRightSettings5x("VertRightFisheye5x");
pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings>
    l_partUnfishFrontVert5x("PartialUnfishFrontVert5x");
pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishRearVert5x("PartialUnfishRearVert5x");
pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishLeftVert5x("PartialUnfishLeftVert5x");
pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings>
    l_partUnfishRightVert5x("PartialUnfishRightVert5x");
pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_frontCropBoundsVert5x("FisheyeCropSettingsFrontVert5x");
pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rearCropBoundsVert5x("FisheyeCropSettingsRearVert5x");
pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_leftCropBoundsVert5x("FisheyeCropSettingsLeftViewVert5x");
pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rightCropBoundsVert5x("FisheyeCropSettingRightViewVert5x");

// Vertical Fisheye junction view settings
pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_vertRearPanoSettings("VertRearPanoFisheye");
pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_vertFrontPanoSettings("VertFrontPanoFisheye");
pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings>
    l_partUnfishFrontPanoVert("PartialUnfishFrontPanoVert");
pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings>
    l_partUnfishRearPanoVert("PartialUnfishRearPanoVert");

// Vertical fisheye view crop setting
pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_frontCropBoundsVert("FisheyeCropSettingsFrontVert");
pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rearCropBoundsVert("FisheyeCropSettingsRearVert");
pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_frontPanoCropBoundsVert("FisheyeCropSettingsFrontPanoVert");
pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rearPanoCropBoundsVert("FisheyeCropSettingsRearPanoVert");
pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_leftCropBoundsVert("FisheyeCropSettingsLeftViewVert");
pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rightCropBoundsVert("FisheyeCropSettingRightViewVert");
} // namespace
#endif

// Horizontal fisheye view crop setting
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_frontAssistCropBoundsHori("FisheyeCropSettingsFrontAssistView");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rearAssistCropBoundsHori("FisheyeCropSettingsRearAssistView");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_frontCropBoundsHori("FisheyeCropSettingsFrontView");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_frontCropBoundsHoriUndistorted("FisheyeCropSettingsFrontViewUndistorted");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rearCropBoundsHori("FisheyeCropSettingsRearView");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rearCropBoundsHoriUndistorted("FisheyeCropSettingsRearViewUndistorted");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_frontPanoCropBoundsHori("FisheyeCropSettingsFrontPano");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rearPanoCropBoundsHori("FisheyeCropSettingsRearPano");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_leftCropBoundsHori("FisheyeCropSettingsLeftView");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rightCropBoundsHori("FisheyeCropSettingRightView");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_singleRearLeftCropBoundsHori("FisheyeCropSettingsSingleRearLeftView");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_singleRearRightCropBoundsHori("FisheyeCropSettingSingleRearRightView");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_leftCropBoundsHori5x("FisheyeCropSettingsLeftView5x");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rightCropBoundsHori5x("FisheyeCropSettingRightView5x");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_leftRearCropBoundsHori5x("FisheyeCropSettingsLeftRearView5x");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rightRearCropBoundsHori5x("FisheyeCropSettingRightRearView5x");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_frontRemoteCropBoundsHori("FisheyeCropSettingsRemoteFrontView");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rearRemoteCropBoundsHori("FisheyeCropSettingsRemoteRearView");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_remoteLeftCropBoundsHori("FisheyeCropSettingsRemoteLeftView");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_remoteRightCropBoundsHori("FisheyeCropSettingRemoteRightView");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_leftCropBoundsHoriWheel("FisheyeCropSettingsLeftViewWheel");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rightCropBoundsHoriWheel("FisheyeCropSettingRightViewWheel");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_leftRearCropBoundsHoriWheel("FisheyeCropSettingsLeftRearViewWheel");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rightRearCropBoundsHoriWheel("FisheyeCropSettingRightRearViewWheel");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_leftRearCropBoundsHoriWheelBottom("FisheyeCropSettingsLeftRearViewWheelBottom");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rightRearCropBoundsHoriWheelBottom("FisheyeCropSettingRightRearViewWheelBottom");

#endif

void CustomScene::init() // PRQA S 6044
{
    if (g_dataContainerToSvs.m_vehicleInfo.vehicleType.empty())
    {
        g_dataContainerToSvs.m_vehicleInfo.vehicleType = "sghl";
    }
    // initialize base class
    Scene::init();

    // pf code. #code looks fine
    XLOG_INFO(g_EngineContext, "Beginning of Custom Scene initialization"); // PRQA S 4060

    pc::factory::RenderManagerRegistry* const l_pRenderManagerRegistry =
        new pc::factory::RenderManagerRegistry(getFramework());
    addUpdateCallback(l_pRenderManagerRegistry);

    const auto l_renderManagerDefault= new pc::factory::RenderManager(
        l_pRenderManagerRegistry, virtcam::VCAM_FRONT_VIEW,
        rbp::vis::imp::chamaeleon::EChamaeleonView::NO_CHAMAELEON);
    l_pRenderManagerRegistry->setDefaultRenderManager(l_renderManagerDefault);

    const auto l_pLayoutGenerator = createSuperEllipseLayoutGenerator();
#if USE_RADAR_WALL
    const osg::ref_ptr<cc::core::CustomZoneLayout> l_zoneLayout = new cc::core::CustomZoneLayout(true);
#endif

    //! Assets
    //! **********************************************************************************************************
    const auto l_pFrontWheels  = new pc::core::Asset(AssetId::EASSETS_FRONT_WHEELS);
    const auto l_pAllWheels    = new pc::core::Asset(AssetId::EASSETS_ALL_WHEELS);
    const auto l_pVehicleDoors = new assets::impostor::ImpostorDoors(AssetId::EASSETS_VEHICLE_DOORS, getFramework());
    const auto l_pVehicle      = new assets::common::Vehicle(
        AssetId::EASSETS_VEHICLE, getFramework(), l_pFrontWheels, l_pAllWheels, l_pVehicleDoors);
    const auto l_pBowl = new assets::common::Bowl(AssetId::EASSETS_BOWL, getFramework(), l_pLayoutGenerator);
    // auto l_pVehicle2D       = new assets::common::Vehicle2D(AssetId::EASSETS_TV2D_IMPOSTOR, getFramework());
#ifdef USE_TRADITIONAL_BASEPLATE
    auto l_pFloor = new assets::common::FloorHistoryTextureOnly(
        AssetId::EASSETS_FLOOR, getFramework(), l_pLayoutGenerator->getPolarLayout());
#else
    const auto l_pFloor =
        new assets::common::Floor(AssetId::EASSETS_FLOOR, getFramework(), l_pLayoutGenerator->getPolarLayout(), true);
    const auto l_dynWheelMaskAsset = new cc::assets::DynWheelMaskAsset(AssetId::EASSETS_DYNAMIC_WHEEL_MASK, getFramework());
    l_pFloor->addDynamicWheelMaskDependency(l_dynWheelMaskAsset);
#endif
    const auto l_pFloorFront = new assets::common::Floor(
        AssetId::EASSETS_FLOOR, getFramework(), l_pLayoutGenerator->getPolarLayout(), cc::assets::common::FLOOR_FRONT);
    const auto l_pFloorRear = new assets::common::Floor(
        AssetId::EASSETS_FLOOR, getFramework(), l_pLayoutGenerator->getPolarLayout(), cc::assets::common::FLOOR_REAR);
    const auto l_pSingleCamFront = new assets::common::SingleCam(
        AssetId::EASSETS_BOWL_FRONT_CAM,
        l_pBowl->getSV3DNode(),
        l_pFloorFront->getSV3DNode(),
        pc::factory::SINGLE_CAM_FRONT);

    const auto l_pSingleCamLeft = new assets::common::SingleCam(
        AssetId::EASSETS_BOWL_LEFT_CAM, l_pBowl->getSV3DNode(), l_pFloor->getSV3DNode(), pc::factory::SINGLE_CAM_LEFT);
    const auto l_pSingleCamRight = new assets::common::SingleCam(
        AssetId::EASSETS_BOWL_RIGHT_CAM,
        l_pBowl->getSV3DNode(),
        l_pFloor->getSV3DNode(),
        pc::factory::SINGLE_CAM_RIGHT);
    const auto l_pSingleCamRear = new assets::common::SingleCam(
        AssetId::EASSETS_BOWL_REAR_CAM,
        l_pBowl->getSV3DNode(),
        l_pFloorRear->getSV3DNode(),
        pc::factory::SINGLE_CAM_REAR);

    // auto l_pCalibOverlay    = new pc::core::Asset( AssetId::EASSETS_CALIB_OVERLAY, new
    // assets::caliboverlay::CalibOverlay(getFramework())); auto l_pBackground      = new pc::core::Asset(
    // AssetId::EASSETS_BACKGROUND, new assets::Background());
    // auto l_pRCTAOverlay     = new pc::core::Asset( AssetId::EASSETS_RCTA_OVERLAY, new
    // assets::rctaoverlay::RctaOverlay(getFramework()));
    // auto l_pDigitalDisplay = new pc::core::Asset(
    //     AssetId::EASSETS_DIGITAL_DISTANCE_DISPLAY,
    //     new assets::distancedigitaldisplay::DistanceDigitalDisplay(getFramework()));
#if USE_RADAR_WALL
#else
    auto l_pObstacleOverlay_vehOffset = new pc::core::Asset(
        AssetId::EASSETS_OBSTACLE_OVERLAY_VEH_OFFSET,
        new assets::tileoverlay::TileOverlay(getFramework(), true, false));
    auto l_pObstacleOverlay_Perspective = new pc::core::Asset(
        AssetId::EASSETS_OBSTACLE_OVERLAY_VEH_OFFSET,
        new assets::tileoverlay::TileOverlay(getFramework(), false, true));
#endif
    // const auto l_pSwInfoOverlay =
    //     new pc::core::Asset(AssetId::EASSETS_SWINFO_OVERLAY, new assets::swinfooverlay::SwInfoOverlay(getFramework()));
    // const auto l_pSpeedOverlay =
    //     new pc::core::Asset(AssetId::EASSETS_SPEED_OVERLAY, new assets::uielements::SpeedOverlay(getFramework()));
    // const auto l_pDynamicGearOverlays = new pc::core::Asset(
    //     AssetId::EASSETS_DYNAMIC_GEAR_OVERLAYS,
    //     new assets::dynamicgearoverlay::DynamicGearOverlays(getFramework()->asCustomFramework()));
//    const auto l_pDynamicDistance = new pc::core::Asset(
//        AssetId::EASSETS_DYNAMIC_DISTANCE_OVERLAYS, new assets::uielements::DynamicDistance(getFramework()));
    // auto l_pDynamicGearOverlays = new pc::core::Asset( AssetId::EASSETS_DYNAMIC_GEAR_OVERLAYS,
    //                      new assets::dynamicgearoverlay::DynamicGearOverlays(getFramework()->asCustomFramework()),
    //                      AssetId::EASSETS_DYNAMIC_GEAR_OVERLAYS);

//    const auto l_pArrowTrajectory = new pc::core::Asset(
//        AssetId::EASSETS_DRIVABLE_PATH, new assets::trajectory::ArrowTrajectory(getFramework(), g_trajParams, false));
    // auto l_pArrowTrajectoryAVM = new pc::core::Asset( AssetId:: EASSETS_DRIVABLE_PATH_AVM, new
    // assets::trajectory::ArrowTrajectory(getFramework(), g_trajParams, true)); auto l_pLowpolyVehicle = new
    // pc::core::Asset( AssetId:: EASSETS_LOWPOLY_VEHICLE_MODEL, new
    // assets::lowpolyvehicle::LowpolyVehicle(getFramework()->asCustomFramework()));
#if USE_RADAR_WALL
#else
    l_pObstacleOverlay_vehOffset->setCullCallback(
        new cc::assets::tileoverlay::TileCallback(getFramework(), false, false));
    l_pObstacleOverlay_Perspective->setCullCallback(
        new cc::assets::tileoverlay::TileCallback(getFramework(), false, true));
#endif
    constexpr pc::core::Asset* l_rimProtectionLine = nullptr;
    if ("sghz" == g_dataContainerToSvs.m_vehicleInfo.vehicleType)
    {
        // disable rim protection
    }
    else
    {
        // enable rim protection
        // l_rimProtectionLine = new pc::core::Asset(
        //                     AssetId::EASSETS_RIM_PROTECTION_LINE,
        //                     new cc::assets::rimline::RimProtectionLine(getFramework()->asCustomFramework()));
    }

    m_pBowlAsset  = l_pBowl;
    m_pFloorAsset = l_pFloor;

#ifndef USE_TRADITIONAL_BASEPLATE
    const auto l_pFloorPlate = l_pFloor->getBasePlateAsset();
#endif
    //! Trajectories ***********************************************************************************************
    // const cc::assets::trajectory::TrajectoryCodingParams& l_trajCodingParams =
    // cc::assets::trajectory::g_trajCodingParams; assets::trajectory::TrajectoryParams_st l_trajParams;
    // assets::trajectory::DIDescriptor_st l_DIDescriptor;
    cc::assets::trajectory::initTrajectoryParams(&g_trajParams, &g_DIDescriptor, false);

    const auto l_pTopViewOverlayCullCallback = new assets::overlaycallback::TopViewOverlayCullCallback(
        getFramework(), assets::overlaycallback::VEHICLE_DRIVING_TUBE);

    const auto l_pOutermostLines = new assets::trajectory::OutermostLinesAsset(
        AssetId::EASSETS_TRAJECTORY_OUTERMOST_LINES, getFramework(), g_trajParams, g_DIDescriptor);
    l_pOutermostLines->addCullCallback(l_pTopViewOverlayCullCallback);

    g_leftOutermostLine  = l_pOutermostLines->getLeft();
    g_rightOutermostLine = l_pOutermostLines->getRight();

    const auto l_pOutermostLinesColorful = new assets::trajectory::OutermostLinesAssetColorful(
        AssetId::EASSETS_TRAJECTORY_OUTERMOST_LINES_COLORFUL, getFramework(), g_trajParams, g_DIDescriptor);
    l_pOutermostLinesColorful->addCullCallback(l_pTopViewOverlayCullCallback);

    // g_leftOutermostLineColorful  = l_pOutermostLinesColorful->getLeft();
    // g_rightOutermostLineColorful = l_pOutermostLinesColorful->getRight();
    // g_leftOutermostLineColorful  = l_pOutermostLines->getLeft();
    // g_rightOutermostLineColorful = l_pOutermostLines->getRight();

    const auto l_pDL1 = new assets::trajectory::DistanceLineAsset(
        AssetId::EASSETS_TRAJECTORY_DL1,
        getFramework(),
        g_trajParams,
        l_pOutermostLines->getLeft(),
        l_pOutermostLines->getRight(),
        g_numLayoutPointsDL1);
    l_pDL1->addCullCallback(l_pTopViewOverlayCullCallback);
    g_pDL1 = l_pDL1->getDistanceLine();

    const auto l_pDL1Colorful = new assets::trajectory::DistanceLineAsset(
        AssetId::EASSETS_TRAJECTORY_DL1,
        getFramework(),
        g_trajParams,
        l_pOutermostLinesColorful->getLeft(),
        l_pOutermostLinesColorful->getRight(),
        g_numLayoutPointsDL1);
    l_pDL1Colorful->addCullCallback(l_pTopViewOverlayCullCallback);

    const auto l_pWheelTracks = new assets::trajectory::WheelTracksAsset(
        AssetId::EASSETS_TRAJECTORY_WHEEL_TRACKS, getFramework(), g_trajParams, l_pDL1->getDistanceLine());
    l_pWheelTracks->addCullCallback(l_pTopViewOverlayCullCallback);

    const auto l_pWheelTracksColorful = new assets::trajectory::WheelTracksAsset(
        AssetId::EASSETS_TRAJECTORY_WHEEL_TRACKS, getFramework(), g_trajParams, l_pDL1Colorful->getDistanceLine());
    l_pWheelTracksColorful->addCullCallback(l_pTopViewOverlayCullCallback);

    const auto l_pCoverPlate = new assets::trajectory::CoverPlateAsset(
        AssetId::EASSETS_TRAJECTORY_COVERPLATE, getFramework(), g_trajParams, g_numVerticesCoverPlate);
    l_pCoverPlate->addCullCallback(l_pTopViewOverlayCullCallback);

    const auto l_pTrailerHitchTrajectory = new assets::trajectory::TrailerHitchTrajectoryAsset(
        AssetId::EASSETS_TRAJECTORY_TRAILER_TRAJECTORY, getFramework(), g_trajParams);
    l_pTrailerHitchTrajectory->addCullCallback(l_pTopViewOverlayCullCallback);

    // if (l_trajCodingParams.m_refLineVisible)
    // assets::trajectory::RefLineAsset* l_pRefLine = nullptr;
    // {
    //   l_pRefLine = new assets::trajectory::RefLineAsset(AssetId::EASSETS_TRAJECTORY_REFLINE, getFramework(),
    //   l_trajParams);
    // }

    // Hori screen mode
    //! Plan view parameters
    //! **********************************************************************************************
    const vfc::float32_t l_total_width_meters  = g_planView->m_widthMeters;
    const vfc::float32_t l_total_length_meters = static_cast<vfc::float32_t>(g_views->m_planViewport.m_size.y()) *
                                                 g_planView->m_widthMeters /
                                                 static_cast<vfc::float32_t>(g_views->m_planViewport.m_size.x());

    // This FOV parameter is set to enlarge the vehicle 2D model
    // x/y  = l_total_length_meters_parking_vert/l_total_width_meters_parking_vert
    const vfc::float32_t l_total_width_meters_vehicle2d =
        g_planView->m_widthMeters - g_planView->m_widthMetersVeh2dDiff;
    const vfc::float32_t l_total_length_meters_vehicle2d =
        static_cast<vfc::float32_t>(g_views->m_planViewport.m_size.y()) * l_total_width_meters_vehicle2d /
        static_cast<vfc::float32_t>(g_views->m_planViewport.m_size.x());

    const vfc::float32_t l_total_width_meters_parking_hori = g_planView->m_widthMetersParkingHori;
    const vfc::float32_t l_total_length_meters_parking_hori =
        static_cast<vfc::float32_t>(g_views->m_apaParkingPlanViewport.m_size.y()) *
        g_planView->m_widthMetersParkingHori /
        static_cast<vfc::float32_t>(g_views->m_apaParkingPlanViewport.m_size.x());

    const vfc::float32_t l_total_width_meters_parking_hori_vehicle2d =
        g_planView->m_widthMetersParkingHori - g_planView->m_widthMetersVeh2dDiff;
    const vfc::float32_t l_total_length_meters_parking_hori_vehicle2d =
        static_cast<vfc::float32_t>(g_views->m_apaParkingPlanViewport.m_size.y()) *
        g_planView->m_widthMetersParkingHori /
        static_cast<vfc::float32_t>(g_views->m_apaParkingPlanViewport.m_size.x());

    // svs full plan view
    const vfc::float32_t l_total_width_meters_full = g_planView->m_widthMetersFullscreen;
    const vfc::float32_t l_total_length_meters_full =
        static_cast<vfc::float32_t>(g_views->m_usableCanvasViewport.m_size.y()) * g_planView->m_widthMetersFullscreen /
        static_cast<vfc::float32_t>(g_views->m_usableCanvasViewport.m_size.x());

    const vfc::float32_t l_total_width_meters_vehicle2d_fullscreen =
        g_planView->m_widthMetersFullscreen - g_planView->m_widthMetersVeh2dFullscreenDiff;
    const vfc::float32_t l_total_length_meters_vehicle2d_fullscreen =
        static_cast<vfc::float32_t>(g_views->m_usableCanvasViewport.m_size.y()) *
        l_total_width_meters_vehicle2d_fullscreen /
        static_cast<vfc::float32_t>(g_views->m_usableCanvasViewport.m_size.x());

    // image in image
    const vfc::float32_t l_total_width_meters_imageInImage = g_planView->m_widthMeters;
    const vfc::float32_t l_total_length_meters_imageInImage =
        static_cast<vfc::float32_t>(g_views->m_imageInimagePlanviewViewport.m_size.y()) * g_planView->m_widthMeters /
        static_cast<vfc::float32_t>(g_views->m_imageInimagePlanviewViewport.m_size.x());

    const vfc::float32_t l_total_width_meters_vehicle2d_imageInImage =
        g_planView->m_widthMeters - g_planView->m_widthMetersVeh2dDiff;
    const vfc::float32_t l_total_length_meters_vehicle2d_imageInImage =
        static_cast<vfc::float32_t>(g_views->m_imageInimagePlanviewViewport.m_size.y()) *
        l_total_width_meters_vehicle2d_imageInImage /
        static_cast<vfc::float32_t>(g_views->m_imageInimagePlanviewViewport.m_size.x());

    // image in image park top
    const vfc::float32_t l_total_width_meters_imageInImage_parktop = g_planView->m_widthMetersPipParkTop;
    const vfc::float32_t l_total_length_meters_imageInImage_parktop =
        static_cast<vfc::float32_t>(g_views->m_parkTop.m_size.y()) * g_planView->m_widthMetersPipParkTop /
        static_cast<vfc::float32_t>(g_views->m_parkTop.m_size.x());

    const vfc::float32_t l_total_width_meters_vehicle2d_imageInImage_parktop =
        g_planView->m_widthMetersPipParkTop - g_planView->m_widthMetersVeh2dDiff;
    const vfc::float32_t l_total_length_meters_vehicle2d_imageInImage_parktop =
        static_cast<vfc::float32_t>(g_views->m_parkTop.m_size.y()) *
        l_total_width_meters_vehicle2d_imageInImage_parktop /
        static_cast<vfc::float32_t>(g_views->m_parkTop.m_size.x());

    // remote
    const vfc::float32_t l_total_width_meters_remote = g_planView->m_widthMeters;
    const vfc::float32_t l_total_length_meters_remote =
        static_cast<vfc::float32_t>(g_views->m_remotePlanViewport.m_size.y()) * g_planView->m_widthMeters /
        static_cast<vfc::float32_t>(g_views->m_remotePlanViewport.m_size.x());

    const vfc::float32_t l_total_width_meters_vehicle2d_remote =
        g_planView->m_widthMeters - g_planView->m_widthMetersVeh2dDiff;
    const vfc::float32_t l_total_length_meters_vehicle2d_remote =
        static_cast<vfc::float32_t>(g_views->m_remotePlanViewport.m_size.y()) * l_total_width_meters_vehicle2d_remote /
        static_cast<vfc::float32_t>(g_views->m_remotePlanViewport.m_size.x());

    // bumper
    const vfc::float32_t l_total_width_meters_bumper = g_planView->m_widthMetersBumper;
    const vfc::float32_t l_total_length_meters_bumper =
        static_cast<vfc::float32_t>(g_views->m_mainViewport.m_size.y()) * l_total_width_meters_bumper /
        static_cast<vfc::float32_t>(g_views->m_mainViewport.m_size.x());

    // planetry view
    const vfc::float32_t l_total_width_meters_planetary = g_planView->m_widthMetersPlanetry;
    const vfc::float32_t l_total_length_meters_planetary =
        static_cast<vfc::float32_t>(g_views->m_usableCanvasViewport.m_size.y()) * l_total_width_meters_planetary /
        static_cast<vfc::float32_t>(g_views->m_usableCanvasViewport.m_size.x());

#if ENABLE_VERTICAL_MODE
    // y/x = l_total_width_meters_parking_vert/l_total_length_meters_parking_vert
    // For Vert mode, svs vert plan view
    const vfc::float32_t l_total_width_meters_vert = g_planView->m_widthMetersVert;
    const vfc::float32_t l_total_length_meters_vert =
        static_cast<vfc::float32_t>(g_views->m_vertPlanViewport.m_size.x()) * l_total_width_meters_vert /
        static_cast<vfc::float32_t>(g_views->m_vertPlanViewport.m_size.y());

    // For Vert mode, vehicle 2D need to enlarge
    const vfc::float32_t l_total_width_meters_vehicle2d_vert =
        g_planView->m_widthMetersVert - g_planView->m_widthMetersVeh2dDiffVert;
    const vfc::float32_t l_total_length_meters_vehicle2d_vert =
        static_cast<vfc::float32_t>(g_views->m_vertPlanViewport.m_size.x()) * l_total_width_meters_vehicle2d_vert /
        static_cast<vfc::float32_t>(g_views->m_vertPlanViewport.m_size.y());

    // Vert mode, svs full plan view
    const vfc::float32_t l_total_width_meters_full_vert = g_planView->m_widthMetersFullscreenVert;
    const vfc::float32_t l_total_length_meters_full_vert =
        static_cast<vfc::float32_t>(g_views->m_vertFullScreenViewport.m_size.x()) * l_total_width_meters_full_vert /
        static_cast<vfc::float32_t>(g_views->m_vertFullScreenViewport.m_size.y());

    const vfc::float32_t l_total_width_meters_vehicle2d_fullscreen_vert =
        g_planView->m_widthMetersFullscreenVert - g_planView->m_widthMetersVeh2dFullscreenDiffVert;
    const vfc::float32_t l_total_length_meters_vehicle2d_fullscreen_vert =
        static_cast<vfc::float32_t>(g_views->m_vertFullScreenViewport.m_size.x()) *
        l_total_width_meters_vehicle2d_fullscreen_vert /
        static_cast<vfc::float32_t>(g_views->m_vertFullScreenViewport.m_size.y());

    // For Vert mode, fov no different between plan view and parking plan view
    const vfc::float32_t l_total_width_meters_parking_vert = g_planView->m_widthMetersParkingVert;
    const vfc::float32_t l_total_length_meters_parking_vert =
        static_cast<vfc::float32_t>(g_views->m_vertParkingPlanViewport.m_size.x()) * l_total_width_meters_parking_vert /
        static_cast<vfc::float32_t>(g_views->m_vertParkingPlanViewport.m_size.y());

    const vfc::float32_t l_total_width_meters_parking_vert_vehicle2d =
        g_planView->m_widthMetersParkingVert - g_planView->m_widthMetersVeh2dDiffParkingVert;
    const vfc::float32_t l_total_length_meters_parking_vert_vehicle2d =
        static_cast<vfc::float32_t>(g_views->m_vertParkingPlanViewport.m_size.x()) *
        l_total_width_meters_parking_vert_vehicle2d /
        static_cast<vfc::float32_t>(g_views->m_vertParkingPlanViewport.m_size.y());

#endif
    // osg::Vec2f l_viewSize(l_total_width_meters, l_total_length_meters);
    // osg::Vec3f l_cameraPosition(virtcam::g_positions->getPosition(virtcam::VCAM_PLAN_VIEW).m_eye);

    // ! elements in searching perspective view
//    const auto l_pParkingSearchingElements = new pc::core::Asset(
//        AssetId::EASSETS_UI_PARKING_SEARCHING,
//        new assets::uielements::ParkingSearching(
//            getFramework()->asCustomFramework(), AssetId::EASSETS_UI_PARKING_SEARCHING));
    // // ! quit button
    // auto l_pParkTypeConfirm = new pc::core::Asset( AssetId::EASSETS_PARKINGTYPECONFIRM,
    //                       new assets::parkingtypeconfirm::ParkingTypeConfirm(getFramework()->asCustomFramework(),
    //                       EASSETS_PARKINGTYPECONFIRM));

    // ! park confirm interface
    // auto l_pParkConfirmInterface = new pc::core::Asset( AssetId::EASSETS_PARKINGCONFIRMINTERFACE,
    //                             new
    //                             assets::parkconfirminterface::ParkingConfirmInterface(getFramework()->asCustomFramework(),
    //                             AssetId::EASSETS_PARKINGCONFIRMINTERFACE));

    //! Wheel separator
    const auto l_pWheelSeparator_Horizontal = new pc::core::Asset(
        AssetId::EASSETS_UI_WHEELSEPARATOR_HORIZONTAL,
        new assets::uielements::wheelseparatorhorizontal::WheelSeparatorHorizontal(
            getFramework()->asCustomFramework(), AssetId::EASSETS_UI_WHEELSEPARATOR_HORIZONTAL));
#if ENABLE_VERTICAL_MODE
    auto l_pWheelSeparator_Vertical = new pc::core::Asset(
        AssetId::EASSETS_UI_WHEELSEPARATOR_VERTICAL,
        new assets::uielements::wheelseparatorvertical::WheelSeparatorVertical(
            getFramework()->asCustomFramework(), AssetId::EASSETS_UI_WHEELSEPARATOR_VERTICAL));
#endif
    //! text symbol
    // auto l_pTextSymbol = new pc::core::Asset( AssetId::EASSETS_UI_WARNSYMBOL_TEXT,
    //                               new assets::uielements::TextSymbols(getFramework()->asCustomFramework(),
    //                               AssetId::EASSETS_UI_WARNSYMBOL_TEXT));

    //! apa corner icon
    // auto l_apaCornerIcons = new pc::core::Asset( AssetId::EASSETS_APA_CORNER_ICONS,
    //                               new assets::uielements::ApaCornerIcon(getFramework()->asCustomFramework(),
    //                               AssetId::EASSETS_APA_CORNER_ICONS));

    //! avm background icon
    // auto l_avmBackgroundIcons = new pc::core::Asset( AssetId::EASSETS_AVM_BACKGROUND_ICONS,
    //                               new assets::uielements::AvmBackgroundIcon(getFramework()->asCustomFramework(),
    //                               AssetId::EASSETS_AVM_BACKGROUND_ICONS));

    //! parking plan view icon symbol
    // auto l_pParkingPlanSymbol = new pc::core::Asset( AssetId::EASSETS_UI_PARKINGPLANICON,  //PRQA S 4208
    // new assets::parkingspace::ParkingSpaceMarkSymbols(getFramework()->asCustomFramework(),
    // AssetId::EASSETS_UI_PARKINGPLANICON));

    //! Views
    //! ***********************************************************************************************************

#if USE_RADAR_WALL
    //! PTS
    // PTS overlay assets
    const auto l_ptsOverlay      = new assets::ptsoverlay::PtsOverlay(getFramework(), l_zoneLayout.get());
    const auto l_obstacleOverlay = new pc::core::Asset(AssetId::EASSETS_OBSTACLE_OVERLAY, l_ptsOverlay);
    const auto l_ptsTransform    = new assets::ptsoverlay::PtsMatrixTransform(getFramework());
    l_ptsTransform->addChild(l_ptsOverlay); // PRQA S 3803

    // auto l_obstacleOverlayParking = new pc::core::Asset( AssetId::EASSETS_OBSTACLE_OVERLAY, l_ptsTransform);
    // auto l_miniPts = new cc::assets::ptsoverlay::MiniPts(AssetId::MiniTopView,
    // static_cast<pc::vehiclemodel::VehicleModel*> (l_vehicle->getAsset()), getFramework());
#endif

   // PDC overlay *****************************************************************************************************
    const auto l_pdcUpdateVisitor = cc::assets::pdc::PdcUpdateVisitor::create(*l_zoneLayout, getFramework());
    const auto l_pdcUpdateCallback = new cc::assets::pdc::PdcUpdateCallback(l_pdcUpdateVisitor);

    const auto l_pdcOverlayGeode = new cc::assets::pdc::PdcOverlay();
    l_pdcOverlayGeode->addUpdateCallback(l_pdcUpdateCallback);

    const auto l_pdcOverlay = new cc::assets::pdc::PdcAsset(AssetId::EASSETS_PDCOVERLAY, l_pdcOverlayGeode);
    l_pdcOverlayGeode->setName("PdcOverlay");

    // rear view
    // ********************************************************************************************************
    osg::Matrixf l_mirrorTheWorld;
    l_mirrorTheWorld(0, 0) = -1.f;
    pc::views::warpfisheye::PartialUnfishModel* const l_pRearModel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRear.data());

    pc::views::warpfisheye::PartialUnfishModel* const l_pRearModelUndistored =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRearPano.data());

    cc::views::warpfisheye::CustomWarpFisheyeView* const l_rearView =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "Rear View Undistored",
            getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::REAR_CAMERA,
            l_pRearModelUndistored,
            l_rearSettingsUndistored.get(),
            l_rearCropBoundsHoriUndistorted.get());

    pc::core::Asset* const l_pRearWheelTracksUndistorted = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_WHEELTRACKS,
        cc::assets::fisheyeassets::createFisheyeWheelTracks(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModelUndistored, l_rearSettingsUndistored));

    // l_pRearWheelTracks->setCullCallback(new cc::assets::overlaycallback::OverlayCallback(getFramework(),
    // cc::assets::overlaycallback::VEHICLE_WHEEL_TRACK));

    pc::core::Asset* const l_pRearOutermostLinesUndistorted = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_OUTERMOST_LINES_COLORFUL,
        cc::assets::fisheyeassets::createFisheyeOutermostLinesColorful(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModelUndistored, l_rearSettingsUndistored));
    // l_pRearOutermostLines->setCullCallback(new cc::assets::overlaycallback::OverlayCallback(getFramework(),
    // cc::assets::overlaycallback::VEHICLE_WHEEL_TRACK));

    pc::core::Asset* const l_pRearCoverPlateUndistorted = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_COVERPLATE,
        cc::assets::fisheyeassets::createFisheyeCoverPlate(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModelUndistored, l_rearSettingsUndistored));


    pc::core::Asset* const l_pRearDL1Undistorted = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_DL1_COLORFUL,
        cc::assets::fisheyeassets::createFisheyeDL1Colorful(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModelUndistored, l_rearSettingsUndistored));


    pc::core::Asset* const l_pRearTrailerAssistLineUndistorted = new pc::core::Asset(
        AssetId::EASSETS_TRAJECTORY_TRAILER_TRAJECTORY,
        cc::assets::fisheyeassets::createFisheyeTrailerHitchTrajectory(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModelUndistored, l_rearSettingsUndistored));

    l_rearView->addAsset(l_pRearWheelTracksUndistorted);
    l_rearView->addAsset(l_pRearOutermostLinesUndistorted);
    l_rearView->addAsset(l_pRearTrailerAssistLineUndistorted);
    l_rearView->addAsset(l_pRearDL1Undistorted);
    l_rearView->addAsset(l_pRearCoverPlateUndistorted);

    // mirror the view
    l_rearView->setProjectionMatrix(l_rearView->getProjectionMatrix() * l_mirrorTheWorld);
    l_rearView->addUpdateCallback(new cc::views::callback::TrailerTrajectoryUpdateCallback{getFramework()});

    cc::views::warpfisheye::CustomWarpFisheyeView* const l_rearPanoramaView =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "Rear View Distored",
            getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::REAR_CAMERA,
            l_pRearModel,
            l_rearSettings.get(),
            l_rearCropBoundsHori.get());

    // mirror the view
    l_rearPanoramaView->setProjectionMatrix(l_rearPanoramaView->getProjectionMatrix() * l_mirrorTheWorld);
    l_rearPanoramaView->addUpdateCallback(new cc::views::callback::TrailerTrajectoryUpdateCallback{getFramework()});

    const auto l_pTimeshowOverlay =
        new pc::core::Asset(AssetId::EASSETS_TIMESHOW_OVERLAY, new assets::timeshowoverlay::TimeShowOverlay(getFramework()));

    pc::core::Asset* const l_pRearWheelTracks = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_WHEELTRACKS,
        cc::assets::fisheyeassets::createFisheyeWheelTracks(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModel, l_rearSettings));

    // l_pRearWheelTracks->setCullCallback(new cc::assets::overlaycallback::OverlayCallback(getFramework(),
    // cc::assets::overlaycallback::VEHICLE_WHEEL_TRACK));

    pc::core::Asset* const l_pRearOutermostLines = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_OUTERMOST_LINES_COLORFUL,
        cc::assets::fisheyeassets::createFisheyeOutermostLinesColorful(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModel, l_rearSettings));
    // l_pRearOutermostLines->setCullCallback(new cc::assets::overlaycallback::OverlayCallback(getFramework(),
    // cc::assets::overlaycallback::VEHICLE_WHEEL_TRACK));

    pc::core::Asset* const l_pRearCoverPlate = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_COVERPLATE,
        cc::assets::fisheyeassets::createFisheyeCoverPlate(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModel, l_rearSettings));


    pc::core::Asset* const l_pRearDL1 = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_DL1_COLORFUL,
        cc::assets::fisheyeassets::createFisheyeDL1Colorful(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModel, l_rearSettings));


    pc::core::Asset* const l_pRearTrailerAssistLine = new pc::core::Asset(
        AssetId::EASSETS_TRAJECTORY_TRAILER_TRAJECTORY,
        cc::assets::fisheyeassets::createFisheyeTrailerHitchTrajectory(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModel, l_rearSettings));

    l_rearPanoramaView->addAsset(l_pRearWheelTracks);
    l_rearPanoramaView->addAsset(l_pRearOutermostLines);
    l_rearPanoramaView->addAsset(l_pRearDL1);
    l_rearPanoramaView->addAsset(l_pRearCoverPlate);
    l_rearPanoramaView->addAsset(l_pRearTrailerAssistLine);


    // Remote Rear View
    pc::views::warpfisheye::PartialUnfishModel* const l_pRearRemoteModel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRemoteRear.data());

    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pRemoteRearView =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "Remote Rear View",
            getCorrectDriveHandSideViewport(g_views->m_remoteMainViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::REAR_CAMERA,
            l_pRearRemoteModel,
            l_remoteRearSettings.get(),
            l_rearRemoteCropBoundsHori.get());

    l_pRemoteRearView->setProjectionMatrix(l_pRemoteRearView->getProjectionMatrix() * l_mirrorTheWorld);
    l_pRemoteRearView->addAsset(l_pRearCoverPlate);
    l_pRemoteRearView->addAsset(l_pTimeshowOverlay);
    // l_pRemoteRearView->addAsset(l_pRearRctaOverlay);

#if ENABLE_VERTICAL_MODE
    // Vertical mode Rear
    // ------------------------------------------------------------------------------------------------- Rotate 90
    // degrees----------------------------------------------------------------------------------------------------
    pc::views::warpfisheye::PartialUnfishModel* l_pRearModelVert =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRearVert.data());

    cc::views::warpfisheye::CustomWarpFisheyeView* l_pVertRearView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "Vert Rear View",
        getCorrectDriveHandSideViewport(g_views->m_vertMainViewport, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::REAR_CAMERA,
        l_pRearModelVert,
        l_vertRearSettings.get(),
        l_rearCropBoundsVert.get());

    // mirror the view
    l_pVertRearView->setProjectionMatrix(l_pVertRearView->getProjectionMatrix() * l_mirrorTheWorld);

    pc::core::Asset* l_pVertRearWheelTracks = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_WHEELTRACKS,
        cc::assets::fisheyeassets::createFisheyeWheelTracks(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModel, l_vertRearSettings));

    pc::core::Asset* l_pVertRearOutermostLines = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_OUTERMOST_LINES_COLORFUL,
        cc::assets::fisheyeassets::createFisheyeOutermostLinesColorful(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModel, l_vertRearSettings));

    pc::core::Asset* l_pVertRearCoverPlate = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_COVERPLATE,
        cc::assets::fisheyeassets::createFisheyeCoverPlate(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModel, l_vertRearSettings));

    pc::core::Asset* l_pVertRearDL1 = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_DL1_COLORFUL,
        cc::assets::fisheyeassets::createFisheyeDL1Colorful(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModel, l_vertRearSettings));

    l_pVertRearView->addAsset(l_pVertRearWheelTracks);
    l_pVertRearView->addAsset(l_pVertRearOutermostLines);
    l_pVertRearView->addAsset(l_pVertRearCoverPlate);
    l_pVertRearView->addAsset(l_pVertRearDL1);

    pc::views::warpfisheye::PartialUnfishModel* l_pRearModelVert5x =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRearVert5x.data());
    cc::views::warpfisheye::CustomWarpFisheyeView* l_pVertSideRearView =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "Vert Side Rear View",
            getCorrectDriveHandSideViewport(g_views->m_vertSideViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::REAR_CAMERA,
            l_pRearModelVert5x,
            l_vertRearSettings5x.get(),
            l_rearCropBoundsVert5x.get());
    // mirror the view
    l_pVertSideRearView->setProjectionMatrix(l_pVertSideRearView->getProjectionMatrix() * l_mirrorTheWorld);

    pc::core::Asset* l_pVertRearWheelTracks5x = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_WHEELTRACKS,
        cc::assets::fisheyeassets::createFisheyeWheelTracks(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::REAR_CAMERA,
            l_pRearModelVert5x,
            l_vertRearSettings5x));

    pc::core::Asset* l_pVertRearOutermostLines5x = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_OUTERMOST_LINES_COLORFUL,
        cc::assets::fisheyeassets::createFisheyeOutermostLinesColorful(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::REAR_CAMERA,
            l_pRearModelVert5x,
            l_vertRearSettings5x));

    pc::core::Asset* l_pVertRearCoverPlate5x = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_COVERPLATE,
        cc::assets::fisheyeassets::createFisheyeCoverPlate(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::REAR_CAMERA,
            l_pRearModelVert5x,
            l_vertRearSettings5x));

    pc::core::Asset* l_pVertRearDL15x = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_DL1_COLORFUL,
        cc::assets::fisheyeassets::createFisheyeDL1Colorful(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::REAR_CAMERA,
            l_pRearModelVert5x,
            l_vertRearSettings5x));

    l_pVertSideRearView->addAsset(l_pVertRearWheelTracks5x);
    l_pVertSideRearView->addAsset(l_pVertRearOutermostLines5x);
    l_pVertSideRearView->addAsset(l_pVertRearCoverPlate5x);
    l_pVertSideRearView->addAsset(l_pVertRearDL15x);

#endif




    // front view
    // *******************************************************************************************************

    pc::views::warpfisheye::PartialUnfishModel* const l_pFrontModelUndistored =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishFrontPano.data());

    pc::views::warpfisheye::PartialUnfishModel* const l_pFrontModel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishFront.data());

    cc::views::warpfisheye::CustomWarpFisheyeView* const l_frontPanoramaView =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "Front View Distored",
            getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pFrontModel,
            l_frontSettings.get(),
            l_frontCropBoundsHori.get());

    l_frontPanoramaView->setEnabledSteering(true);

    pc::core::Asset* const l_pFrontWheelTracks = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_WHEELTRACKS,
        cc::assets::fisheyeassets::createFisheyeWheelTracks(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModel, l_frontSettings));

    pc::core::Asset* const l_pFrontOutermostLines = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_OUTERMOST_LINES,
        cc::assets::fisheyeassets::createFisheyeOutermostLines(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModel, l_frontSettings));

    pc::core::Asset* const l_pFrontCoverPlate = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_COVERPLATE,
        cc::assets::fisheyeassets::createFisheyeCoverPlate(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModel, l_frontSettings));

    pc::core::Asset* const l_pFrontDL1 = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_DL1,
        cc::assets::fisheyeassets::createFisheyeDL1(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModel, l_frontSettings));

    l_frontPanoramaView->addAsset(l_pFrontWheelTracks);
    l_frontPanoramaView->addAsset(l_pFrontOutermostLines);
    l_frontPanoramaView->addAsset(l_pFrontCoverPlate);
    l_frontPanoramaView->addAsset(l_pFrontDL1);


    pc::core::Asset* const l_pFrontWheelTracksUndistored = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_WHEELTRACKS,
        cc::assets::fisheyeassets::createFisheyeWheelTracks(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModelUndistored, l_frontSettingsUndistored));

    pc::core::Asset* const l_pFrontOutermostLinesUndistored = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_OUTERMOST_LINES,
        cc::assets::fisheyeassets::createFisheyeOutermostLines(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModelUndistored, l_frontSettingsUndistored));

    pc::core::Asset* const l_pFrontCoverPlateUndistored = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_COVERPLATE,
        cc::assets::fisheyeassets::createFisheyeCoverPlate(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModelUndistored, l_frontSettingsUndistored));

    pc::core::Asset* const l_pFrontDL1Undistored = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_DL1,
        cc::assets::fisheyeassets::createFisheyeDL1(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModelUndistored, l_frontSettingsUndistored));


    //Without distortion
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_frontView =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "Front View Undistored",
            getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pFrontModelUndistored,
            l_frontSettingsUndistored.get(),
            l_frontCropBoundsHoriUndistorted.get());

    l_frontView->setEnabledSteering(true);

    l_frontView->addAsset(l_pFrontWheelTracksUndistored);
    l_frontView->addAsset(l_pFrontOutermostLinesUndistored);
    l_frontView->addAsset(l_pFrontCoverPlateUndistored);
    l_frontView->addAsset(l_pFrontDL1Undistored);

    // Remote Front View
    pc::views::warpfisheye::PartialUnfishModel* const l_pRemoteFrontModel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRemoteFront.data());

    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pRemoteFrontView =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "Remote Front View",
            getCorrectDriveHandSideViewport(g_views->m_remoteMainViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pRemoteFrontModel,
            l_remoteFrontSettings.get(),
            l_frontRemoteCropBoundsHori.get());
    l_pRemoteFrontView->addAsset(l_pFrontCoverPlate);
    l_pRemoteFrontView->addAsset(l_pTimeshowOverlay);
#if ENABLE_VERTICAL_MODE
    // Vertical mode Front
    // ------------------------------------------------------------------------------------------------- Rotate 90
    // degrees----------------------------------------------------------------------------------------------------
    pc::views::warpfisheye::PartialUnfishModel* l_pFrontModelVert =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishFrontVert.data());

    cc::views::warpfisheye::CustomWarpFisheyeView* l_pVertFrontView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        // pc::views::warpfisheye::WarpFisheyeView* l_pVertFrontView = new pc::views::warpfisheye::WarpFisheyeView(
        "Vert Front View",
        getCorrectDriveHandSideViewport(g_views->m_vertMainViewport, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::FRONT_CAMERA,
        l_pFrontModelVert,
        l_vertFrontSettings.get(),
        l_frontCropBoundsVert.get());

    pc::core::Asset* l_pVertFrontWheelTracks = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_WHEELTRACKS,
        cc::assets::fisheyeassets::createFisheyeWheelTracks(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModel, l_vertFrontSettings));

    pc::core::Asset* l_pVertFrontOutermostLines = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_OUTERMOST_LINES,
        cc::assets::fisheyeassets::createFisheyeOutermostLines(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModel, l_vertFrontSettings));

    pc::core::Asset* l_pVertFrontCoverPlate = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_COVERPLATE,
        cc::assets::fisheyeassets::createFisheyeCoverPlate(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModel, l_vertFrontSettings));

    pc::core::Asset* l_pVertFrontDL1 = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_DL1,
        cc::assets::fisheyeassets::createFisheyeDL1(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModel, l_vertFrontSettings));

    l_pVertFrontView->addAsset(l_pVertFrontOutermostLines);
    l_pVertFrontView->addAsset(l_pVertFrontWheelTracks);
    l_pVertFrontView->addAsset(l_pVertFrontCoverPlate);
    l_pVertFrontView->addAsset(l_pVertFrontDL1);

    pc::views::warpfisheye::PartialUnfishModel* l_pFrontModelVert5x =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishFrontVert5x.data());
    cc::views::warpfisheye::CustomWarpFisheyeView* l_pVertSideFrontView =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            // pc::views::warpfisheye::WarpFisheyeView* l_pVertFrontView = new pc::views::warpfisheye::WarpFisheyeView(
            "Vert Side Front View",
            getCorrectDriveHandSideViewport(g_views->m_vertSideViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pFrontModelVert5x,
            l_vertFrontSettings5x.get(),
            l_frontCropBoundsVert5x.get());

    pc::core::Asset* l_pVertFrontWheelTracks5x = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_WHEELTRACKS,
        cc::assets::fisheyeassets::createFisheyeWheelTracks(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pFrontModelVert5x,
            l_vertFrontSettings5x));

    pc::core::Asset* l_pVertFrontOutermostLines5x = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_OUTERMOST_LINES,
        cc::assets::fisheyeassets::createFisheyeOutermostLines(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pFrontModelVert5x,
            l_vertFrontSettings5x));

    pc::core::Asset* l_pVertFrontCoverPlate5x = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_COVERPLATE,
        cc::assets::fisheyeassets::createFisheyeCoverPlate(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pFrontModelVert5x,
            l_vertFrontSettings5x));

    pc::core::Asset* l_pVertFrontDL15x = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_DL1,
        cc::assets::fisheyeassets::createFisheyeDL1(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pFrontModelVert5x,
            l_vertFrontSettings5x));
    l_pVertSideFrontView->addAsset(l_pVertFrontOutermostLines5x);
    l_pVertSideFrontView->addAsset(l_pVertFrontWheelTracks5x);
    l_pVertSideFrontView->addAsset(l_pVertFrontCoverPlate5x);
    l_pVertSideFrontView->addAsset(l_pVertFrontDL15x);
#endif

    // Using Assist View for four view mode in ST24 --
    // ********************************************************************************************************
    pc::views::warpfisheye::PartialUnfishModel* const l_pAssistFrontModel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishFrontAssist.data());
    pc::views::warpfisheye::PartialUnfishModel* const l_pAssistRearModel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRearAssist.data());

    pc::core::Asset* const l_pAssistFrontWheelTracks = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_WHEELTRACKS,
        cc::assets::fisheyeassets::createFisheyeWheelTracks(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pAssistFrontModel,
            l_frontAssistSettings));

    pc::core::Asset* const l_pAssistFrontOutermostLines = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_OUTERMOST_LINES,
        cc::assets::fisheyeassets::createFisheyeOutermostLines(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pAssistFrontModel,
            l_frontAssistSettings));

    pc::core::Asset* const l_pAssistFrontCoverPlate = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_COVERPLATE,
        cc::assets::fisheyeassets::createFisheyeCoverPlate(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pAssistFrontModel,
            l_frontAssistSettings));
    pc::core::Asset* const l_pAssistFrontDL1 = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_DL1,
        cc::assets::fisheyeassets::createFisheyeDL1(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pAssistFrontModel,
            l_frontAssistSettings));

    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pAssitFrontView =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "Assist Front View",
            getCorrectDriveHandSideViewport(g_views->m_apaAssistViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pAssistFrontModel,
            l_frontAssistSettings.get(),
            l_frontAssistCropBoundsHori.get());

    l_pAssitFrontView->addAsset(l_pAssistFrontWheelTracks);
    l_pAssitFrontView->addAsset(l_pAssistFrontOutermostLines);
    l_pAssitFrontView->addAsset(l_pAssistFrontCoverPlate);
    l_pAssitFrontView->addAsset(l_pAssistFrontDL1);

    // auto l_frontPanoramaViewCallback = new cc::views::warpfisheye::FisheyeEnableCallback(
    //     getFramework()->asCustomFramework(), cc::views::warpfisheye::ECameraIndex::Front_CAMERA_ENABLE);
    // l_pAssitFrontView->addCullCallback(l_frontPanoramaViewCallback);

    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pAssitRearView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "Assist Rear View",
        getCorrectDriveHandSideViewport(g_views->m_apaAssistViewport, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::REAR_CAMERA,
        l_pAssistRearModel,
        l_rearAssistSettings.get(),
        l_rearAssistCropBoundsHori.get());

    pc::core::Asset* const l_pAssistRearWheelTracks = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_WHEELTRACKS,
        cc::assets::fisheyeassets::createFisheyeWheelTracks(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::REAR_CAMERA,
            l_pAssistRearModel,
            l_rearAssistSettings));

    pc::core::Asset* const l_pAssistRearOutermostLines = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_OUTERMOST_LINES_COLORFUL,
        cc::assets::fisheyeassets::createFisheyeOutermostLinesColorful(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::REAR_CAMERA,
            l_pAssistRearModel,
            l_rearAssistSettings));

    pc::core::Asset* const l_pAssistRearCoverPlate = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_COVERPLATE,
        cc::assets::fisheyeassets::createFisheyeCoverPlate(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::REAR_CAMERA,
            l_pAssistRearModel,
            l_rearAssistSettings));

    pc::core::Asset* const l_pAssistRearDL1 = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_DL1_COLORFUL,
        cc::assets::fisheyeassets::createFisheyeDL1Colorful(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::REAR_CAMERA,
            l_pAssistRearModel,
            l_rearAssistSettings));
    // mirror the view
    l_pAssitRearView->setProjectionMatrix(l_pAssitRearView->getProjectionMatrix() * l_mirrorTheWorld);

    l_pAssitRearView->addAsset(l_pAssistRearWheelTracks);
    l_pAssitRearView->addAsset(l_pAssistRearOutermostLines);
    l_pAssitRearView->addAsset(l_pAssistRearCoverPlate);
    l_pAssitRearView->addAsset(l_pAssistRearDL1);

    // auto l_rearPanoramaViewCallback = new cc::views::warpfisheye::FisheyeEnableCallback(
    //     getFramework()->asCustomFramework(), cc::views::warpfisheye::ECameraIndex::Rear_CAMERA_ENABLE);
    // l_pAssitRearView->addCullCallback(l_rearPanoramaViewCallback);

    // //plan (top) view
    // **************************************************************************************************
    // //Realize the Plan View as an ortho camera

    // //! Vehicle impostor - needs the total dimensions
    // assets::impostor::FixedImpostor* l_pTV2D_CarImpostor = new assets::impostor::FixedImpostor(
    //     AssetId::EASSETS_TV2D_IMPOSTOR, l_pVehicle,
    //     static_cast<pc::vehiclemodel::VehicleModel*> (l_pVehicle->getAsset()),
    //     assets:: impostor::g_settings->m_textureWidth, assets:: impostor::g_settings->m_textureHeight,
    //     l_total_width_meters, l_total_length_meters,  // plan view dimensions
    //     0.5f * l_total_width_meters, 0.7f* l_total_length_meters, // vehicle dimensions or area covered by RTT
    //     virtcam::g_positions->getPosition(virtcam::VCAM_PLAN_VIEW),
    //     getFramework()
    // );

    // //! Impostor Brightness
    // ********************************************************************************************* osg::Uniform
    // *l_impostorBrightnessUniform =
    // l_pTV2D_CarImpostor->getOrCreateStateSet()->getOrCreateUniform("impostorBrightness", osg::Uniform::FLOAT);
    // l_impostorBrightnessUniform->set( 1.0f );  // PRQA S 3803
    // l_impostorBrightnessUniform->setUpdateCallback( new BrightnessUpdateCallback(getFramework()) );

    // //! Vertical mode Vehicle impostor - needs the total dimensions
    //   assets::impostor::FixedImpostor* l_pTV2D_CarImpostorVert = new assets::impostor::FixedImpostor(
    //     AssetId::EASSETS_TV2D_IMPOSTOR, l_pVehicle,
    //     static_cast<pc::vehiclemodel::VehicleModel*> (l_pVehicle->getAsset()),
    //     assets:: impostor::g_settings->m_textureHeight, assets:: impostor::g_settings->m_textureWidth,
    //     l_total_length_meters, l_total_width_meters,  // plan view dimensions
    //     0.7f* l_total_length_meters, 0.5f * l_total_width_meters, // vehicle dimensions or area covered by RTT
    //     virtcam::g_positions->getPosition(virtcam::VCAM_VERT_PLAN_VIEW),
    //     getFramework()
    // );

    // //! Vertical mode Impostor Brightness
    // ********************************************************************************************* osg::Uniform
    // *l_impostorBrightnessUniformVert =
    // l_pTV2D_CarImpostorVert->getOrCreateStateSet()->getOrCreateUniform("impostorBrightness", osg::Uniform::FLOAT);
    // l_impostorBrightnessUniformVert->set( 1.0f );  // PRQA S 3803
    // l_impostorBrightnessUniformVert->setUpdateCallback( new BrightnessUpdateCallback(getFramework()) );

    const auto l_pPlanView = new cc::views::planview::PlanView(
        "Plan View",
        getCorrectDriveHandSideViewport(g_views->m_planViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pPlanView->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters / 2., l_total_width_meters / 2., -l_total_length_meters / 2., l_total_length_meters / 2.);

    const auto l_renderManagerPlanView = new cc::factory::CustomRenderManager(
        l_pRenderManagerRegistry,
        virtcam::VCAM_PLAN_VIEW,
        rbp::vis::imp::chamaeleon::EChamaeleonView::DEFAULT);
    l_renderManagerPlanView->assignCamera(l_pPlanView);
    l_renderManagerPlanView->assignCamera(l_pFloor->getGeneratorSnapshotCam());

    l_pPlanView->addAsset(l_pFloor);
#ifndef USE_TRADITIONAL_BASEPLATE
    l_pPlanView->addAsset(l_pFloorPlate);
#endif

#ifdef ENABLE_USS_OVERLAY
    auto l_ussWarnSymbolsOverlay =
        new assets::uielements::WarnSymbols(getFramework()->asCustomFramework(), AssetId::EASSETS_UI_WARNSYMBOL_USS);
    auto l_pWarnSymbolUss = new pc::core::Asset(AssetId::EASSETS_UI_WARNSYMBOL_USS, l_ussWarnSymbolsOverlay);
#endif

    const auto l_pRemotePlanView = new cc::views::planview::PlanView(
        "Remote Plan View",
        getCorrectDriveHandSideViewport(g_views->m_remotePlanViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());
    l_pRemotePlanView->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_remote / 2.,
        l_total_width_meters_remote / 2.,
        -l_total_length_meters_remote / 2.,
        l_total_length_meters_remote / 2.);
    l_renderManagerPlanView->assignCamera(l_pRemotePlanView);
    l_pRemotePlanView->addAsset(l_pFloor);
#ifndef USE_TRADITIONAL_BASEPLATE

    l_pRemotePlanView->addAsset(l_pFloorPlate);

#ifdef ENABLE_USS_OVERLAY
    // l_pRemotePlanView->addAsset(l_pWarnSymbolUss);
#endif

#endif
    // l_pPlanView->addAsset(l_pVehicle); // replace with impostor
    // l_pPlanView->setImpostor(l_pTV2D_CarImpostor);
    // l_pPlanView->setWheels(l_pAllWheels);

    // l_pPlanView->addAsset(l_pSplineOverlay);
    // l_pPlanView->addAsset(l_pSplineOverlayShadow);
    // l_pPlanView->addAsset(l_pObstacleOverlay_vehOffset);
    //  l_pPlanView->addAsset(l_pCalibOverlay);
    //  l_pPlanView->addAsset(l_pWheelTracks);
    // l_pPlanView->addAsset(l_pRCTAOverlay);
    // l_pPlanView->addAsset(l_pDigitalDisplay);
    // l_pPlanView->addAsset(l_pSwInfoOverlay);
    l_pPlanView->setRenderOrder(osg::Camera::PRE_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_FLOOR);

    l_pRemotePlanView->setRenderOrder(osg::Camera::PRE_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_FLOOR);
    // auto l_pParkSpacePlanViewMat = new cc::views::planview::PlanViewCullCallback();
    // l_pPlanView->addCullCallback(l_pParkSpacePlanViewMat);

    //! parking space on plan view
    // auto l_pParkingspacePlanView  = new pc::core::Asset( AssetId::EASSETS_TV2D_PARKING_SPACE,
    //                                 new assets::parkingspace::ParkingSpace(getFramework()->asCustomFramework(),
    //                                 AssetId::EASSETS_TV2D_PARKING_SPACE, l_pPlanView, l_pParkSpacePlanViewMat));
    // l_pPlanView->addAsset(l_pParkingspacePlanView);

    // auto l_pCameraIconPlanViewMat = new cc::views::planview::PlanViewCullCallback();
    // l_pPlanView->addCullCallback(l_pCameraIconPlanViewMat);

    // auto l_pCameraIcon = new pc::core::Asset( AssetId::EASSETS_UI_CAMERA_ICON,
    //                       new assets::uielements::CameraIcon(getFramework()->asCustomFramework(),
    //                       AssetId::EASSETS_UI_CAMERA_ICON, l_pPlanView));
    // l_pPlanView->addAsset(l_pCameraIcon);


    const auto l_pVehicle2DWheels = new pc::core::Asset(
        AssetId::EASSETS_VEHICLE_2D_WHEELS, new assets::vehicle2dwheels::Vehicle2DWheels(getFramework()));

    const auto l_pVehicle2DCrabWheelsNormal = new pc::core::Asset(
        AssetId::EASSETS_VEHICLE_2D_WHEELS, new assets::crabwheel::Vehicle2DCrabWheels(getFramework(), false));
    const auto l_pVehicle2DCrabWheels = new pc::core::Asset(
        AssetId::EASSETS_VEHICLE_2D_WHEELS, new assets::crabwheel::Vehicle2DCrabWheels(getFramework(), true));

    // resize the vehicle 2d for plan view
    const auto l_pPlanViewVehicle2D = new cc::views::planview::PlanView(
        "Plan View Vehicle2D",
        getCorrectDriveHandSideViewport(g_views->m_planViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pPlanViewVehicle2D->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_vehicle2d / 2.,
        l_total_width_meters_vehicle2d / 2.,
        -l_total_length_meters_vehicle2d / 2.,
        l_total_length_meters_vehicle2d / 2.);

    const auto l_pRemotePlanViewVehicle2D = new cc::views::planview::PlanView(
        "Remote Plan View Vehicle2D",
        getCorrectDriveHandSideViewport(g_views->m_remotePlanViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pRemotePlanViewVehicle2D->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_vehicle2d_remote / 2.,
        l_total_width_meters_vehicle2d_remote / 2.,
        -l_total_length_meters_vehicle2d_remote / 2.,
        l_total_length_meters_vehicle2d_remote / 2.);

#if USE_RESIZE_2DVEHICLEMODEL_PLANVIEW

    l_pPlanViewVehicle2D->addAsset(l_pOutermostLines);
    // l_pPlanViewVehicle2D->addAsset(l_pWheelTracks);
#ifdef ENABLE_USS_OVERLAY
    // l_pPlanViewVehicle2D->addAsset(l_pWarnSymbolUss);
#endif
    l_pPlanViewVehicle2D->setRenderOrder(
        osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_ABOVE_VEHICLE_2D);
    l_pPlanViewVehicle2D->addAsset(new assets::uielements::Vehicle2DOverlay(
            getFramework()->asCustomFramework(), AssetId::EASSETS_VEHICLE_2D_ICON, l_pPlanViewVehicle2D));
    l_pPlanViewVehicle2D->addAsset(l_pVehicle2DWheels);
    // l_pPlanViewVehicle2D->addAsset(l_pAllWheels);
    // l_pPlanViewVehicle2D->addAsset(l_pVehicle2DIcon);

    // static_cast<cc::assets::uielements::Vehicle2DOverlay*>(l_pVehicle2DIcon->getAsset())->setReferenceView(l_pPlanViewVehicle2D);

    l_pRemotePlanViewVehicle2D->setRenderOrder(
        osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_ABOVE_VEHICLE_2D);
    // l_pRemotePlanViewVehicle2D->addAsset(l_pVehicle2DIcon);
    l_pRemotePlanViewVehicle2D->addAsset(new assets::uielements::Vehicle2DOverlay(
        getFramework()->asCustomFramework(), AssetId::EASSETS_VEHICLE_2D_ICON, l_pRemotePlanViewVehicle2D));
    l_pRemotePlanViewVehicle2D->addAsset(l_pVehicle2DWheels);
#else
    l_pPlanView->addAsset(l_pOutermostLines);
    l_pPlanView->addAsset(l_pWheelTracks);
#ifdef ENABLE_USS_OVERLAY
    l_pPlanView->addAsset(l_pWarnSymbolUss);
#endif
    l_pPlanView->addAsset(l_pVehicle2DIcon);

#endif
    const auto l_pParkingSlotPlanviewManager = new pc::core::Asset(
        AssetId::EASSETS_PARKINGSPOTS, new cc::assets::parkingslot::corners::ParkingSlotCornersPlanView(getFramework()));

    const auto l_pImageinImagePlanViewVehicle2D = new cc::views::planview::PlanView(
        "Image in Image Plan View Vehicle2D",
        getCorrectDriveHandSideViewport(g_views->m_imageInimagePlanviewViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pImageinImagePlanViewVehicle2D->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_vehicle2d_imageInImage / 2.,
        l_total_width_meters_vehicle2d_imageInImage / 2.,
        -l_total_length_meters_vehicle2d_imageInImage / 2.,
        l_total_length_meters_vehicle2d_imageInImage / 2.);

    l_pImageinImagePlanViewVehicle2D->addAsset(l_pOutermostLines);
#ifdef ENABLE_USS_OVERLAY
    l_pImageinImagePlanViewVehicle2D->addAsset(l_pWarnSymbolUss);
#endif
    l_pImageinImagePlanViewVehicle2D->setRenderOrder(
        osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_ABOVE_VEHICLE_2D);
    // l_pImageinImagePlanViewVehicle2D->addAsset(l_pVehicle2DIcon);
    l_pImageinImagePlanViewVehicle2D->addAsset(new assets::uielements::Vehicle2DOverlay(
        getFramework()->asCustomFramework(), AssetId::EASSETS_VEHICLE_2D_ICON, l_pImageinImagePlanViewVehicle2D));
    l_pImageinImagePlanViewVehicle2D->addAsset(l_pVehicle2DWheels);
    const auto l_pPlanViewUSSOverlay = new cc::views::planview::PlanView(
        "Plan View USS Overlay",
        getCorrectDriveHandSideViewport(g_views->m_planViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pPlanViewUSSOverlay->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters / 2., l_total_width_meters / 2., -l_total_length_meters / 2., l_total_length_meters / 2.);

    const auto l_distanceOverlay = new pc::core::Asset(
        AssetId::EASSETS_DIGITAL_DISTANCE_DISPLAY,
        new assets::distanceoverlay::DistanceOverlay(l_zoneLayout, cc::assets::distanceoverlay::g_distanceOverlaySettings->m_characterSize, getFramework()));

    const auto l_distanceOverlayAsset = static_cast<assets::distanceoverlay::DistanceOverlay*>(l_distanceOverlay->getAsset());

    l_distanceOverlayAsset->setStopDistanceEnabled(false);

#if USE_RADAR_WALL
    if(g_customerScene->m_objectWallVariant == 1)
    {
        l_pPlanViewUSSOverlay->addAsset(l_obstacleOverlay);
    }
    else if (g_customerScene->m_objectWallVariant == 2)
    {
        l_pPlanViewUSSOverlay->addAsset(l_pdcOverlay);
    }
    else
    {
        // add nothing
    }
#else
    l_pPlanViewUSSOverlay->addAsset(l_pObstacleOverlay_vehOffset);
#endif

    // l_pPlanViewUSSOverlay->addAsset(l_pDigitalDisplay);//wind
    // l_pPlanViewUSSOverlay->addAsset(l_pSwInfoOverlay);
    // l_pPlanViewUSSOverlay->addAsset(l_pArrowTrajectoryAVM);
    l_pPlanViewUSSOverlay->addAsset(l_distanceOverlay);
    l_pPlanViewUSSOverlay->setRenderOrder(osg::Camera::POST_RENDER, BYD_PLANVIEW_RENDERBIN_ORDER_USS);

    const auto l_pPlanViewFullscreen = new cc::views::planview::PlanView(
        "Plan View Fullscreen",
        getCorrectDriveHandSideViewport(g_views->m_usableCanvasViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pPlanViewFullscreen->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_full / 2.,
        l_total_width_meters_full / 2.,
        -l_total_length_meters_full / 2.,
        l_total_length_meters_full / 2.);

    l_renderManagerPlanView->assignCamera(l_pPlanViewFullscreen);
    l_pPlanViewFullscreen->addAsset(l_pFloor);
    l_pPlanViewFullscreen->addAsset(l_pFloorPlate);
    l_pPlanViewFullscreen->setRenderOrder(osg::Camera::PRE_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_FLOOR);

    const auto l_pPlanViewFullscreenTR = new cc::views::planview::PlanView(
        "Plan View Fullscreen Turn around",
        getCorrectDriveHandSideViewport(g_views->m_usableCanvasViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pPlanViewFullscreenTR->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_full / 2.,
        l_total_width_meters_full / 2.,
        -l_total_length_meters_full / 2.,
        l_total_length_meters_full / 2.);

    l_renderManagerPlanView->assignCamera(l_pPlanViewFullscreenTR);
    l_pPlanViewFullscreenTR->addAsset(l_pFloor);
    l_pPlanViewFullscreenTR->addAsset(l_pFloorPlate);
    l_pPlanViewFullscreenTR->setRenderOrder(osg::Camera::PRE_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_FLOOR);
#ifdef TARGET_STANDALONE
    l_pPlanViewFullscreenTR->addAsset(
        new cc::assets::uielements::TurnArroundOverlay{
            getFramework(),
            AssetId::EASSETS_VEHICLE_2D_ICON,
            cc::assets::uielements::g_defaultSettings.data(),
            cc::assets::uielements::g_defaultSettings->m_turnArroundCalibrateVehicle,
            cc::assets::uielements::g_defaultSettings->m_turnArroundCalibrateVehiclePos,
            l_pPlanViewFullscreenTR
        });
#endif
    auto turnArroundUpdateVisitor = cc::assets::uielements::MeterPerPixelProjectionUpdateVisitor{cc::assets::uielements::g_defaultSettings->m_meterPerPixelTR};
    l_pPlanViewFullscreenTR->accept(turnArroundUpdateVisitor);



    const auto l_pPlanViewFullscreenHM = new cc::views::planview::PlanView(
        "Plan View Fullscreen Horizontal Move",
        getCorrectDriveHandSideViewport(g_views->m_usableCanvasViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pPlanViewFullscreenHM->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_full / 2.,
        l_total_width_meters_full / 2.,
        -l_total_length_meters_full / 2.,
        l_total_length_meters_full / 2.);

    l_renderManagerPlanView->assignCamera(l_pPlanViewFullscreenHM);
    l_pPlanViewFullscreenHM->addAsset(l_pFloor);
    l_pPlanViewFullscreenHM->addAsset(l_pFloorPlate);
    l_pPlanViewFullscreenHM->setRenderOrder(osg::Camera::PRE_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_FLOOR);
    l_pPlanViewFullscreenHM->addAsset(
            new cc::assets::uielements::TurnArroundOverlay{
                getFramework(),
                AssetId::EASSETS_VEHICLE_2D_ICON,
                cc::assets::uielements::g_defaultSettings.data(),
                cc::assets::uielements::g_defaultSettings->m_hmCalibrateVehicle,
                cc::assets::uielements::g_defaultSettings->m_hmCalibrateVehiclePos,
                l_pPlanViewFullscreenHM
            });
    auto hmUpdateVisitor = cc::assets::uielements::MeterPerPixelProjectionUpdateVisitor{cc::assets::uielements::g_defaultSettings->m_meterPerPixelHM};// 360 pixels represent 3m from UI
    l_pPlanViewFullscreenHM->accept(hmUpdateVisitor);

    const auto l_pPlanViewCompassTR = new cc::views::planview::PlanView(
        "Plan View Compass Turn around ",
        getCorrectDriveHandSideViewport(g_views->m_usableCanvasViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pPlanViewCompassTR->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_full / 2.,
        l_total_width_meters_full / 2.,
        -l_total_length_meters_full / 2.,
        l_total_length_meters_full / 2.);

    l_renderManagerPlanView->assignCamera(l_pPlanViewCompassTR);

    l_pPlanViewCompassTR->addAsset(l_pFloor);
    l_pPlanViewCompassTR->addAsset(l_pFloorPlate);
    if(g_customerScene->m_objectWallVariant == 1)
    {
        l_pPlanViewCompassTR->addAsset(l_obstacleOverlay);
    }
    else if (g_customerScene->m_objectWallVariant == 2)
    {
        l_pPlanViewCompassTR->addAsset(l_pdcOverlay);
    }
    else
    {
        // add nothing
    }
    l_pPlanViewCompassTR->setRenderOrder(osg::Camera::PRE_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_FLOOR);
#ifdef TARGET_STANDALONE
    l_pPlanViewCompassTR->addAsset(
        new cc::assets::uielements::TurnArroundOverlay{
            getFramework(),
            AssetId::EASSETS_VEHICLE_2D_ICON,
            cc::assets::uielements::g_defaultSettings.data(),
            cc::assets::uielements::g_defaultSettings->m_compassCalibrateVehicle,
            cc::assets::uielements::g_defaultSettings->m_compassCalibrateVehiclePos,
            l_pPlanViewCompassTR
        });
#endif
    auto comPassUpdateVisitor = cc::assets::uielements::TurnArroundProjectionUpdateVisitor{cc::assets::uielements::g_defaultSettings->m_compassCalibrateVehicle};
    l_pPlanViewCompassTR->accept(comPassUpdateVisitor);

    auto const l_crabTrajectoryAsset = new cc::assets::trajectory::CrabTrajectoryAsset{
        cc::core::AssetId::EASSETS_TRAJECTORY_CRAB_TRAJECTORY, getFramework(), g_trajParams};

    const auto l_pPlanViewCarbTR = new cc::views::planview::PlanView(
        "Plan View Carb Turn around ",
        getCorrectDriveHandSideViewport(g_views->m_usableCanvasViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pPlanViewCarbTR->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_full / 2.,
        l_total_width_meters_full / 2.,
        -l_total_length_meters_full / 2.,
        l_total_length_meters_full / 2.);

    l_renderManagerPlanView->assignCamera(l_pPlanViewCarbTR);

    l_pPlanViewCarbTR->addAsset(l_pFloor);
    l_pPlanViewCarbTR->addAsset(l_pFloorPlate);
    if(g_customerScene->m_objectWallVariant == 1)
    {
        l_pPlanViewCarbTR->addAsset(l_obstacleOverlay);
    }
    else if (g_customerScene->m_objectWallVariant == 2)
    {
        l_pPlanViewCarbTR->addAsset(l_pdcOverlay);
    }
    else
    {
        // add nothing
    }
    l_pPlanViewCarbTR->setRenderOrder(osg::Camera::PRE_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_FLOOR);
    l_pPlanViewCarbTR->addAsset(
        new cc::assets::uielements::TurnArroundOverlay{
            getFramework(),
            AssetId::EASSETS_VEHICLE_2D_ICON,
            cc::assets::uielements::g_defaultSettings.data(),
            cc::assets::uielements::g_defaultSettings->m_carbCalibrateVehicle,
            cc::assets::uielements::g_defaultSettings->m_carbCalibrateVehiclePos,
            l_pPlanViewCarbTR
        });
    l_pPlanViewCarbTR->addAsset(l_crabTrajectoryAsset);
    l_pPlanViewCarbTR->addAsset(l_pVehicle2DCrabWheelsNormal);
    l_pPlanViewCarbTR->addAsset(l_pVehicle2DCrabWheels);
    auto carbUpdateVisitor = cc::assets::uielements::MeterPerPixelProjectionUpdateVisitor{cc::assets::uielements::g_defaultSettings->m_meterPerPixelCrab};// 360 pixels represent 3m from UI
    l_pPlanViewCarbTR->accept(carbUpdateVisitor);

    const auto l_pPlanViewFullscreenVehicle2D = new cc::views::planview::PlanView(
        "Plan View Vehicle2D Fullscreen",
        getCorrectDriveHandSideViewport(g_views->m_usableCanvasViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pPlanViewFullscreenVehicle2D->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_vehicle2d_fullscreen / 2.,
        l_total_width_meters_vehicle2d_fullscreen / 2.,
        -l_total_length_meters_vehicle2d_fullscreen / 2.,
        l_total_length_meters_vehicle2d_fullscreen / 2.);

    l_pPlanViewFullscreenVehicle2D->addAsset(l_pOutermostLines);
    l_pPlanViewFullscreenVehicle2D->addAsset(l_pWheelTracks);
#ifdef ENABLE_USS_OVERLAY
    l_pPlanViewFullscreenVehicle2D->addAsset(l_pWarnSymbolUss);
#endif
    // l_pPlanViewFullscreenVehicle2D->addAsset(l_pVehicle2DIcon);
    l_pPlanViewFullscreenVehicle2D->addAsset(new assets::uielements::Vehicle2DOverlay(
        getFramework()->asCustomFramework(), AssetId::EASSETS_VEHICLE_2D_ICON, l_pPlanViewFullscreenVehicle2D));
    l_pPlanViewFullscreenVehicle2D->addAsset(l_pVehicle2DWheels);
    l_pPlanViewFullscreenVehicle2D->setRenderOrder(
        osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_ABOVE_VEHICLE_2D);

    const auto l_pPlanViewFullscreenUSSOverlay = new cc::views::planview::PlanView(
        "Plan View Fullscreen USS Overlay",
        getCorrectDriveHandSideViewport(g_views->m_usableCanvasViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pPlanViewFullscreenUSSOverlay->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_full / 2.,
        l_total_width_meters_full / 2.,
        -l_total_length_meters_full / 2.,
        l_total_length_meters_full / 2.);

    l_pPlanViewFullscreenUSSOverlay->addAsset(l_distanceOverlay);

#ifndef USE_RADAR_WALL
    l_pPlanViewFullscreenUSSOverlay->addAsset(l_pObstacleOverlay_vehOffset);
#else
    if(g_customerScene->m_objectWallVariant == 1)
    {
        l_pPlanViewFullscreenUSSOverlay->addAsset(l_obstacleOverlay);
    }
    else if (g_customerScene->m_objectWallVariant == 2)
    {
        l_pPlanViewFullscreenUSSOverlay->addAsset(l_pdcOverlay);
    }
    else
    {
        // add nothing
    }
#endif
    // l_pPlanViewFullscreenUSSOverlay->addAsset(l_pDigitalDisplay);
    l_pPlanViewFullscreenUSSOverlay->setRenderOrder(
        osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_USS);

#if ENABLE_VERTICAL_MODE
    //! Vertical Plan view
    auto l_pVertPlanView = new cc::views::planview::PlanView(
        "Vert Plan View",
        getCorrectDriveHandSideViewport(g_views->m_vertPlanViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertPlan,
        getFramework());

    l_pVertPlanView->setProjectionMatrixAsOrtho2D(
        -l_total_length_meters_vert / 2.,
        l_total_length_meters_vert / 2.,
        -l_total_width_meters_vert / 2.,
        l_total_width_meters_vert / 2.);

    l_renderManagerPlanView->assignCamera(l_pVertPlanView);

    // l_pVertPlanView->addAsset(l_pOutermostLines);
    l_pVertPlanView->addAsset(l_pFloor);
    // l_pVertPlanView->addAsset(l_pVehicle); // replace with impostor
    // l_pVertPlanView->setImpostor(l_pTV2D_CarImpostorVert);
    // l_pVertPlanView->setWheels(l_pAllWheels);

    // l_pVertPlanView->addAsset(l_pSplineOverlay);
    // l_pVertPlanView->addAsset(l_pSplineOverlayShadow);
#if USE_RADAR_WALL
#else
// l_pVertPlanView->addAsset(l_pObstacleOverlay);
#endif
    //  l_pVertPlanView->addAsset(l_pCalibOverlay);
    //  l_pVertPlanView->addAsset(l_pWheelTracks);
    // l_pVertPlanView->addAsset(l_pDigitalDisplay);
#ifndef USE_TRADITIONAL_BASEPLATE
    l_pVertPlanView->addAsset(l_pFloorPlate);
#endif
    // l_pVertPlanView->addAsset(l_pTextSymbol);
    // l_pVertPlanView->addAsset(l_pRCTAOverlay);
    l_pVertPlanView->setRenderOrder(osg::Camera::PRE_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_FLOOR);

    // resize the vehicle 2d for plan view
    auto l_pVertPlanViewVehicle2D = new cc::views::planview::PlanView(
        "Vert Plan View Vehicle2D",
        getCorrectDriveHandSideViewport(g_views->m_vertPlanViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertPlan,
        getFramework());

    l_pVertPlanViewVehicle2D->setProjectionMatrixAsOrtho2D(
        -l_total_length_meters_vehicle2d_vert / 2.,
        l_total_length_meters_vehicle2d_vert / 2.,
        -l_total_width_meters_vehicle2d_vert / 2.,
        l_total_width_meters_vehicle2d_vert / 2.);

    auto l_pVertParkingPlanViewVehicle2D = new cc::views::planview::PlanView(
        "Vert Parking Plan View Vehicle2D",
        getCorrectDriveHandSideViewport(g_views->m_vertParkingPlanViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertPlan,
        getFramework());

    l_pVertParkingPlanViewVehicle2D->setProjectionMatrixAsOrtho2D(
        -l_total_length_meters_parking_vert_vehicle2d / 2.0f,
        l_total_length_meters_parking_vert_vehicle2d / 2.0f,
        -l_total_width_meters_parking_vert_vehicle2d / 2.0f,
        l_total_width_meters_parking_vert_vehicle2d / 2.0f);

#ifdef TARGET_STANDALONE
    l_pVertParkingPlanViewVehicle2D->addUpdateCallback(new cc::views::planview::PlanViewProjectionCallback);
#endif

    l_pVertParkingPlanViewVehicle2D->addAsset(l_pOutermostLines);
    // l_pVertParkingPlanViewVehicle2D->addAsset(l_pWheelTracks);
#ifdef ENABLE_USS_OVERLAY
    l_pVertParkingPlanViewVehicle2D->addAsset(l_pWarnSymbolUss);
#endif
    // l_pVertParkingPlanViewVehicle2D->addAsset(l_pVehicle2D);
    // l_pVertParkingPlanViewVehicle2D->addAsset(l_pVehicle2DIcon);
    l_pVertParkingPlanViewVehicle2D->addAsset(new assets::uielements::Vehicle2DOverlay(
        getFramework()->asCustomFramework(), AssetId::EASSETS_VEHICLE_2D_ICON, l_pVertParkingPlanViewVehicle2D));
    l_pVertParkingPlanViewVehicle2D->addAsset(l_pVehicle2DWheels);
    l_pVertParkingPlanViewVehicle2D->setRenderOrder(
        osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_ABOVE_VEHICLE_2D);

#if USE_RESIZE_2DVEHICLEMODEL_PLANVIEW

    l_pVertPlanViewVehicle2D->addAsset(l_pOutermostLines);
    // l_pVertPlanViewVehicle2D->addAsset(l_pWheelTracks);
#ifdef ENABLE_USS_OVERLAY
    l_pVertPlanViewVehicle2D->addAsset(l_pWarnSymbolUss);
#endif
    // l_pVertPlanViewVehicle2D->addAsset(l_pVehicle2D);
    // l_pVertPlanViewVehicle2D->addAsset(l_pVehicle2DIcon);
    l_pVertPlanViewVehicle2D->addAsset(new assets::uielements::Vehicle2DOverlay(
        getFramework()->asCustomFramework(), AssetId::EASSETS_VEHICLE_2D_ICON, l_pVertPlanViewVehicle2D));
    l_pVertPlanViewVehicle2D->addAsset(l_pVehicle2DWheels);
    l_pVertPlanViewVehicle2D->setRenderOrder(
        osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_ABOVE_VEHICLE_2D);

#else
    // l_pVertPlanView->addAsset(l_pVehicle2D);
    l_pVertPlanView->addAsset(l_pOutermostLines);
    l_pVertPlanView->addAsset(l_pWheelTracks);
#ifdef ENABLE_USS_OVERLAY
    l_pVertPlanView->addAsset(l_pWarnSymbolUss);
#endif
    // l_pVertPlanView->addAsset(l_pVehicle2DIcon);

#endif

    auto l_pVertPlanViewUSSOverlay = new cc::views::planview::PlanView(
        "Vert Plan View USS Overlay",
        getCorrectDriveHandSideViewport(g_views->m_vertPlanViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertPlan,
        getFramework());

    l_pVertPlanViewUSSOverlay->setProjectionMatrixAsOrtho2D(
        -l_total_length_meters_vehicle2d_vert / 2.,
        l_total_length_meters_vehicle2d_vert / 2.,
        -l_total_width_meters_vehicle2d_vert / 2.,
        l_total_width_meters_vehicle2d_vert / 2.);

#ifndef USE_RADAR_WALL
    l_pVertPlanViewUSSOverlay->addAsset(l_pObstacleOverlay_vehOffset);
#endif
    //   l_pVertPlanViewUSSOverlay->addAsset(l_pDigitalDisplay);//wind
    // l_pVertPlanViewUSSOverlay->addAsset(l_pSwInfoOverlay);
    l_pVertPlanViewUSSOverlay->setRenderOrder(osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_USS);

    auto l_pVertParkingPlanViewUSSOverlay = new cc::views::planview::PlanView(
        "Vert Parking Plan View USS Overlay",
        getCorrectDriveHandSideViewport(g_views->m_vertParkingPlanViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertPlan,
        getFramework());

    l_pVertParkingPlanViewUSSOverlay->setProjectionMatrixAsOrtho2D(
        -l_total_length_meters_parking_vert / 2.,
        l_total_length_meters_parking_vert / 2.,
        -l_total_width_meters_parking_vert / 2.,
        l_total_width_meters_parking_vert / 2.);

#ifndef USE_RADAR_WALL
    l_pVertParkingPlanViewUSSOverlay->addAsset(l_pObstacleOverlay_vehOffset);
#endif
    //   l_pVertParkingPlanViewUSSOverlay->addAsset(l_pDigitalDisplay);//wind
    // l_pVertParkingPlanViewUSSOverlay->addAsset(l_pSwInfoOverlay);
    l_pVertParkingPlanViewUSSOverlay->setRenderOrder(
        osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_USS);

    auto l_pVertPlanViewFullscreen = new cc::views::planview::PlanView(
        "Vert Plan View Fullscreen",
        getCorrectDriveHandSideViewport(g_views->m_vertFullScreenViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertPlan,
        getFramework());

    l_pVertPlanViewFullscreen->setProjectionMatrixAsOrtho2D(
        -l_total_length_meters_full_vert / 2.,
        l_total_length_meters_full_vert / 2.,
        -l_total_width_meters_full_vert / 2.,
        l_total_width_meters_full_vert / 2.);

    l_renderManagerPlanView->assignCamera(l_pVertPlanViewFullscreen);
    l_pVertPlanViewFullscreen->addAsset(l_pFloor);
    l_pVertPlanViewFullscreen->addAsset(l_pFloorPlate);
    // l_pVertPlanViewFullscreen->addAsset(l_pTextSymbol);
    l_pVertPlanViewFullscreen->setRenderOrder(osg::Camera::PRE_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_FLOOR);

    auto l_pVertPlanViewFullscreenVehicle2D = new cc::views::planview::PlanView(
        "Vert Plan View Vehicle2D Fullscreen",
        getCorrectDriveHandSideViewport(g_views->m_vertFullScreenViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertPlan,
        getFramework());

    l_pVertPlanViewFullscreenVehicle2D->setProjectionMatrixAsOrtho2D(
        -l_total_length_meters_vehicle2d_fullscreen_vert / 2.,
        l_total_length_meters_vehicle2d_fullscreen_vert / 2.,
        -l_total_width_meters_vehicle2d_fullscreen_vert / 2.,
        l_total_width_meters_vehicle2d_fullscreen_vert / 2.);

    l_pVertPlanViewFullscreenVehicle2D->addAsset(l_pOutermostLines);
    // l_pVertPlanViewFullscreenVehicle2D->addAsset(l_pWheelTracks);
#ifdef ENABLE_USS_OVERLAY
    l_pVertPlanViewFullscreenVehicle2D->addAsset(l_pWarnSymbolUss);
#endif
    // l_pVertPlanViewFullscreenVehicle2D->addAsset(l_pVehicle2DIcon);
    l_pVertPlanViewFullscreenVehicle2D->addAsset(new assets::uielements::Vehicle2DOverlay(
        getFramework()->asCustomFramework(), AssetId::EASSETS_VEHICLE_2D_ICON, l_pVertPlanViewFullscreenVehicle2D));
    l_pVertPlanViewFullscreenVehicle2D->addAsset(l_pVehicle2DWheels);
    l_pVertPlanViewFullscreenVehicle2D->setRenderOrder(
        osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_ABOVE_VEHICLE_2D);

    auto l_pVertPlanViewFullscreenUSSOverlay = new cc::views::planview::PlanView(
        "Vert Plan View Fullscreen USS Overlay",
        getCorrectDriveHandSideViewport(g_views->m_vertFullScreenViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertPlan,
        getFramework());

    l_pVertPlanViewFullscreenUSSOverlay->setProjectionMatrixAsOrtho2D(
        -l_total_length_meters_vehicle2d_vert / 2.,
        l_total_length_meters_vehicle2d_vert / 2.,
        -l_total_width_meters_vehicle2d_vert / 2.,
        l_total_width_meters_vehicle2d_vert / 2.);

#ifndef USE_RADAR_WALL
    l_pVertPlanViewFullscreenUSSOverlay->addAsset(l_pObstacleOverlay_vehOffset);
#endif
    //   l_pVertPlanViewFullscreenUSSOverlay->addAsset(l_pDigitalDisplay);//wind
    // l_pVertPlanViewFullscreenUSSOverlay->addAsset(l_pSwInfoOverlay);
    l_pVertPlanViewFullscreenUSSOverlay->setRenderOrder(
        osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_USS);

#endif

    // free parking manager asset
    auto const l_freeparkingManager = new pc::core::Asset(
        AssetId::EASSETS_FREEPARKING_OVERLAY, new cc::assets::freeparkingoverlay::FreeparkingManager(getFramework()));

    const auto l_pHoriParkingPlanViewVehicle2D = new cc::views::planview::PlanView(
        "Hori Parking Plan View Vehicle2D",
        getCorrectDriveHandSideViewport(g_views->m_apaParkingPlanViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_horiParkingPlan,
        getFramework());
    l_pHoriParkingPlanViewVehicle2D->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_parking_hori_vehicle2d / 2.0,
        l_total_width_meters_parking_hori_vehicle2d / 2.0,
        -l_total_length_meters_parking_hori_vehicle2d / 2.0,
        l_total_length_meters_parking_hori_vehicle2d / 2.0);
    l_pHoriParkingPlanViewVehicle2D->addAsset(l_pOutermostLines);
    // l_pHoriParkingPlanViewVehicle2D->addAsset(l_pWheelTracks);
#ifdef ENABLE_USS_OVERLAY
    l_pHoriParkingPlanViewVehicle2D->addAsset(l_pWarnSymbolUss);
#endif
    l_pHoriParkingPlanViewVehicle2D->setRenderOrder(
        osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_ABOVE_VEHICLE_2D);
    // l_pHoriParkingPlanViewVehicle2D->addAsset(l_pVehicle2DIcon);
    l_pHoriParkingPlanViewVehicle2D->addAsset(l_freeparkingManager);
    l_pHoriParkingPlanViewVehicle2D->addAsset(new assets::uielements::Vehicle2DOverlay(
        getFramework()->asCustomFramework(), AssetId::EASSETS_VEHICLE_2D_ICON, l_pHoriParkingPlanViewVehicle2D));
    l_pHoriParkingPlanViewVehicle2D->addAsset(l_pVehicle2DWheels);
    l_pHoriParkingPlanViewVehicle2D->addAsset(l_pParkingSlotPlanviewManager);

    // horizontal parking plan view
    const auto l_pHoriParkingPlanView = new cc::views::planview::PlanView(
        "Hori Parking Plan View",
        getCorrectDriveHandSideViewport(g_views->m_apaParkingPlanViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_horiParkingPlan,
        getFramework());

    // auto l_freeparkingButton = new
    // cc::assets::button::freeparkingbutton::FreeparkingButton{AssetId::EASSETS_UI_FREEPARKING_BUTTON, getFramework()};
    // auto l_startPauseConfirmButton = new
    // cc::assets::button::confirmbutton::StartPauseConfirmButton{AssetId::EASSETS_UI_CONFIRM_BUTTON, getFramework()};
    // auto l_continueButton = new
    // cc::assets::button::continuebutton::ContinueButton{AssetId::EASSETS_UI_CONTINUE_BUTTON, getFramework()}; auto
    // l_quitButton = new cc::assets::button::quitbutton::QuitButton{AssetId::EASSETS_UI_QUIT_BUTTON, getFramework()};
    // auto l_parkoutLeftButton = new
    // cc::assets::button::parkout::ParkoutLeftButton{AssetId::EASSETS_UI_PARKOUT_LEFT_BUTTON, getFramework()}; auto
    // l_parkoutRightButton = new
    // cc::assets::button::parkout::ParkoutRightButton{AssetId::EASSETS_UI_PARKOUT_RIGHT_BUTTON, getFramework()};

    // l_freeparkingButton->setName("FreeparkingButton");
    // l_startPauseConfirmButton->setName("ConfirmButton");
    // l_continueButton->setName("ContinueButton");
    // l_quitButton->setName("QuitButton");
    // l_parkoutLeftButton->setName("ParkoutLeft");
    // l_parkoutRightButton->setName("ParkoutRight");

    l_pHoriParkingPlanView->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_parking_hori / 2.,
        l_total_width_meters_parking_hori / 2.,
        -l_total_length_meters_parking_hori / 2.,
        l_total_length_meters_parking_hori / 2.);

    const auto l_renderManagerHoriParkingPlanView =
        new cc::factory::CustomRenderManager(l_pRenderManagerRegistry, virtcam::VCAM_HORI_PARKING_PLAN_VIEW,
        rbp::vis::imp::chamaeleon::EChamaeleonView::DEFAULT);
    l_renderManagerHoriParkingPlanView->assignCamera(l_pHoriParkingPlanView);

    const auto l_pE3ParkingTopViewOverlay = new cc::assets::E3ParkingTopViewAsset(AssetId::EASSETS_UI_PARKING_E3_TOPVIEW, getFramework()->asCustomFramework());

    // For osg vehicle 2D model
    l_pHoriParkingPlanView->addAsset(l_pFloor);

#ifndef USE_TRADITIONAL_BASEPLATE
    l_pHoriParkingPlanView->addAsset(l_pFloorPlate);
#endif
    // l_pHoriParkingPlanView->setWheels(l_pAllWheels);

    // l_pHoriParkingPlanView->addAsset(l_apaCornerIcons); //For apa corner icons
    // l_pHoriParkingPlanView->addAsset(l_pVehicle2DWheels);
    // l_pHoriParkingPlanView->addAsset(l_pVehicle2DIcon);
    // l_pHoriParkingPlanView->addAsset(l_pVehicle2D);
    // //   l_pHoriParkingPlanView->setWheels(l_pAllWheels);
    // l_pHoriParkingPlanView->addAsset(l_pVehicleTransIcon);

    // l_pHoriParkingPlanView->addAsset(l_pObstacleOverlay);
    // l_pHoriParkingPlanView->addAsset(l_pWheelTracks);
    // l_pHoriParkingPlanView->addAsset(l_pDigitalDisplay);

#if USE_RADAR_WALL
#else
#endif

    const auto l_pHoriParkingPlanViewUSSOverlay = new cc::views::planview::PlanView(
        "Hori Parking Plan View USS Overlay",
        getCorrectDriveHandSideViewport(g_views->m_apaParkingPlanViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_horiParkingPlan,
        getFramework());

    l_pHoriParkingPlanViewUSSOverlay->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_parking_hori / 2.,
        l_total_width_meters_parking_hori / 2.,
        -l_total_length_meters_parking_hori / 2.,
        l_total_length_meters_parking_hori / 2.);

#ifndef USE_RADAR_WALL
    l_pHoriParkingPlanViewUSSOverlay->addAsset(l_pObstacleOverlay_vehOffset);
#endif
    //   l_pHoriParkingPlanViewUSSOverlay->addAsset(l_pDigitalDisplay);//wind
    // l_pHoriParkingPlanViewUSSOverlay->addAsset(l_freeparkingManager);
    // l_pHoriParkingPlanViewUSSOverlay->addAsset(l_pSwInfoOverlay);
    l_pHoriParkingPlanViewUSSOverlay->addAsset(l_distanceOverlay);
    if(g_customerScene->m_objectWallVariant == 1)
    {
        l_pHoriParkingPlanViewUSSOverlay->addAsset(l_obstacleOverlay);
    }
    else if (g_customerScene->m_objectWallVariant == 2)
    {
        l_pHoriParkingPlanViewUSSOverlay->addAsset(l_pdcOverlay);
    }
    else
    {
        // add nothing
    }
    l_pHoriParkingPlanViewUSSOverlay->addAsset(l_freeparkingManager);

    l_pHoriParkingPlanViewUSSOverlay->setRenderOrder(osg::Camera::POST_RENDER, BYD_PLANVIEW_RENDERBIN_ORDER_USS);

#if USE_PARKSPACE_MARK
    l_pHoriParkingPlanView->addAsset(l_pParkingPlanSymbol);
#endif

    // ! parking space on horizontal plan view
    // auto l_pParkSpacePlanViewMat = new cc::views::planview::PlanViewCullCallback();
    // auto l_pParkingspacePlanView = new pc::core::Asset(
    //     AssetId::EASSETS_TV2D_PARKING_SPACE,
    //     new assets::parkingspace::ParkingSpace(
    //         getFramework()->asCustomFramework(),
    //         AssetId::EASSETS_TV2D_PARKING_SPACE,
    //         l_pHoriParkingPlanView,
    //         g_views->m_apaParkingPlanViewport,
    //         true,
    //         l_pParkSpacePlanViewMat));

    // l_pHoriParkingPlanView->addCullCallback(l_pParkSpacePlanViewMat);
    // l_pHoriParkingPlanView->addAsset(l_pParkingspacePlanView);
    l_pHoriParkingPlanView->setRenderOrder(osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_VEHICLE_2D);

    // horizontal parking floor plan view
    const auto l_pHoriParkingFloorPlanView = new cc::views::planview::PlanView(
        "Hori Parking Plan Floor View",
        getCorrectDriveHandSideViewport(g_views->m_apaParkingPlanViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_horiParkingPlan,
        getFramework());

    l_pHoriParkingFloorPlanView->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_parking_hori / 2.,
        l_total_width_meters_parking_hori / 2.,
        -l_total_length_meters_parking_hori / 2.,
        l_total_length_meters_parking_hori / 2.);

    l_renderManagerHoriParkingPlanView->assignCamera(l_pHoriParkingFloorPlanView);

    l_pHoriParkingFloorPlanView->addAsset(l_pFloor);
#ifndef USE_TRADITIONAL_BASEPLATE
    l_pHoriParkingFloorPlanView->addAsset(l_pFloorPlate);
#endif
    l_pHoriParkingFloorPlanView->addAsset(l_pWheelSeparator_Horizontal);
    l_pHoriParkingFloorPlanView->setRenderOrder(osg::Camera::PRE_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_FLOOR);

    // horizontal parking plan view with SEPARATOR
    const auto l_pHoriSeparatorView = new cc::views::planview::PlanView(
        "Hori Parking Plan View with SEPARATOR",
        getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_horiParkingPlan,
        getFramework());

    l_pHoriSeparatorView->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_parking_hori / 2.,
        l_total_width_meters_parking_hori / 2.,
        -l_total_length_meters_parking_hori / 2.,
        l_total_length_meters_parking_hori / 2.);

    l_renderManagerPlanView->assignCamera(l_pHoriSeparatorView);

    l_pHoriSeparatorView->addAsset(l_pFloor);
#ifndef USE_TRADITIONAL_BASEPLATE
    l_pHoriSeparatorView->addAsset(l_pFloorPlate);
#endif
    l_pHoriSeparatorView->addAsset(l_pWheelSeparator_Horizontal);
    l_pHoriSeparatorView->setRenderOrder(osg::Camera::PRE_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_FLOOR);

#if ENABLE_VERTICAL_MODE
    // vertical parking plan view
    auto l_pVertParkingPlanView = new cc::views::planview::PlanView(
        "Vert Parking Plan View",
        getCorrectDriveHandSideViewport(g_views->m_vertParkingPlanViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertParkingPlan,
        getFramework());

    l_pVertParkingPlanView->setProjectionMatrixAsOrtho2D(
        -l_total_length_meters_parking_vert / 2.,
        l_total_length_meters_parking_vert / 2.,
        -l_total_width_meters_parking_vert / 2.,
        l_total_width_meters_parking_vert / 2.);

    l_pVertParkingPlanView->addAsset(l_pOutermostLines);
    // l_pVertParkingPlanView->addAsset(l_pFloor);
    // l_pVertParkingPlanView->addAsset(l_pVehicle2DIcon);
    l_pVertParkingPlanView->addAsset(new assets::uielements::Vehicle2DOverlay(
        getFramework()->asCustomFramework(), AssetId::EASSETS_VEHICLE_2D_ICON, l_pVertParkingPlanView));

#if USE_RADAR_WALL
#else
#endif
    l_pVertParkingPlanView->addAsset(l_pWheelTracks);
    //   l_pVertParkingPlanView->addAsset(l_pDigitalDisplay);
    // l_pVertParkingPlanView->addAsset(l_freeparkingManager);

#if USE_PARKSPACE_MARK
    l_pVertParkingPlanView->addAsset(l_pParkingPlanSymbol);
#endif

    // ! parking space on vertical plan view
    // auto l_pParkSpacePlanViewMat_Vert = new cc::views::planview::PlanViewCullCallback();
    // auto l_pParkingspacePlanView_Vert = new pc::core::Asset(
    //     AssetId::EASSETS_TV2D_PARKING_SPACE,
    //     new assets::parkingspace::ParkingSpace(
    //         getFramework()->asCustomFramework(),
    //         AssetId::EASSETS_TV2D_PARKING_SPACE,
    //         l_pVertParkingPlanView,
    //         g_views->m_vertParkingPlanViewport,
    //         false,
    //         l_pParkSpacePlanViewMat_Vert));

    // l_pVertParkingPlanView->addCullCallback(l_pParkSpacePlanViewMat_Vert);
    // l_pVertParkingPlanView->addAsset(l_pParkingspacePlanView_Vert);
    l_pVertParkingPlanView->setRenderOrder(osg::Camera::POST_RENDER, core::BYD_PLANVIEW_RENDERBIN_ORDER_VEHICLE_2D);

    // vertical parking floor plan view
    auto l_pVertParkingFloorPlanView = new cc::views::planview::PlanView(
        "Vert Parking Floor Plan View",
        getCorrectDriveHandSideViewport(g_views->m_vertParkingPlanViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertParkingPlan,
        getFramework());

    l_pVertParkingFloorPlanView->setProjectionMatrixAsOrtho2D(
        -l_total_length_meters_parking_vert / 2.,
        l_total_length_meters_parking_vert / 2.,
        -l_total_width_meters_parking_vert / 2.,
        l_total_width_meters_parking_vert / 2.);

    l_renderManagerHoriParkingPlanView->assignCamera(l_pVertParkingFloorPlanView);

    l_pVertParkingFloorPlanView->addAsset(l_pFloor);
#ifndef USE_TRADITIONAL_BASEPLATE
    l_pVertParkingFloorPlanView->addAsset(l_pFloorPlate);
#endif
    l_pVertParkingFloorPlanView->setRenderOrder(osg::Camera::PRE_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_FLOOR);

    // Vertical parking plan view with SEPARATOR
    auto l_pVertSeparatorView = new cc::views::planview::PlanView(
        "Vert Parking Plan View with SEPARATOR",
        getCorrectDriveHandSideViewport(g_views->m_vertMainViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertParkingPlan,
        getFramework());

    l_pVertSeparatorView->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_parking_vert / 2.,
        l_total_width_meters_parking_vert / 2.,
        -l_total_length_meters_parking_vert / 2.,
        l_total_length_meters_parking_vert / 2.);

    l_renderManagerPlanView->assignCamera(l_pVertSeparatorView);

    l_pVertSeparatorView->addAsset(l_pFloor);
#ifndef USE_TRADITIONAL_BASEPLATE
    l_pVertSeparatorView->addAsset(l_pFloorPlate);
#endif
    l_pVertSeparatorView->addAsset(l_pWheelSeparator_Vertical);
    l_pVertSeparatorView->setRenderOrder(osg::Camera::PRE_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_FLOOR);
#endif

    // create a fake plan view to display the vehicle impostor on LSMG screen
    pc::core::Viewport m_impostorViewport = g_views->m_planViewport;
    if (true == pc::vehicle::g_mechanicalData->m_leftHandDrive)
    {
        m_impostorViewport.m_origin.x() = g_views->m_planViewport.m_origin.x() + g_views->m_planViewport.m_size.x();
    }
    const auto l_pImpostorPlanView = new cc::views::planview::PlanView(
        "Impostor Plan View", m_impostorViewport, virtcam::g_positions->m_plan, getFramework());

    l_pImpostorPlanView->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters / 2., l_total_width_meters / 2., -l_total_length_meters / 2., l_total_length_meters / 2.);

    const auto l_pRefPlanViewMat = new cc::views::planview::PlanViewCullCallback();
    l_pImpostorPlanView->addCullCallback(l_pRefPlanViewMat);

    // l_pImpostorPlanView->setImpostor(l_pTV2D_CarImpostor);
    //  l_pImpostorPlanView->setWheels(l_pAllWheels);
    // l_pImpostorPlanView->addAsset(l_pVehicle2D);
    // l_pImpostorPlanView->addAsset(l_pVehicle2DIcon);
    l_pImpostorPlanView->addAsset(new assets::uielements::Vehicle2DOverlay(
        getFramework()->asCustomFramework(), AssetId::EASSETS_VEHICLE_2D_ICON, l_pImpostorPlanView));

    // {
    //   // render the vehicle doors on top of the impostor
    //   auto l_pDoorView = new pc::core::View(
    //       "Doors View",
    //       getCorrectDriveHandSideViewport(g_views->m_planViewport, g_views->m_usableCanvasViewport),
    //       virtcam::g_positions->m_plan);
    //   l_pDoorView->setProjectionMatrixAsOrtho2D(
    //       - l_total_width_meters /2., l_total_width_meters /2.,
    //       - l_total_length_meters /2. , l_total_length_meters /2.);
    //   l_pDoorView->addAsset(l_pVehicleDoors);
    //   l_pDoorView->setRenderOrder(osg::Camera::POST_RENDER);

    //   l_pVehicleDoors->getOrCreateStateSet()->setRenderBinDetails(cc::core::RENDERBIN_ORDER_TV2D_VEHICLE_DOORS,
    //   "RenderBin"); l_pVehicleDoors->getOrCreateStateSet()->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);

    //   l_pPlanView->setImpostorDoors(l_pDoorView);
    //   l_pImpostorPlanView->setImpostorDoors(l_pDoorView);

    //   // Vertical mode: render the vehicle doors on top of the impostor
    //   auto l_pVertDoorView = new pc::core::View(
    //       "Vert Doors View",
    //       getCorrectDriveHandSideViewport(g_views->m_vertPlanViewport, g_views->m_usableCanvasViewport),
    //       virtcam::g_positions->m_vertPlan);
    //   l_pVertDoorView->setProjectionMatrixAsOrtho2D(
    //       - l_total_length_meters /2., l_total_length_meters /2.,
    //       - l_total_width_meters /2. , l_total_width_meters /2.);
    //   l_pVertDoorView->addAsset(l_pVehicleDoors);
    //   l_pVertDoorView->setRenderOrder(osg::Camera::POST_RENDER);

    //   // l_pVehicleDoors->getOrCreateStateSet()->setRenderBinDetails(cc::core::RENDERBIN_ORDER_TV2D_VEHICLE_DOORS,
    //   "RenderBin");
    //   // l_pVehicleDoors->getOrCreateStateSet()->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);

    //   l_pVertPlanView->setImpostorDoors(l_pVertDoorView);
    // }

    //! give this view a unique StateSet whitout replacing its the default StateSet
    osg::StateSet* const l_PlanViewStateSet = new osg::StateSet(*l_pPlanView->getOrCreateStateSet());
    l_pPlanView->setStateSet(l_PlanViewStateSet);
    osg::Uniform* l_pAlphaUniformPlaView =
        l_PlanViewStateSet->getOrCreateUniform("VehicleTransparency", osg::Uniform::FLOAT);
    l_pAlphaUniformPlaView->set(1.0f); // PRQA S 3803

    //! give the Eng View LSMG (impostor) view a unique StateSet whitout replacing its the default StateSet
    osg::StateSet* const l_impostorPlanViewStateSet = new osg::StateSet(*l_pImpostorPlanView->getOrCreateStateSet());
    l_pImpostorPlanView->setStateSet(l_impostorPlanViewStateSet);
    l_pAlphaUniformPlaView = l_impostorPlanViewStateSet->getOrCreateUniform("VehicleTransparency", osg::Uniform::FLOAT);
    l_pAlphaUniformPlaView->set(1.0f); // PRQA S 3803

    // threat views
    // ***************************************************************************************************** The zoom
    // views will be realized with an ortho camera, so the world sizes for the vp are needed. For now hard-coded in the
    // SW.
    const osg::Vec2i l_vpSize = g_views->m_mainViewport.m_size;
    // const double length = 4.0; // corresponds to vehicle length
    // const double width = length * static_cast<double>(l_vpSize.x()) / static_cast<double>(l_vpSize.y());

#if USE_FISHEYE_VIEWS_SIDE

    // left view *******************************************************************************************************
    pc::views::warpfisheye::PartialUnfishModel* const l_pLeftModel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishLeft.data());
    pc::views::warpfisheye::PartialUnfishModel* const l_pRightModel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRight.data());
    pc::views::warpfisheye::PartialUnfishModel* const l_pSingleRearLeftModel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishSingleRearLeft.data());
    pc::views::warpfisheye::PartialUnfishModel* const l_pSingleRearRightModel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishSingleRearRight.data());
    pc::views::warpfisheye::PartialUnfishModel* const l_pLeftModel5x =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishLeft5x.data());
    pc::views::warpfisheye::PartialUnfishModel* const l_pRightModel5x =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRight5x.data());
    pc::views::warpfisheye::PartialUnfishModel* const l_pLeftRearModel5x =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRearLeft5x.data());
    pc::views::warpfisheye::PartialUnfishModel* const l_pRightRearModel5x =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRearRight5x.data());
    pc::views::warpfisheye::PartialUnfishModel* const l_pRemoteLeftModel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRemoteLeft.data());
    pc::views::warpfisheye::PartialUnfishModel* const l_pRemoteRightModel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRemoteRight.data());

//Top wheel views
    pc::views::warpfisheye::PartialUnfishModel* const l_pLeftModelWheel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishLeftWheel.data());
    pc::views::warpfisheye::PartialUnfishModel* const l_pRightModelWheel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRightWheel.data());
    pc::views::warpfisheye::PartialUnfishModel* const l_pLeftRearModelWheel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishLeftRearWheel.data());
    pc::views::warpfisheye::PartialUnfishModel* const l_pRightRearModelWheel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRightRearWheel.data());
#if ENABLE_VERTICAL_MODE
    pc::views::warpfisheye::PartialUnfishModel* l_pVertLeftModel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishLeftVert.data());
    pc::views::warpfisheye::PartialUnfishModel* l_pVertLeftModel5x =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishLeftVert5x.data());
    pc::views::warpfisheye::PartialUnfishModel* l_pVertRightModel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRightVert.data());
    pc::views::warpfisheye::PartialUnfishModel* l_pVertRightModel5x =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRightVert5x.data());

    cc::views::warpfisheye::CustomWarpFisheyeView* l_pVertLeftView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "Vertical leftView",
        getCorrectDriveHandSideViewport(g_views->m_vertMainViewport, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::LEFT_CAMERA,
        l_pVertLeftModel,
        l_vertLeftSettings.get(),
        l_leftCropBoundsVert.get());

    // Vertical mode right view
    // *******************************************************************************************************
    cc::views::warpfisheye::CustomWarpFisheyeView* l_pVertRightView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "Vertical rightView",
        getCorrectDriveHandSideViewport(g_views->m_vertMainViewport, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::RIGHT_CAMERA,
        l_pVertRightModel,
        l_vertRightSettings.get(),
        l_rightCropBoundsVert.get());

    cc::views::warpfisheye::CustomWarpFisheyeView* l_pVertSideLeftView =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "Vert Side Left View",
            getCorrectDriveHandSideViewport(g_views->m_vertSideViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::LEFT_CAMERA,
            l_pVertLeftModel5x,
            l_vertLeftSettings5x.get(),
            l_leftCropBoundsVert5x.get());

    // Vertical mode right view
    // *******************************************************************************************************
    cc::views::warpfisheye::CustomWarpFisheyeView* l_pVertSideRightView =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "Vert Side Right View",
            getCorrectDriveHandSideViewport(g_views->m_vertSideViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::RIGHT_CAMERA,
            l_pVertRightModel5x,
            l_vertRightSettings5x.get(),
            l_rightCropBoundsVert5x.get());

#endif
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pLeftView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "leftView",
        getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::LEFT_CAMERA,
        l_pLeftModel,
        l_leftSettings.get(),
        l_leftCropBoundsHori.get());

    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pLeftViewRear = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "leftViewRear",
        getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::LEFT_CAMERA,
        l_pSingleRearLeftModel,
        l_singleRearLeftSettings.get(),
        l_singleRearLeftCropBoundsHori.get());

    const auto l_pFrontCombinedCameraAsset = new pc::core::Asset(
        AssetId::EASSETS_COMBINED_FRONT_CAMERA_ASSET,
        new cc::views::combinedview::CombinedViewAsset(
            g_views->m_usableCanvasViewport.m_size.y(),
            getCorrectDriveHandSideViewport(g_views->m_fourWindowCombinedViewport, g_views->m_usableCanvasViewport),
            getCorrectDriveHandSideViewport(g_views->m_fourWindowRightViewport, g_views->m_usableCanvasViewport),
            getFramework()->asCustomFramework(),
            pc::core::sysconf::RIGHT_CAMERA,
            l_pRightModel,
            l_rightSettings.get(),
            l_rightCropBoundsHori.get(),
            getCorrectDriveHandSideViewport(g_views->m_fourWindowLeftViewport, g_views->m_usableCanvasViewport),
            pc::core::sysconf::LEFT_CAMERA,
            l_pLeftModel,
            l_leftSettings.get(),
            l_leftCropBoundsHori.get()));

    const auto l_pFrontCombinedLeftRightView = new pc::core::View(
        "Combined front left/right camera view",
        getCorrectDriveHandSideViewport(g_views->m_fourWindowCombinedViewport, g_views->m_usableCanvasViewport));

    l_pFrontCombinedLeftRightView->setProjectionMatrix(
        static_cast<cc::views::combinedview::CombinedViewAsset*>(l_pFrontCombinedCameraAsset->getAsset())
            ->getProjectionMatrix());
    l_pFrontCombinedLeftRightView->setViewMatrixAsLookAt(
        osg::Vec3f(-1.0f, 0.0f, 0.0f), osg::Vec3f(0.0f, 0.0f, 0.0f), osg::Vec3f(0.0f, 0.0f, 1.0f));

    l_pFrontCombinedLeftRightView->addAsset(l_pFrontCombinedCameraAsset);
    l_pFrontCombinedLeftRightView->setClearColor(osg::Vec4(0.0f, 0.0f, 0.0f, 1.0f));

    const auto l_pRearCombinedCameraAsset = new pc::core::Asset(
        AssetId::EASSETS_COMBINED_REAR_CAMERA_ASSET,
        new cc::views::combinedview::CombinedViewAsset(
            g_views->m_usableCanvasViewport.m_size.y(),
            getCorrectDriveHandSideViewport(g_views->m_fourWindowCombinedViewport, g_views->m_usableCanvasViewport),
            getCorrectDriveHandSideViewport(g_views->m_fourWindowRightViewport, g_views->m_usableCanvasViewport),
            getFramework()->asCustomFramework(),
            pc::core::sysconf::RIGHT_CAMERA,
            l_pRightModel,
            l_rearRightSettings.get(),
            l_rightRearCropBoundsHoriWheelBottom.get(),
            getCorrectDriveHandSideViewport(g_views->m_fourWindowLeftViewport, g_views->m_usableCanvasViewport),
            pc::core::sysconf::LEFT_CAMERA,
            l_pLeftModel,
            l_rearLeftSettings.get(),
            l_leftRearCropBoundsHoriWheelBottom.get(),
            false,
            true));

    const auto l_pRearCombinedLeftRightView = new pc::core::View(
        "Combined rear left/right camera view",
        getCorrectDriveHandSideViewport(g_views->m_fourWindowCombinedViewport, g_views->m_usableCanvasViewport));

    l_pRearCombinedLeftRightView->setProjectionMatrix(
        static_cast<cc::views::combinedview::CombinedViewAsset*>(l_pRearCombinedCameraAsset->getAsset())
            ->getProjectionMatrix());
    l_pRearCombinedLeftRightView->setViewMatrixAsLookAt(
        osg::Vec3f(-1.0f, 0.0f, 0.0f), osg::Vec3f(0.0f, 0.0f, 0.0f), osg::Vec3f(0.0f, 0.0f, 1.0f));

    // l_pRearCombinedLeftRightView->setProjectionMatrix(l_pRearCombinedLeftRightView->getProjectionMatrix() * l_mirrorTheWorld);

    l_pRearCombinedLeftRightView->addAsset(l_pRearCombinedCameraAsset);
    l_pRearCombinedLeftRightView->setClearColor(osg::Vec4(0.0f, 0.0f, 0.0f, 1.0f));

    // For left and right rear view in four viewports

    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pLeftRearView_5x =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "rearLeftView_5x",
            getCorrectDriveHandSideViewport(g_views->m_fourWindowLeftViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::LEFT_CAMERA,
            l_pRightRearModel5x,
            l_leftRearSettings5x.get(),
            l_leftRearCropBoundsHori5x.get());

    l_pLeftRearView_5x->setProjectionMatrix(l_pLeftRearView_5x->getProjectionMatrix() * l_mirrorTheWorld);

    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pRightRearView_5x =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "rearRightView_5x",
            getCorrectDriveHandSideViewport(g_views->m_fourWindowRightViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::RIGHT_CAMERA,
            l_pLeftRearModel5x,
            l_rightRearSettings5x.get(),
            l_rightRearCropBoundsHori5x.get());

    l_pRightRearView_5x->setProjectionMatrix(l_pRightRearView_5x->getProjectionMatrix() * l_mirrorTheWorld);

    // right view
    // *******************************************************************************************************
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pRightView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "rightView",
        getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::RIGHT_CAMERA,
        l_pRightModel,
        l_rightSettings.get(),
        l_rightCropBoundsHori.get());

    // Rear right view
    // *******************************************************************************************************
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pRightViewRear = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "rightViewRear",
        getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::RIGHT_CAMERA,
        l_pSingleRearRightModel,
        l_singleRearRightSettings.get(),
        l_singleRearRightCropBoundsHori.get());

    // Remote Left View
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pRemoteLeftView =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "Remote leftView",
            getCorrectDriveHandSideViewport(g_views->m_remoteMainViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::LEFT_CAMERA,
            l_pRemoteLeftModel,
            l_remoteLeftSettings.get(),
            l_remoteLeftCropBoundsHori.get());
    l_pRemoteLeftView->addAsset(l_pTimeshowOverlay);
    // Remote right view
    // *******************************************************************************************************
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pRemoteRightView =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "Remote rightView",
            getCorrectDriveHandSideViewport(g_views->m_remoteMainViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::RIGHT_CAMERA,
            l_pRemoteRightModel,
            l_remoteRightSettings.get(),
            l_remoteRightCropBoundsHori.get());
    l_pRemoteRightView->addAsset(l_pTimeshowOverlay);

#else
    auto l_pLeftView = new pc::core::View(
        "leftView",
        getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_leftView);
    l_pLeftView->addAsset(l_pSingleCamLeft);

    // right view
    // *******************************************************************************************************
    auto l_pRightView = new pc::core::View(
        "rightView",
        getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_rightView);
    l_pRightView->addAsset(l_pSingleCamRight);

#if ENABLE_VERTICAL_MODE
    // Vertical mode left view
    // *******************************************************************************************************
    auto l_pVertLeftView = new pc::core::View(
        "Vertical leftView",
        getCorrectDriveHandSideViewport(g_views->m_vertSideViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertLeftView);
    l_pVertLeftView->addAsset(l_pSingleCamLeft);

    // Vertical mode right view
    // *******************************************************************************************************
    auto l_pVertRightView = new pc::core::View(
        "Vertical rightView",
        getCorrectDriveHandSideViewport(g_views->m_vertSideViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertRightView);
    l_pVertRightView->addAsset(l_pSingleCamRight);
#endif
#endif

    // Image in image mode
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pImageinImage_Left =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "ImageinImage left",
            getCorrectDriveHandSideViewport(g_views->m_imageInimageLeftViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::LEFT_CAMERA,
            l_pLeftModel5x,
            l_leftSettings5x.get(),
            l_leftCropBoundsHori5x.get());

    l_pImageinImage_Left->setProjectionMatrix(l_pImageinImage_Left->getProjectionMatrix() * l_mirrorTheWorld);

    // Image in image mode
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pImageinImage_Right =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "ImageinImage right",
            getCorrectDriveHandSideViewport(g_views->m_imageInimageRightViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::RIGHT_CAMERA,
            l_pRightModel5x,
            l_rightSettings5x.get(),
            l_rightCropBoundsHori5x.get());

    l_pImageinImage_Right->setProjectionMatrix(l_pImageinImage_Right->getProjectionMatrix() * l_mirrorTheWorld);

    const auto l_pImageinImagePlanView = new cc::views::planview::PlanView(
        "Image in Image Plan View",
        getCorrectDriveHandSideViewport(g_views->m_imageInimagePlanviewViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pImageinImagePlanView->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_imageInImage / 2.,
        l_total_width_meters_imageInImage / 2.,
        -l_total_length_meters_imageInImage / 2.,
        l_total_length_meters_imageInImage / 2.);

    l_renderManagerPlanView->assignCamera(l_pImageinImagePlanView);

    l_pImageinImagePlanView->addAsset(l_pFloor);
#ifndef USE_TRADITIONAL_BASEPLATE
    l_pImageinImagePlanView->addAsset(l_pFloorPlate);
#endif
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pImageinImage_LeftRear =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "ImageinImage left",
            getCorrectDriveHandSideViewport(g_views->m_imageInimageLeftViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::LEFT_CAMERA,
            l_pLeftRearModel5x,
            l_leftRearSettings5x.get(),
            l_leftRearCropBoundsHori5x.get());

    l_pImageinImage_LeftRear->setProjectionMatrix(l_pImageinImage_LeftRear->getProjectionMatrix() * l_mirrorTheWorld);

    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pImageinImage_RightRear =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "ImageinImage right",
            getCorrectDriveHandSideViewport(g_views->m_imageInimageRightViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::RIGHT_CAMERA,
            l_pRightRearModel5x,
            l_rightRearSettings5x.get(),
            l_rightRearCropBoundsHori5x.get());

    l_pImageinImage_RightRear->setProjectionMatrix(l_pImageinImage_RightRear->getProjectionMatrix() * l_mirrorTheWorld);
    const auto l_pImageinImagePlanViewFullscreenUSSOverlay = new cc::views::planview::PlanView(
        "Image in Image Plan View Fullscreen USS Overlay",
        getCorrectDriveHandSideViewport(g_views->m_imageInimagePlanviewViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pImageinImagePlanViewFullscreenUSSOverlay->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_imageInImage / 2.,
        l_total_width_meters_imageInImage / 2.,
        -l_total_length_meters_imageInImage / 2.,
        l_total_length_meters_imageInImage / 2.);

    l_pImageinImagePlanViewFullscreenUSSOverlay->addAsset(l_distanceOverlay);

#ifndef USE_RADAR_WALL
    l_pImageinImagePlanViewFullscreenUSSOverlay->addAsset(l_pObstacleOverlay_vehOffset);
#else
    if(g_customerScene->m_objectWallVariant == 1)
    {
        l_pImageinImagePlanViewFullscreenUSSOverlay->addAsset(l_obstacleOverlay);
    }
    else if (g_customerScene->m_objectWallVariant == 2)
    {
        l_pImageinImagePlanViewFullscreenUSSOverlay->addAsset(l_pdcOverlay);
    }
    else
    {
        // add nothing
    }
#endif
    // l_pImageinImagePlanViewFullscreenUSSOverlay->addAsset(l_pDigitalDisplay);
    l_pImageinImagePlanViewFullscreenUSSOverlay->setRenderOrder(
        osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_USS);

    // Both wheel left view
    // *******************************************************************************************************
    const auto l_pBothWheelLeftView = new pc::core::View(
        "Both Wheel Left View",
        getCorrectDriveHandSideViewport(g_views->m_frontWheelLeft, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_bothWheelLeft);
    l_pBothWheelLeftView->addAsset(l_pSingleCamLeft);
    if (l_rimProtectionLine != nullptr){
        l_pBothWheelLeftView->addAsset(l_rimProtectionLine);
    }

    // set the offset in the middle of viewport
    constexpr vfc::float32_t l_additionalViewportOffsetBothWheel = 0.7f;
    osg::Matrix          l_viewportOffsetMatrixBothWheelLeft;
    osg::Matrix          l_viewportOffsetMatrixBothWheelRight;
    l_viewportOffsetMatrixBothWheelLeft.makeTranslate(osg::Vec3f(l_additionalViewportOffsetBothWheel, 0.0f, 0.0f));
    l_viewportOffsetMatrixBothWheelRight.makeTranslate(osg::Vec3f(-l_additionalViewportOffsetBothWheel, 0.0f, 0.0f));

    l_pBothWheelLeftView->setProjectionMatrix(
        l_pBothWheelLeftView->getProjectionMatrix() * l_viewportOffsetMatrixBothWheelLeft);

    // Both wheel right view
    // *******************************************************************************************************
    const auto l_pBothWheelRightView = new pc::core::View(
        "Both Wheel Right View",
        getCorrectDriveHandSideViewport(g_views->m_frontWheelRight, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_bothWheelRight);

    l_pBothWheelRightView->addAsset(l_pSingleCamRight);
    l_pBothWheelRightView->setProjectionMatrix(
        l_pBothWheelRightView->getProjectionMatrix() * l_viewportOffsetMatrixBothWheelRight);

    if (l_rimProtectionLine != nullptr){
        l_pBothWheelRightView->addAsset(l_rimProtectionLine);
    }

    // front wheel left view
    // *******************************************************************************************************
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pFrontWheelLeftView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "Front Wheel Left View",
        getCorrectDriveHandSideViewport(g_views->m_frontWheelLeft, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::LEFT_CAMERA,
        l_pLeftModelWheel,
        l_leftWheelSettings.get(),
        l_leftCropBoundsHoriWheel.get());

    // front wheel right view
    // *******************************************************************************************************
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pFrontWheelRightView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "Front Wheel Right View",
        getCorrectDriveHandSideViewport(g_views->m_frontWheelRight, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::RIGHT_CAMERA,
        l_pRightModelWheel,
        l_rightWheelSettings.get(),
        l_rightCropBoundsHoriWheel.get());

    // rear wheel left view
    // ******************************************************************************************************
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pRearWheelLeftView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "Rear Wheel Left View",
        getCorrectDriveHandSideViewport(g_views->m_rearWheelLeft, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::LEFT_CAMERA,
        l_pLeftRearModelWheel,
        l_leftRearWheelSettings.get(),
        l_leftRearCropBoundsHoriWheel.get());

    l_pRearWheelLeftView->setProjectionMatrix(l_pRearWheelLeftView->getProjectionMatrix() * l_mirrorTheWorld);

    // rear wheel right view
    // *******************************************************************************************************
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pRearWheelRightView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "Rear Wheel Right View",
        getCorrectDriveHandSideViewport(g_views->m_rearWheelRight, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::RIGHT_CAMERA,
        l_pRightRearModelWheel,
        l_rightRearWheelSettings.get(),
        l_rightRearCropBoundsHoriWheel.get());


    l_pRearWheelRightView->setProjectionMatrix(l_pRearWheelRightView->getProjectionMatrix() * l_mirrorTheWorld);

    // Both wheel left Enlarged view
    // *******************************************************************************************************
    const auto l_pBothWheelLeftEnlargedView = new pc::core::View(
        "Both Wheel Left Enlarged View",
        getCorrectDriveHandSideViewport(g_views->m_frontWheelLeftEnlarged, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_bothWheelLeft);
    l_pBothWheelLeftEnlargedView->addAsset(l_pSingleCamLeft);
    if (l_rimProtectionLine != nullptr){
        l_pBothWheelLeftEnlargedView->addAsset(l_rimProtectionLine);
    }

    // set the offset in the middle of viewport
    constexpr vfc::float32_t l_additionalViewportOffsetBothWheelEnlarged = 0.7f;
    osg::Matrix          l_viewportOffsetMatrixBothWheelLeftEnlarged;
    osg::Matrix          l_viewportOffsetMatrixBothWheelRightEnlarged;
    l_viewportOffsetMatrixBothWheelLeftEnlarged.makeTranslate(osg::Vec3f(l_additionalViewportOffsetBothWheelEnlarged, 0.0f, 0.0f));
    l_viewportOffsetMatrixBothWheelRightEnlarged.makeTranslate(osg::Vec3f(-l_additionalViewportOffsetBothWheelEnlarged, 0.0f, 0.0f));

    l_pBothWheelLeftEnlargedView->setProjectionMatrix(
        l_pBothWheelLeftEnlargedView->getProjectionMatrix() * l_viewportOffsetMatrixBothWheelLeftEnlarged);

    // Both wheel right enlarged view
    // *******************************************************************************************************
    const auto l_pBothWheelRightEnlargedView = new pc::core::View(
        "Both Wheel Right Enlarged View",
        getCorrectDriveHandSideViewport(g_views->m_frontWheelRightEnlarged, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_bothWheelRight);

    l_pBothWheelRightEnlargedView->addAsset(l_pSingleCamRight);
    l_pBothWheelRightEnlargedView->setProjectionMatrix(
        l_pBothWheelRightEnlargedView->getProjectionMatrix() * l_viewportOffsetMatrixBothWheelRightEnlarged);

    if (l_rimProtectionLine != nullptr){
        l_pBothWheelRightEnlargedView->addAsset(l_rimProtectionLine);
    }

    // front wheel left enlarged view
    // *******************************************************************************************************
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pFrontWheelLeftEnlargedView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "Front Wheel Left Enlarged View",
        getCorrectDriveHandSideViewport(g_views->m_frontWheelLeftEnlarged, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::LEFT_CAMERA,
        l_pLeftModelWheel,
        l_leftWheelSettings.get(),
        l_leftCropBoundsHoriWheel.get());

    // front wheel right enlarged view
    // *******************************************************************************************************
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pFrontWheelRightEnlargedView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "Front Wheel Right Enlarged View",
        getCorrectDriveHandSideViewport(g_views->m_frontWheelRightEnlarged, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::RIGHT_CAMERA,
        l_pRightModelWheel,
        l_rightWheelSettings.get(),
        l_rightCropBoundsHoriWheel.get());

    // rear wheel left enlarged view
    // ******************************************************************************************************
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pRearWheelLeftEnlargedView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "Rear Wheel Left Enlarged View",
        getCorrectDriveHandSideViewport(g_views->m_rearWheelLeftEnlarged, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::LEFT_CAMERA,
        l_pLeftRearModelWheel,
        l_leftRearWheelSettings.get(),
        l_leftRearCropBoundsHoriWheel.get());

    l_pRearWheelLeftEnlargedView->setProjectionMatrix(l_pRearWheelLeftEnlargedView->getProjectionMatrix() * l_mirrorTheWorld);

    // rear wheel right view
    // *******************************************************************************************************
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pRearWheelRightEnlargedView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "Rear Wheel Right Enlarged View",
        getCorrectDriveHandSideViewport(g_views->m_rearWheelRightEnlarged, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::RIGHT_CAMERA,
        l_pRightRearModelWheel,
        l_rightRearWheelSettings.get(),
        l_rightRearCropBoundsHoriWheel.get());


    l_pRearWheelRightEnlargedView->setProjectionMatrix(l_pRearWheelRightEnlargedView->getProjectionMatrix() * l_mirrorTheWorld);
#if ENABLE_VERTICAL_MODE
    // vertical front wheel left view
    auto l_pVertFrontWheelLeftView = new pc::core::View(
        "Vertical Front Wheel Left View",
        getCorrectDriveHandSideViewport(g_views->m_vertWheelLeftViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertFrontWheelLeftView);

    l_pVertFrontWheelLeftView->addAsset(l_pSingleCamLeft);
    osg::Matrix l_viewportOffsetMatrixVertLeft;
    l_viewportOffsetMatrixVertLeft.makeTranslate(osg::Vec3f(0.0f, l_additionalViewportOffset, 0.0f));
    l_pVertFrontWheelLeftView->setProjectionMatrix(
        l_pVertFrontWheelLeftView->getProjectionMatrix() * l_viewportOffsetMatrixVertLeft);
    if (l_rimProtectionLine != nullptr){
        l_pVertFrontWheelLeftView->addAsset(l_rimProtectionLine);
    }

    // vertical front wheel right view
    auto l_pVertFrontWheelRightView = new pc::core::View(
        "Vertical Front Wheel Right View",
        getCorrectDriveHandSideViewport(g_views->m_vertWheelRightViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertFrontWheelRightView);

    l_pVertFrontWheelRightView->addAsset(l_pSingleCamRight);
    osg::Matrix l_viewportOffsetMatrixVertRight;
    l_viewportOffsetMatrixVertRight.makeTranslate(osg::Vec3f(0.0f, -l_additionalViewportOffset, 0.0f));
    l_pVertFrontWheelRightView->setProjectionMatrix(
        l_pVertFrontWheelRightView->getProjectionMatrix() * l_viewportOffsetMatrixVertRight);
    l_pVertFrontWheelRightView->addAsset(l_rimProtectionLine);

    // vertical front wheel left view
    auto l_pVertWheelLeftView = new pc::core::View(
        "Vertical Wheel Left View",
        getCorrectDriveHandSideViewport(g_views->m_vertWheelLeftViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertWheelLeftView);

    l_pVertWheelLeftView->addAsset(l_pSingleCamLeft);
    l_pVertWheelLeftView->setProjectionMatrix(
        l_pVertWheelLeftView->getProjectionMatrix() * l_viewportOffsetMatrixVertLeft);
    if (l_rimProtectionLine != nullptr){
        l_pVertWheelLeftView->addAsset(l_rimProtectionLine);
    }

    // vertical front wheel right view
    auto l_pVertWheelRightView = new pc::core::View(
        "Vertical Wheel Right View",
        getCorrectDriveHandSideViewport(g_views->m_vertWheelRightViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertWheelRightView);

    l_pVertWheelRightView->addAsset(l_pSingleCamRight);
    l_pVertWheelRightView->setProjectionMatrix(
        l_pVertWheelRightView->getProjectionMatrix() * l_viewportOffsetMatrixVertRight);
    if (l_rimProtectionLine != nullptr){
        l_pVertWheelRightView->addAsset(l_rimProtectionLine);
    }

    // vertical rear wheel left view
    auto l_pVertRearWheelLeftView = new pc::core::View(
        "Vertical Rear Wheel Left View",
        getCorrectDriveHandSideViewport(g_views->m_vertWheelLeftViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertRearWheelLeftView);

    l_pVertRearWheelLeftView->addAsset(l_pSingleCamLeft);
    l_pVertRearWheelLeftView->setProjectionMatrix(
        l_pVertRearWheelLeftView->getProjectionMatrix() * l_mirrorTheWorld * l_viewportOffsetMatrixVertLeft);
    if (l_rimProtectionLine != nullptr){
        l_pVertRearWheelLeftView->addAsset(l_rimProtectionLine);
    }

    // vertical rear wheel right view
    auto l_pVertRearWheelRightView = new pc::core::View(
        "Vertical Rear Wheel Right View",
        getCorrectDriveHandSideViewport(g_views->m_vertWheelRightViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertRearWheelRightView);

    l_pVertRearWheelRightView->addAsset(l_pSingleCamRight);
    l_pVertRearWheelRightView->setProjectionMatrix(
        l_pVertRearWheelRightView->getProjectionMatrix() * l_mirrorTheWorld * l_viewportOffsetMatrixVertRight);
    if (l_rimProtectionLine != nullptr){
        l_pVertRearWheelRightView->addAsset(l_rimProtectionLine);
    }

#endif

    // Rear Junction View
    // ************************************************************************************************
#if 0
  // auto l_pRearJunctionView = new views::panoramaview::PanoramaView(
  //   getFramework(),
  //   views::panoramaview::PanoramaView::PANORAMAVIEW_REAR,
  //   "Rear Junction View",
  //   getCorrectDriveHandSideViewport(g_views->m_usableCanvasViewport, g_views->m_usableCanvasViewport));

    auto l_pRearJunctionView = new views::panoramaviewtriple::PanoramaViewTriple(
    getFramework(),
    views::panoramaviewtriple::PanoramaViewTriple::PANORAMAVIEW_REAR,
    "Rear Junction View",
    getCorrectDriveHandSideViewport(g_views->m_usableCanvasViewport, g_views->m_usableCanvasViewport));
#else
    // pc::views::warpfisheye::StereoGraphicModel* l_pRearModel = new
    // pc::views::warpfisheye::StereoGraphicModel(l_rearSettings->m_horizontalHalfFov);
    // pc::views::warpfisheye::StereoGraphicModel* l_pRearModel = new
    // pc::views::warpfisheye::StereoGraphicModel(l_rearModelSettings);
    // pc::views::warpfisheye::PinholeModel* l_pRearModel = new
    // pc::views::warpfisheye::PinholeModel(l_rearSettings->m_horizontalHalfFov);

    // pc::views::warpfisheye::StereoGraphicModel* l_pRearJunctionModel =
    //     new pc::views::warpfisheye::StereoGraphicModel(l_partUnfishRearPano->m_modelSettings);
    // pc::views::warpfisheye::PartialUnfishModel* l_pRearJunctionModel = new
    // pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRearPano);

    // cc::views::warpfisheye::CustomWarpFisheyeView* l_pRearJunctionView =
    //     new cc::views::warpfisheye::CustomWarpFisheyeView(
    //         "Rear Junction View",
    //         getCorrectDriveHandSideViewport(g_views->m_cpcViewport, g_views->m_usableCanvasViewport),
    //         getFramework(),
    //         pc::core::sysconf::REAR_CAMERA,
    //         l_pRearJunctionModel,
    //         l_rearPanoSettings.get(),
    //         l_rearPanoCropBoundsHori.get());

    // l_pRearJunctionView->setProjectionMatrix(l_pRearJunctionView->getProjectionMatrix() * l_mirrorTheWorld);
    // // l_pRearJunctionView->addAsset(l_pRearTrailerAssistLine);
    // l_pRearJunctionView->setRenderOrder(
    //     osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_ABOVE_VEHICLE_2D);

    // auto l_pRearJunctionViewTopCallback = new cc::views::warpfisheye::TopViewPortEnableCallback(
    //     getFramework()->asCustomFramework(),
    //     cc::views::warpfisheye::ETopViewPortCameraIndex::RearJunction_CAMERA_ENABLE);
    // l_pRearJunctionView->addCullCallback(l_pRearJunctionViewTopCallback);

#if ENABLE_VERTICAL_MODE
    // ! Vertical rear junction view
    pc::views::warpfisheye::PartialUnfishModel* l_pRearJunctionModelVert =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRearPanoVert.data());

    cc::views::warpfisheye::CustomWarpFisheyeView* l_pVertRearJunctionView =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "Vertical Rear Junction View",
            getCorrectDriveHandSideViewport(g_views->m_vertFullScreenViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::REAR_CAMERA,
            l_pRearJunctionModelVert,
            l_vertRearPanoSettings.get(),
            l_rearPanoCropBoundsVert.get());

    l_pVertRearJunctionView->setProjectionMatrix(l_pVertRearJunctionView->getProjectionMatrix() * l_mirrorTheWorld);

    l_pVertRearJunctionView->setRenderOrder(
        osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_ABOVE_VEHICLE_2D);

    // ! Vertical front junction view
    pc::views::warpfisheye::PartialUnfishModel* l_pFrontJunctionModelVert =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishFrontPanoVert.data());

    cc::views::warpfisheye::CustomWarpFisheyeView* l_pVertFrontJunctionView =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "Vertical Front Junction View",
            getCorrectDriveHandSideViewport(g_views->m_vertFullScreenViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pFrontJunctionModelVert,
            l_vertFrontPanoSettings.get(),
            l_frontPanoCropBoundsVert.get());

    l_pVertFrontJunctionView->setRenderOrder(
        osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_ABOVE_VEHICLE_2D);

#endif // ENABLE_VERTICAL_MODE
#endif

// Front Junction View ***********************************************************************************************
#if 0

  // auto l_pFrontJunctionView = new views::panoramaview::PanoramaView(
  //   getFramework(),
  //   views::panoramaview::PanoramaView::PANORAMAVIEW_FRONT,
  //   "Front Junction View",
  //   getCorrectDriveHandSideViewport(g_views->m_usableCanvasViewport, g_views->m_usableCanvasViewport));

  auto l_pFrontJunctionView = new views::panoramaviewtriple::PanoramaViewTriple(
    getFramework(),
    views::panoramaviewtriple::PanoramaViewTriple::PANORAMAVIEW_FRONT,
    "Front Junction View",
    getCorrectDriveHandSideViewport(g_views->m_usableCanvasViewport, g_views->m_usableCanvasViewport));
#else
    // pc::views::warpfisheye::StereoGraphicModel* l_pFrontModel = new
    // pc::views::warpfisheye::StereoGraphicModel(l_frontSettings->m_horizontalHalfFov);
    // pc::views::warpfisheye::StereoGraphicModel* l_pFrontModel = new
    // pc::views::warpfisheye::StereoGraphicModel(l_frontModelSettings);
    // pc::views::warpfisheye::PinholeModel* l_pRearModel = new
    // pc::views::warpfisheye::PinholeModel(l_rearSettings->m_horizontalHalfFov);

    // pc::views::warpfisheye::StereoGraphicModel* l_pFrontJunctionModel =
    //     new pc::views::warpfisheye::StereoGraphicModel(l_partUnfishFrontPano->m_modelSettings);
    // pc::views::warpfisheye::PartialUnfishModel* l_pFrontJunctionModel = new
    // pc::views::warpfisheye::PartialUnfishModel(l_partUnfishFrontPano);

    // cc::views::warpfisheye::CustomWarpFisheyeView* l_pFrontJunctionView =
    //     new cc::views::warpfisheye::CustomWarpFisheyeView(
    //         "Front Junction View",
    //         getCorrectDriveHandSideViewport(g_views->m_cpcViewport, g_views->m_usableCanvasViewport),
    //         getFramework(),
    //         pc::core::sysconf::FRONT_CAMERA,
    //         l_pFrontJunctionModel,
    //         l_frontPanoSettings.get(),
    //         l_frontPanoCropBoundsHori.get());

    // l_pFrontJunctionView->setRenderOrder(
    //     osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_ABOVE_VEHICLE_2D);

    // auto l_pFrontJunctionViewTopCallback = new cc::views::warpfisheye::TopViewPortEnableCallback(
    //     getFramework()->asCustomFramework(),
    //     cc::views::warpfisheye::ETopViewPortCameraIndex::FrontJunction_CAMERA_ENABLE);
    // l_pFrontJunctionView->addCullCallback(l_pFrontJunctionViewTopCallback);

#endif

    // parking mode select view
    // ***************************************************************************************************
    // auto l_pParkingModeSelectView = new pc::core::View("Parking Mode Select View", g_views->m_usableCanvasViewport);

    // auto l_pParkingModeSelect = new pc::core::Asset( AssetId::EASSETS_UI_PARKING_MODE,
    //                       new assets::uielements::ParkingMode(getFramework()->asCustomFramework(),
    //                       AssetId::EASSETS_UI_PARKING_MODE));

    // l_pParkingModeSelectView->addAsset(l_pParkingModeSelect);
    // l_pParkingModeSelectView->addAsset(l_pQuitButton);
    // l_pParkingModeSelectView->addAsset(l_pBackground);

    const auto l_pParkingView = new pc::core::View(
        "Parking UI View",
        getCorrectDriveHandSideViewport(g_views->m_apaPlanViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_horiParkingPlan);
#if ENABLE_VERTICAL_MODE
    auto l_pParkingViewVert = new pc::core::View(
        "Parking UI View Vert",
        getCorrectDriveHandSideViewport(g_views->m_vertParkingUIViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_horiParkingPlan);
#endif

    m_augmentedViewTransitionHori =
        new cc::assets::augmentedview::AugmentedViewTransition(getFramework(), cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI);
//    const auto l_pAugmentedViewTransitionHori =
//        new pc::core::Asset(AssetId::EASSETS_AUGMENTED_VIEW_TRANSITION, m_augmentedViewTransitionHori);

    m_augmentedViewTransitionVert =
        new cc::assets::augmentedview::AugmentedViewTransition(getFramework(), cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT);
//    const auto l_pAugmentedViewTransitionVert =
//        new pc::core::Asset(AssetId::EASSETS_AUGMENTED_VIEW_TRANSITION_VERT, m_augmentedViewTransitionVert);

    // auto l_pParkingViewElement = new pc::core::Asset( AssetId::EASSETS_UI_PARKING_SEARCHING,
    //                       new assets::uielements::ParkingView(getFramework()->asCustomFramework(),
    //                       AssetId::EASSETS_UI_PARKING_SEARCHING));

    // auto l_pParkingViewBackground = new pc::core::Asset( AssetId::EASSETS_UI_PARKING_BACKGROUND,
    //                       new assets::uielements::ParkingViewBackground(getFramework()->asCustomFramework(),
    //                       AssetId::EASSETS_UI_PARKING_BACKGROUND));

    // l_pParkingView->addAsset(l_pParkingViewBackground);
    // l_pParkingView->addAsset(l_pParkingViewElement);
    // l_pParkingView->addAsset(l_pSpeedOverlay);

    // l_pParkingView->addAsset(l_startPauseConfirmButton);
    // l_startPauseConfirmButton->setHoriReferenceView(l_pParkingView);

    // l_pParkingView->addAsset(l_continueButton);
    // l_continueButton->setHoriReferenceView(l_pParkingView);

    // l_pParkingView->addAsset(l_quitButton);
    // l_quitButton->setHoriReferenceView(l_pParkingView);

    // l_pParkingView->addAsset(l_parkoutLeftButton);
    // l_parkoutLeftButton->setHoriReferenceView(l_pParkingView);

    // l_pParkingView->addAsset(l_parkoutRightButton);
    // l_parkoutRightButton->setHoriReferenceView(l_pParkingView);
#if ENABLE_VERTICAL_MODE
    // l_pParkingViewVert->addAsset(l_pParkingViewBackground);
    // l_pParkingViewVert->addAsset(l_pParkingViewElement);
    l_pParkingViewVert->addAsset(l_pSpeedOverlay);

    // l_pParkingViewVert->addAsset(l_startPauseConfirmButton);
    // l_startPauseConfirmButton->setVertReferenceView(l_pParkingViewVert);

    // l_pParkingViewVert->addAsset(l_continueButton);
    // l_continueButton->setVertReferenceView(l_pParkingViewVert);

    // l_pParkingViewVert->addAsset(l_quitButton);
    // l_quitButton->setVertReferenceView(l_pParkingViewVert);

    // l_pParkingViewVert->addAsset(l_parkoutLeftButton);
    // l_parkoutLeftButton->setVertReferenceView(l_pParkingViewVert);

    // l_pParkingViewVert->addAsset(l_parkoutRightButton);
    // l_parkoutRightButton->setVertReferenceView(l_pParkingViewVert);
#endif

    // auto l_dynamicGearView = new pc::core::View(
    //     "Gear UI View",
    //     getCorrectDriveHandSideViewport(g_views->m_usableCanvasViewport, g_views->m_usableCanvasViewport),
    //     virtcam::g_positions->m_horiParkingPlan);

    // l_dynamicGearView->setProjectionMatrixAsOrtho2D(
    //     0.0,
    //     static_cast<vfc::float64_t>(g_views->m_usableCanvasViewport.m_size.x()),
    //     0.0,
    //     static_cast<vfc::float64_t>(g_views->m_usableCanvasViewport.m_size.y()));

    // l_dynamicGearView->addAsset(l_pDynamicGearOverlays);
    // l_dynamicGearView->addAsset(l_pDynamicDistance);

    constexpr vfc::float32_t     l_scalingFactor = 2.0f / 3.0f;
//    pc::core::UpscalingData* const l_upscalingData = new pc::core::UpscalingData(l_scalingFactor, g_views->m_planViewport);

    // auto l_drivablePath = new pc::core::Asset( AssetId::EASSETS_DRIVABLE_PATH, new
    // cc::assets::drivablepath::DrivablePathAsset(getFramework()->asCustomFramework(),
    // AssetId::EASSETS_DRIVABLE_PATH,g_trajParams)); parking searching view
    // ******************************************************************************************************
    // auto l_pParkSearchView = new cc::views::parkview::ParkInView(
    //     l_upscalingData,
    //     g_views->m_apaPlanViewport,
    //     g_views->m_vertParkingUIViewport,
    //     virtcam::g_positions->m_virtualParkSearching,
    //     virtcam::g_positions->m_vertVirtualParkSearching,
    //     getFramework(),
    //     AssetId::EASSETS_PARKINGSPOTS);

    // auto l_virtualCameraUpdater = new cc::virtcam::VirtualCameraUpdater(getFramework());
    // l_virtualCameraUpdater->setHemisphereCenter(
    //     osg::Vec3f(pc::vehicle::g_mechanicalData->getCenter(), 0.0f) + cc::virtcam::g_carCenter->m_carCenterHori);
    // l_pParkSearchView->setCameraUpdater(l_virtualCameraUpdater);

    // auto l_pParkingSpotManager     = new pc::core::Asset( AssetId::EASSETS_PARKINGSPOTS, new
    // assets::parkingspots::ParkingSpotManager(getFramework()));
    // // auto l_pStreetOverlay          = new pc::core::Asset( AssetId::EASSETS_STREETOVERLAY, new
    // assets::streetoverlay::StreetOverlay(getFramework()));
#ifdef USE_VIRTUAL_OBJECT_SEARCHING
    // auto l_pVirtualRealityManager = new pc::core::Asset(
    //     AssetId::EASSETS_VIRTUAL_REALITY, new assets::virtualreality::VirtualRealityManager(getFramework()));
    // l_pParkSearchView->addAsset(l_pVirtualRealityManager);
#else
    // auto l_pParkingSpotManager = new pc::core::Asset(
    //     AssetId::EASSETS_PARKINGSPOTS, new assets::parkingspots::ParkingSpotManager(getFramework()));
    // l_pParkSearchView->addAsset(l_pParkingSpotManager);
#endif
    // const auto l_pE3ParkingOverlay = new cc::assets::E3ParkingAsset(AssetId::EASSETS_UI_PARKING_E3, getFramework()->asCustomFramework(), g_views->m_apaPlanViewport);



    // m_augmentedViewTransitionHori->registerWithView(l_pParkSearchView);
    // m_augmentedViewTransitionVert->registerWithView(l_pParkSearchView);

    // l_pParkSearchView->addAsset(l_pAugmentedViewTransitionHori, true);
    // l_pParkSearchView->addAsset(l_pAugmentedViewTransitionVert, true);

    // l_pParkSearchView->addAsset(l_pLowpolyVehicle);
    // l_pParkSearchView->addAsset(l_pVehicle);

    // l_pParkSearchView->addAsset(l_pStreetOverlay);
    // l_pParkSearchView->addAsset(l_pParkingSpotManager);
    // l_pParkSearchView->addAsset(l_pParkingSearchingElements);
    // l_pParkSearchView->addAsset(l_pArrowTrajectory);
    // l_pParkSearchView->addAsset(l_pParkingSlotManager);

    // auto l_parkoutButtonGroup = new cc::assets::button::parkout::ParkoutButtonGroup{
    //     AssetId::EASSETS_UI_PARKOUT_LEFT_BUTTON, l_pParkSearchView, getFramework()};
    // l_pParkSearchView->addAsset(l_parkoutButtonGroup);

    // rpa guidance View **************************************************************************************
    // auto l_pParkingRPAGuidanceView = new pc::core::View("Parking RPA Guidance View",
    // g_views->m_usableCanvasViewport);

    // auto l_pParkingRPAGuidance = new pc::core::Asset( AssetId::EASSETS_RPA_GUIDANCE,
    //                       new assets::uielements::ParkingRPAGuidance(getFramework()->asCustomFramework(),
    //                       AssetId::EASSETS_RPA_GUIDANCE));

    // l_pParkingRPAGuidanceView->addAsset(l_pParkingRPAGuidance);

    // Surround View
    // *****************************************************************************************************
    //  const vfc::uint32_t l_testCullSetting =  ~(pc::vehiclemodel::VehicleModel::CULL_SETTING_INTERIOR |
    //  pc::vehiclemodel::VehicleModel::CULL_SETTING_WHEELS);
    cc::views::surroundview::SurroundView* const l_pSurroundView = new cc::views::surroundview::SurroundView(
        "Surround View",
        getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->getPosition(virtcam::VCAM_DEFAULT_VIEW),
        getFramework());

    const auto l_pRenderManagerSurroundView = new cc::assets::common::CustomRenderManager(
        l_pRenderManagerRegistry,
        virtcam::VCAM_DEFAULT_VIEW,
        rbp::vis::imp::chamaeleon::EChamaeleonView::DEFAULT);
    l_pRenderManagerSurroundView->assignCamera(l_pSurroundView);
    l_pSurroundView->setRenderOrder(osg::Camera::RenderOrder::POST_RENDER, static_cast<vfc::int32_t>(BYD_PLANVIEW_RENDERBIN_ORDER_USS) + 1);
    // l_pSurroundView->setRenderOrder(osg::Camera::RenderOrder::POST_RENDER, std::numeric_limits<int>::max());

    l_pSurroundView->addAsset(l_pFloor);
#ifndef USE_TRADITIONAL_BASEPLATE
    l_pSurroundView->addAsset(l_pFloorPlate);
#endif
    l_pSurroundView->addAsset(l_pBowl);
    l_pSurroundView->addAsset(l_pOutermostLines);
    l_pSurroundView->addAsset(l_pWheelTracks);
    l_pSurroundView->addAsset(l_pAllWheels);
#if USE_RADAR_WALL
    if(g_customerScene->m_objectWallVariant == 1)
    {
        l_pSurroundView->addAsset(l_obstacleOverlay);
    }
    else if (g_customerScene->m_objectWallVariant == 2)
    {
        l_pSurroundView->addAsset(l_pdcOverlay);
    }
    else
    {
        // add nothing
    }
#else
    l_pSurroundView->addAsset(l_pObstacleOverlay_Perspective);
    // l_pSurroundView->addAsset(l_pOutermostLinesColorful);
    //  l_pSurroundView->addAsset(l_pParkingIcon);
#endif
    // l_pSurroundView->addAsset(l_pSplineOverlay);


    cc::views::surroundview::SurroundView* const l_pSurroundViewFindCar = new cc::views::surroundview::SurroundView(
    "Surround View Find Car",
    getCorrectDriveHandSideViewport(g_views->m_remoteMainViewport, g_views->m_usableCanvasViewport),
    virtcam::g_positions->m_persFront,
    getFramework());

    l_pRenderManagerSurroundView->assignCamera(l_pSurroundViewFindCar);
    l_pSurroundViewFindCar->setRenderOrder(
        osg::Camera::RenderOrder::POST_RENDER, static_cast<vfc::int32_t>(BYD_PLANVIEW_RENDERBIN_ORDER_USS) + 1);

    l_pSurroundViewFindCar->addAsset(l_pFloor);
#ifndef USE_TRADITIONAL_BASEPLATE
    l_pSurroundViewFindCar->addAsset(l_pFloorPlate);
#endif
    l_pSurroundViewFindCar->addAsset(l_pBowl);
    l_pSurroundViewFindCar->addAsset(l_pOutermostLines);
    l_pSurroundViewFindCar->addAsset(l_pWheelTracks);
    l_pSurroundViewFindCar->addAsset(l_pAllWheels);

    if (g_customerScene->m_objectWallVariant == 1)
    {
        l_pSurroundViewFindCar->addAsset(l_obstacleOverlay);
    }
    else if (g_customerScene->m_objectWallVariant == 2)
    {
        l_pSurroundViewFindCar->addAsset(l_pdcOverlay);
    }
    else
    {
    }
#if DISABLE_ROTATABLE_TRANSPARENT_MODEL
    // transparent impostor as vehicle model asset 3D
    // osg::ref_ptr<assets::impostor::TransparentVehicleImpostor> l_pTV3D_Transparent3dImpostor =
    //     new assets::impostor::TransparentVehicleImpostor(
    //         getFramework()->asCustomFramework(),
    //         AssetId::EASSETS_TV3D_TRANSPARENT_IMPOSTOR,
    //         static_cast<pc::vehiclemodel::VehicleModel*>(l_pVehicle->getAsset()),
    //         osg::Vec2us(
    //             static_cast<unsigned short>(l_pSurroundView->getViewport()->width()),
    //             static_cast<unsigned short>(l_pSurroundView->getViewport()->height())),
    //         false);
    // l_pTV3D_Transparent3dImpostor->setName("TV3D_Transparent3dImpostor");
    // l_pSurroundView->addAsset(l_pTV3D_Transparent3dImpostor);

    l_pSurroundView->addAsset(l_pVehicle);
    l_pSurroundViewFindCar->addAsset(l_pVehicle);
    // l_pPlanViewVehicle2D->addAsset(l_pSuperTransparentOverlay);
#else
    l_pSurroundView->addAsset(l_pVehicle);

    l_pPlanViewVehicle2D->addAsset(new assets::uielements::Vehicle2DOverlay(
            getFramework()->asCustomFramework(), AssetId::EASSETS_VEHICLE_2D_ICON, l_pPlanViewVehicle2D));
#endif

    //! Planetary View
    cc::views::surroundview::SurroundView* const l_planetaryView = new cc::views::surroundview::SurroundView(
        "Planetary View",
        getCorrectDriveHandSideViewport(g_views->m_usableCanvasViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->getPosition(virtcam::VCAM_PLANETRAY_VIEW),
        getFramework());

    const auto l_pRenderManagerPlanetaryView = new cc::assets::common::CustomRenderManager(
        l_pRenderManagerRegistry,
        virtcam::VCAM_PLANETRAY_VIEW,
        rbp::vis::imp::chamaeleon::EChamaeleonView::DEFAULT);
    l_pRenderManagerPlanetaryView->assignCamera(l_planetaryView);
    // l_planetaryView->setRenderOrder(osg::Camera::RenderOrder::POST_RENDER, BYD_PLANVIEW_RENDERBIN_ORDER_USS + 1);
    l_planetaryView->addAsset(l_pFloor);
#ifndef USE_TRADITIONAL_BASEPLATE
    l_planetaryView->addAsset(l_pFloorPlate);
#endif
    l_planetaryView->addAsset(l_pBowl);
    // l_planetaryView->addAsset(l_pOutermostLines);
    // l_planetaryView->addAsset(l_pWheelTracks);
    // l_planetaryView->addAsset(l_pAllWheels);
    // l_planetaryView->addAsset(l_obstacleOverlay);
    // l_planetaryView->addAsset(l_pVehicle);


    const auto l_pPlanetaryViewVehicle2D = new cc::views::planview::PlanView(
        "Plan View Vehicle2D Fullscreen",
        getCorrectDriveHandSideViewport(g_views->m_usableCanvasViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pPlanetaryViewVehicle2D->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_planetary / 2.,
        l_total_width_meters_planetary / 2.,
        -l_total_length_meters_planetary / 2.,
        l_total_length_meters_planetary / 2. );

    // l_pPlanetaryViewVehicle2D->addAsset(l_pOutermostLines);
    // l_pPlanetaryViewVehicle2D->addAsset(l_pWheelTracks);
#ifdef ENABLE_USS_OVERLAY
    l_pPlanetaryViewVehicle2D->addAsset(l_pWarnSymbolUss);
#endif
    // l_pPlanViewFullscreenVehicle2D->addAsset(l_pVehicle2DIcon);
    l_pPlanetaryViewVehicle2D->addAsset(new assets::uielements::Vehicle2DOverlay(
        getFramework()->asCustomFramework(), AssetId::EASSETS_VEHICLE_2D_ICON, l_pPlanetaryViewVehicle2D));
    l_pPlanetaryViewVehicle2D->addAsset(l_pVehicle2DWheels);
    l_pPlanetaryViewVehicle2D->setRenderOrder(
        osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_ABOVE_VEHICLE_2D);


    //! Super Transparent Overlay
//    const auto l_pSuperTransparentOverlay = new pc::core::Asset(
//        AssetId::EASSETS_SUPER_TRANSPARENT_OVERLAY,
//        new cc::assets::superTransparentOverlay::SuperTransparentOverlay(getFramework()->asCustomFramework(), nullptr));

    // l_pSurroundView->addAsset(l_pSuperTransparentOverlay);

    //! give this view a unique StateSet whitout replacing its the default StateSet
    osg::StateSet* const l_SurroundViewStateSet = l_pSurroundView->getOrCreateStateSet();
    osg::Uniform*  const l_pAlphaUniform =
        l_SurroundViewStateSet->getOrCreateUniform("VehicleTransparency", osg::Uniform::FLOAT);
    l_pAlphaUniform->set(1.0f); // PRQA S 3803

    // Install hemispherical camera controller on 3D surroundview
    const auto l_hemisphereCameraUpdaterHori = new cc::virtcam::HeadUnitHemisphereCameraUpdater(getFramework());

    // Set center to match the default SV virtual camera position
    osg::Vec3f carCenterHori;
    // if (!l_hemisphereCameraUpdater->estimateCenter(cc::virtcam::g_positions->m_overTheRoof.toViewMatrix(),
    // carCenter))
    // {
    //   carCenter = osg::Vec3f(pc::vehicle::g_mechanicalData->getCenter(), 5.0f); // change to z=1
    // }
    carCenterHori =
        osg::Vec3f(pc::vehicle::g_mechanicalData->getCenter(), 0.0f) + cc::virtcam::g_carCenter->m_carCenterHori;
    l_hemisphereCameraUpdaterHori->setHemisphereCenter(carCenterHori);

    l_pSurroundView->setCameraUpdater(l_hemisphereCameraUpdaterHori);
    l_pSurroundViewFindCar->setCameraUpdater(l_hemisphereCameraUpdaterHori);


    // Attach a return channel updater, which will send the current camera parameters (expressed in hemispherical
    // coordinates) to the HU (via state machine)
    // l_hemisphereCameraUpdaterHori->attachReturnChannelUpdateCallback(l_pSurroundView, this);

#if ENABLE_VERTICAL_MODE
    // ! Vertical mode surround view
    cc::views::surroundview::SurroundView* l_pVertSurroundView = new cc::views::surroundview::SurroundView(
        "Vertical Surround View",
        getCorrectDriveHandSideViewport(g_views->m_vertMainViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->getPosition(virtcam::VCAM_VERT_REAR_LEFT_VIEW),
        getFramework());

    auto l_pRenderManagerVertSurroundView =
        new cc::assets::common::CustomRenderManager(l_pRenderManagerRegistry, virtcam::VCAM_VERT_REAR_LEFT_VIEW,
        rbp::vis::imp::chamaeleon::EChamaeleonView::DEFAULT);
    l_pRenderManagerVertSurroundView->assignCamera(l_pVertSurroundView);

    auto       l_hemisphereCameraUpdaterVert = new cc::virtcam::HeadUnitHemisphereCameraUpdater(getFramework());
    osg::Vec3f carCenterVert;
    carCenterVert =
        osg::Vec3f(pc::vehicle::g_mechanicalData->getCenter(), 0.0f) + cc::virtcam::g_carCenter->m_carCenterVert;
    l_hemisphereCameraUpdaterVert->setHemisphereCenter(carCenterVert);

    l_pVertSurroundView->addAsset(l_pFloor);
    l_pVertSurroundView->addAsset(l_pBowl);
    l_pVertSurroundView->addAsset(l_pVehicle);
    l_pVertSurroundView->addAsset(l_pOutermostLines);
    l_pVertSurroundView->addAsset(l_pWheelTracks);
    l_pVertSurroundView->setCameraUpdater(l_hemisphereCameraUpdaterVert);
#if USE_RADAR_WALL
#else
    l_pVertSurroundView->addAsset(l_pObstacleOverlay_Perspective);

    // l_pVertSurroundView->addAsset(l_pParkingIcon);
    // l_pVertSurroundView->addAsset(l_pTV3D_Transparent3dImpostor);
#endif

    //! give this view a unique StateSet whitout replacing its the default StateSet
    // osg::StateSet* l_vertSurroundViewStateSet = l_pVertSurroundView->getOrCreateStateSet();
    // osg::Uniform *l_pVertAlphaUniform = l_vertSurroundViewStateSet->getOrCreateUniform("VehicleTransparency",
    // osg::Uniform::FLOAT); l_pVertAlphaUniform->set(1.0f);  // PRQA S 3803
#endif // ENABLE_VERTICAL_MODE

    //! Fisheye Views
    //! ***************************************************************************************************
    const auto l_pRawFisheyeFront = new cc::views::CustomRawFisheyeView::CustomRawFisheyeView(
        0u,
        false,
        getFramework(),
        getCorrectDriveHandSideViewport(g_views->m_fisheyeViewport, g_views->m_usableCanvasViewport));
    l_pRawFisheyeFront->setName("RawFisheyeFront");

    const auto l_pRawFisheyeRear = new cc::views::CustomRawFisheyeView::CustomRawFisheyeView(
        2u,
        false,
        getFramework(),
        getCorrectDriveHandSideViewport(g_views->m_fisheyeViewport, g_views->m_usableCanvasViewport));
    l_pRawFisheyeRear->setName("RawFisheyeRear");

    const auto l_pRawFisheyeLeft = new cc::views::CustomRawFisheyeView::CustomRawFisheyeView(
        3u,
        false,
        getFramework(),
        getCorrectDriveHandSideViewport(g_views->m_fisheyeViewport, g_views->m_usableCanvasViewport));
    l_pRawFisheyeLeft->setName("RawFisheyeLeft");

    const auto l_pRawFisheyeRight = new cc::views::CustomRawFisheyeView::CustomRawFisheyeView(
        1u,
        false,
        getFramework(),
        getCorrectDriveHandSideViewport(g_views->m_fisheyeViewport, g_views->m_usableCanvasViewport));
    l_pRawFisheyeRight->setName("RawFisheyeRight");

    // auto l_pRawFisheyeQuad  = new cc::views::rawfisheyeview::CustomRawFisheyeView(0, true, getFramework(),
    // getCorrectDriveHandSideViewport(g_views->m_fisheyeViewport, g_views->m_usableCanvasViewport));
    // l_pRawFisheyeQuad->setName("RawFisheyeQuad");

    //! EOL Calibration View
    //! ***************************************************************************************************
    const auto l_pRawFisheyeQuadCalibration = new cc::views::CustomRawFisheyeView::CustomRawFisheyeView(
        0u,
        true,
        getFramework(),
        getCorrectDriveHandSideViewport(g_views->m_cpcViewport, g_views->m_usableCanvasViewport));
    l_pRawFisheyeQuadCalibration->setName("CpcView");

    const auto l_ECALprogressoverlay = new pc::core::Asset(
        AssetId::EASSETS_ECAL_PROGRESS_OVERLAY,
        new assets::ECALprogressoverlay::ECALprogressoverlay(
            getFramework()->asCustomFramework(), AssetId::EASSETS_ECAL_PROGRESS_OVERLAY));

    l_pRawFisheyeQuadCalibration->addAsset(l_ECALprogressoverlay);
    static_cast<assets::ECALprogressoverlay::ECALprogressoverlay*>(l_ECALprogressoverlay->getAsset())
        ->setReferenceView(l_pRawFisheyeQuadCalibration);

    //! engineering views
    //! ***********************************************************************************************
    const auto l_pEngineeringView = new cc::views::nfsengineeringview::NfsEngineeringView(
        getFramework(), cc::views::nfsengineeringview::ENGSCREEN_DEFAULT, nullptr);
    l_pEngineeringView->setName("EngineeringView Default");

    const auto l_pEnginneringViewCalib = new cc::views::nfsengineeringview::NfsEngineeringView(
        getFramework(), cc::views::nfsengineeringview::ENGSCREEN_NFSCALIB, nullptr);
    l_pEnginneringViewCalib->setName("EnginneringViewCalib");

    const auto l_CPCCpcDebugScreen = new cc::views::nfsengineeringview::NfsEngineeringView(
        getFramework(), cc::views::nfsengineeringview::ENGSCREEN_CPC, nullptr);
    l_CPCCpcDebugScreen->setName("CPCCpcDebugView");

    // const auto l_pProfilingView = new pc::views::perfview::ProfilingView();
    // l_pProfilingView->addAsset(l_pVehicle);
    // l_pProfilingView->addAsset(l_pDynamicGearOverlays);

    //! Offroad Place holder
    //! ********************************************************************************************

    // separator between plan view and main view
    const auto l_pDayNightView = new cc::views::daynightview::DayNightView(
        "DayNight",
        getCorrectDriveHandSideViewport(g_views->m_dayNightViewport, g_views->m_usableCanvasViewport),
        getFramework(),
        false,
        g_views->m_usableCanvasViewport,
        0u);

    // separator between wheel left and wheel right view
    const auto l_pWheelSeparatorHorizontalView =
        new pc::core::View("WheelSeparatorHorizontalView", g_views->m_wheelSeparator);
    l_pWheelSeparatorHorizontalView->addAsset(l_pWheelSeparator_Horizontal);
    static_cast<assets::uielements::wheelseparatorhorizontal::WheelSeparatorHorizontal*>(
        l_pWheelSeparator_Horizontal->getAsset())
        ->setReferenceView(l_pWheelSeparatorHorizontalView);
#if ENABLE_VERTICAL_MODE
    auto l_pWheelSeparatorVerticalView =
        new pc::core::View("WheelSeparatorVerticalView", g_views->m_vertWheelSeparator);
    l_pWheelSeparatorVerticalView->addAsset(l_pWheelSeparator_Vertical);
    static_cast<assets::uielements::wheelseparatorvertical::WheelSeparatorVertical*>(
        l_pWheelSeparator_Vertical->getAsset())
        ->setReferenceView(l_pWheelSeparatorVerticalView);
#endif // ENABLE_VERTICAL_MODE
    // See through bonnet **********************************************************************************************
    const auto l_pSeeThrBonnetView = new pc::core::Asset(
        AssetId::EASSETS_SEE_THROUGH_BONNET,
        new assets::stb::SeeThroughBonnet(
            getFramework()->asCustomFramework(),
            (static_cast<assets::trajectory::OutermostLine*>(l_pOutermostLines->asGroup()->getChild(0u)))
                ->getMainLogicPtr()
                ->getInputDataRef()));

    // bonnet View
    // ******************************************************************************************************

    //! * [l_pBonnetView](@ref cc.views.bonnetview.BonnetView): See Through Bonnet
    const auto l_pBonnetView = new cc::views::bonnetview::BonnetView(
        "Bonnet View", g_views->m_mainViewport, virtcam::g_positions->m_bonnet, getFramework());

    const auto l_pRenderManagerBonnetView = new pc::factory::RenderManager(
        l_pRenderManagerRegistry,
        virtcam::VCAM_BONNET_VIEW,
        rbp::vis::imp::chamaeleon::EChamaeleonView::DEFAULT);
    l_pRenderManagerBonnetView->assignCamera(l_pBonnetView);

    l_pBonnetView->addAsset(l_pFloor); // needed for floor-plate text
    l_pBonnetView->addAsset(l_pWheelTracks);
    l_pBonnetView->addAsset(l_pOutermostLines);
    l_pBonnetView->addAsset(l_pSeeThrBonnetView);
    l_pBonnetView->addAsset(l_pFrontWheels);

    //! Front Bumper View
    const auto l_pFrontBumperView = new pc::core::View(
      "Front Bumper View",
      getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
      virtcam::g_positions->m_frontBumper);

    const auto l_pRenderManagerFrontBumperView = new cc::assets::common::CustomRenderManager(
        l_pRenderManagerRegistry,
        virtcam::VCAM_FRONT_BUMPER_VIEW,
        rbp::vis::imp::chamaeleon::EChamaeleonView::DEFAULT);
    l_pRenderManagerFrontBumperView->assignCamera(l_pFrontBumperView);

    l_pFrontBumperView->addAsset(l_pFloor);
    l_pFrontBumperView->addAsset(l_pFloorPlate);
    l_pFrontBumperView->addAsset(l_pAllWheels);
    l_pFrontBumperView->addAsset(l_pOutermostLines);
    // l_pFrontBumperView->addAsset(l_pSuperTransparentOverlay);
    l_pFrontBumperView->addAsset(l_pWheelTracks);
    // assets::impostor::TransparentVehicleImpostor* l_pTV2D_Transparent2dImpostorBumperFront =
    //     new assets::impostor::TransparentVehicleImpostor(
    //         getFramework()->asCustomFramework(),
    //         AssetId::EASSETS_TV2D_TRANSPARENT_IMPOSTOR,
    //         static_cast<pc::vehiclemodel::VehicleModel*>(l_pVehicle->getAsset()),
    //         osg::Vec2us(
    //             static_cast<unsigned short>(l_pSurroundView->getViewport()->width()),
    //             static_cast<unsigned short>(l_pSurroundView->getViewport()->height())),
    //         false);
    // l_pTV2D_Transparent2dImpostorBumperFront->setName("FrontBumperImpostor");
    // l_pFrontBumperView->addAsset(l_pTV2D_Transparent2dImpostorBumperFront);
    l_pFrontBumperView->addAsset(l_pVehicle);
    if(g_customerScene->m_objectWallVariant == 1)
    {
        l_pFrontBumperView->addAsset(l_obstacleOverlay);
    }
    else if (g_customerScene->m_objectWallVariant == 2)
    {
        l_pFrontBumperView->addAsset(l_pdcOverlay);
    }
    else
    {
        // add nothing
    }
    auto l_bumperDistanceOverlay = new pc::core::Asset(
        AssetId::EASSETS_BUMPER_DIGITAL_DISTANCE_DISPLAY,
        new assets::distanceoverlay::DistanceOverlay(l_zoneLayout, cc::assets::distanceoverlay::g_distanceOverlaySettings->m_bumperCharacterSize, getFramework()));

    auto const l_bumperDistanceOverlayAsset = static_cast<assets::distanceoverlay::DistanceOverlay*>(l_bumperDistanceOverlay->getAsset());

    l_bumperDistanceOverlayAsset->setStopDistanceEnabled(false);

    l_pFrontBumperView->addAsset(l_bumperDistanceOverlay);

    l_pFrontBumperView->setProjectionMatrixAsOrtho2D(
      - l_total_width_meters_bumper/2., l_total_width_meters_bumper/2.,
      - l_total_length_meters_bumper /2. , l_total_length_meters_bumper /2.);

    //! Rear Bumper View
    const auto l_pRearBumperView = new pc::core::View(
      "Rear Bumper View",
      getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
      virtcam::g_positions->m_rearBumper);

    const auto l_pRenderManagerRearBumperView = new cc::assets::common::CustomRenderManager(
        l_pRenderManagerRegistry,
        virtcam::VCAM_REAR_BUMPER_VIEW,
        rbp::vis::imp::chamaeleon::EChamaeleonView::DEFAULT);
    l_pRenderManagerRearBumperView->assignCamera(l_pRearBumperView);

    l_pRearBumperView->addAsset(l_pFloor);
    l_pRearBumperView->addAsset(l_pFloorPlate);
    l_pRearBumperView->addAsset(l_pAllWheels);
    l_pRearBumperView->addAsset(l_pOutermostLines);
    // l_pRearBumperView->addAsset(l_pSuperTransparentOverlay);
    l_pRearBumperView->addAsset(l_pWheelTracks);
    // assets::impostor::TransparentVehicleImpostor* l_pTV2D_Transparent2dImpostorBumperRear =
    //     new assets::impostor::TransparentVehicleImpostor(
    //         getFramework()->asCustomFramework(),
    //         AssetId::EASSETS_TV2D_TRANSPARENT_IMPOSTOR,
    //         static_cast<pc::vehiclemodel::VehicleModel*>(l_pVehicle->getAsset()),
    //         osg::Vec2us(
    //             static_cast<unsigned short>(l_pSurroundView->getViewport()->width()),
    //             static_cast<unsigned short>(l_pSurroundView->getViewport()->height())),
    //         false);
    // l_pTV2D_Transparent2dImpostorBumperRear->setName("FrontBumperImpostor");
    // l_pRearBumperView->addAsset(l_pTV2D_Transparent2dImpostorBumperRear);
    l_pRearBumperView->addAsset(l_pVehicle);
    if(g_customerScene->m_objectWallVariant == 1)
    {
        l_pRearBumperView->addAsset(l_obstacleOverlay);
    }
    else if (g_customerScene->m_objectWallVariant == 2)
    {
        l_pRearBumperView->addAsset(l_pdcOverlay);
    }
    else
    {
        // add nothing
    }
    l_pRearBumperView->addAsset(l_bumperDistanceOverlay);
    l_pRearBumperView->setProjectionMatrixAsOrtho2D(
      - l_total_width_meters_bumper/2., l_total_width_meters_bumper/2.,
      - l_total_length_meters_bumper /2. , l_total_length_meters_bumper /2.);

    // l_pRenderManagerFrontBumperView->assignCamera(l_pFrontBumperView);

    //   l_pFrontBumperView->addAsset(l_pFloor);
    // #ifndef USE_TRADITIONAL_BASEPLATE
    //   l_pFrontBumperView->addAsset(l_pFloorPlate);
    // #endif
    //   l_pFrontBumperView->addAsset(l_pVehicle);
    //   l_pFrontBumperView->addAsset(l_pOutermostLines);

    //   auto l_pRearBumperView = new cc::views::surroundview::SurroundView(
    //     "Rear Bumper view",
    //     getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
    //     virtcam::g_positions->m_rearBumper);

    //     //     getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
    //     // virtcam::g_positions->getPosition(virtcam::VCAM_DEFAULT_VIEW));

    //   // l_pRearBumperView->setProjectionMatrixAsOrtho2D(
    //   //   - l_total_width_meters_parking_hori /2., l_total_width_meters_parking_hori /2.,
    //   //   - l_total_length_meters_parking_hori /2. , l_total_length_meters_parking_hori /2.);

    //   l_pRenderManagerRearBumperView->assignCamera(l_pRearBumperView);

    //   l_pRearBumperView->addAsset(l_pFloor);
    //   l_pRearBumperView->addAsset(l_pVehicle);
    //   l_pRearBumperView->addAsset(l_pOutermostLines);
    //   #ifndef USE_TRADITIONAL_BASEPLATE
    //   l_pRearBumperView->addAsset(l_pFloorPlate);
    // #endif

    // vertical mode
#if ENABLE_VERTICAL_MODE
    auto l_pVertBonnetView = new cc::views::bonnetview::BonnetView(
        "Vert Bonnet View", g_views->m_vertMainViewport, virtcam::g_positions->m_vertBonnet, getFramework());

    auto l_pRenderManagerBonnetViewVert =
        new pc::factory::RenderManager(l_pRenderManagerRegistry, virtcam::VCAM_VERT_BONNET_VIEW,
        rbp::vis::imp::chamaeleon::EChamaeleonView::DEFAULT);
    l_pRenderManagerBonnetViewVert->assignCamera(l_pVertBonnetView);

    l_pVertBonnetView->addAsset(l_pFloor); // needed for floor-plate text
    l_pVertBonnetView->addAsset(l_pWheelTracks);
    l_pVertBonnetView->addAsset(l_pOutermostLines);
    l_pVertBonnetView->addAsset(l_pSeeThrBonnetView);
    l_pVertBonnetView->addAsset(l_pFrontWheels);
#endif

    //For SGHL image in image mode
    const auto l_frontViewPark = new pc::core::View(
        "Front View Park",
        getCorrectDriveHandSideViewport(g_views->m_parkSingleViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_front);
    l_frontViewPark->addAsset(l_pOutermostLines);
    l_frontViewPark->addAsset(l_pWheelTracks);
    l_frontViewPark->addAsset(l_pSingleCamFront);
    l_frontViewPark->addAsset(l_pCoverPlate);
    l_frontViewPark->addAsset(l_pDL1);

    const auto l_rearViewPark = new pc::core::View(
        "Rear View Park",
        getCorrectDriveHandSideViewport(g_views->m_parkSingleViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_rear);
    l_rearViewPark->addAsset(l_pWheelTracksColorful);
    l_rearViewPark->addAsset(l_pOutermostLinesColorful);
    l_rearViewPark->addAsset(l_pSingleCamRear);
    l_rearViewPark->addAsset(l_pTrailerHitchTrajectory);
    l_rearViewPark->addAsset(l_pDL1Colorful);
    l_rearViewPark->addAsset(l_pCoverPlate);

    // mirror the view
    l_rearViewPark->setProjectionMatrix(l_rearViewPark->getProjectionMatrix() * l_mirrorTheWorld);


    const auto l_pImageinImageParkPlanView = new cc::views::planview::PlanView(
        "Image in Image Park Top",
        getCorrectDriveHandSideViewport(g_views->m_parkTop, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pImageinImageParkPlanView->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_imageInImage_parktop / 2.,
        l_total_width_meters_imageInImage_parktop / 2.,
        -l_total_length_meters_imageInImage_parktop / 2.,
        l_total_length_meters_imageInImage_parktop / 2.);

    l_renderManagerPlanView->assignCamera(l_pImageinImageParkPlanView);

    l_pImageinImageParkPlanView->addAsset(l_pFloor);
#ifndef USE_TRADITIONAL_BASEPLATE
    l_pImageinImageParkPlanView->addAsset(l_pFloorPlate);
#endif
    // l_pImageinImageParkPlanView->addAsset(l_freeparkingManager);


    const auto l_pImageinImagePlanViewParkUSSOverlay = new cc::views::planview::PlanView(
        "Image in Image Park USS Overlay",
        getCorrectDriveHandSideViewport(g_views->m_parkTop, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pImageinImagePlanViewParkUSSOverlay->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_imageInImage_parktop / 2.,
        l_total_width_meters_imageInImage_parktop / 2.,
        -l_total_length_meters_imageInImage_parktop / 2.,
        l_total_length_meters_imageInImage_parktop / 2.);

    l_pImageinImagePlanViewParkUSSOverlay->addAsset(l_distanceOverlay);

#ifndef USE_RADAR_WALL
    l_pImageinImagePlanViewParkUSSOverlay->addAsset(l_pObstacleOverlay_vehOffset);
#else
    if(g_customerScene->m_objectWallVariant == 1)
    {
        l_pImageinImagePlanViewParkUSSOverlay->addAsset(l_obstacleOverlay);
    }
    else if (g_customerScene->m_objectWallVariant == 2)
    {
        l_pImageinImagePlanViewParkUSSOverlay->addAsset(l_pdcOverlay);
    }
    else
    {
        // add nothing
    }
#endif
    l_pImageinImagePlanViewParkUSSOverlay->addAsset(l_pE3ParkingTopViewOverlay, true);
    l_pImageinImagePlanViewParkUSSOverlay->setRenderOrder(
        osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_USS);

    const auto l_pImageinImageParkPlanViewVehicle2D = new cc::views::planview::PlanView(
        "Image in Image Park Plan View Vehicle2D",
        getCorrectDriveHandSideViewport(g_views->m_parkTop, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pImageinImageParkPlanViewVehicle2D->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_vehicle2d_imageInImage_parktop / 2.,
        l_total_width_meters_vehicle2d_imageInImage_parktop / 2.,
        -l_total_length_meters_vehicle2d_imageInImage_parktop / 2.,
        l_total_length_meters_vehicle2d_imageInImage_parktop / 2.);

    l_pImageinImageParkPlanViewVehicle2D->addAsset(l_pOutermostLines);
#ifdef ENABLE_USS_OVERLAY
    l_pImageinImageParkPlanViewVehicle2D->addAsset(l_pWarnSymbolUss);
#endif
    l_pImageinImageParkPlanViewVehicle2D->setRenderOrder(
        osg::Camera::POST_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_ABOVE_VEHICLE_2D);
    l_pImageinImageParkPlanViewVehicle2D->addAsset(new assets::uielements::Vehicle2DOverlay(
        getFramework()->asCustomFramework(), AssetId::EASSETS_VEHICLE_2D_ICON, l_pImageinImageParkPlanViewVehicle2D));
    l_pImageinImageParkPlanViewVehicle2D->addAsset(l_pVehicle2DWheels);
    // l_pImageinImageParkPlanViewVehicle2D->addAsset(l_pParkingSlotPlanviewManager);

    //! VIEW CORNERS
    {
        using namespace cc::assets::viewcorner;
        using namespace cc::core;
        //! VIEW CORNER HORIZONTAL
        // auto customFramework = getFramework()->asCustomFramework();
        // l_pPlanView           ->addAsset(new ViewportCorner{customFramework,   AssetId::EASSETS_CORNER_PLAN_VIEW,
        // l_pPlanView}); l_rearPanoramaView           ->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_REAR_VIEW,     l_rearPanoramaView}); l_pLeftView           ->addAsset(new
        // ViewportCorner{customFramework,   AssetId::EASSETS_CORNER_LEFT_VIEW,     l_pLeftView}); l_frontPanoramaView
        // ->addAsset(new ViewportCorner{customFramework,   AssetId::EASSETS_CORNER_FRONT_VIEW, l_frontPanoramaView});
        // l_pRightView          ->addAsset(new ViewportCorner{customFramework,   AssetId::EASSETS_CORNER_RIGHT_VIEW,
        // l_pRightView}); l_pHoriParkingPlanView->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_PLAN_VIEW,     l_pHoriParkingPlanView}); l_pHoriParkingFloorPlanView->addAsset(new
        // ViewportCorner{customFramework,   AssetId::EASSETS_CORNER_PLAN_VIEW,     l_pHoriParkingPlanView});
        // l_pHoriSeparatorView  ->addAsset(new ViewportCorner{customFramework,   AssetId::EASSETS_SEPARATOR_VIEW_5x,
        // l_pHoriSeparatorView}); l_pSurroundView       ->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_SURROUND_VIEW, l_pSurroundView}); l_pLeftView_5x        ->addAsset(new
        // ViewportCorner{customFramework,   AssetId::EASSETS_CORNER_LEFT_VIEW_5x, l_pLeftView_5x}); l_pRightView_5x
        // ->addAsset(new ViewportCorner{customFramework,   AssetId::EASSETS_CORNER_RIGHT_VIEW_5x, l_pRightView_5x});
        // l_pLeftRearView_5x    ->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_LEFT_REAR_VIEW_5x, l_pLeftRearView_5x}); l_pRightRearView_5x   ->addAsset(new
        // ViewportCorner{customFramework,   AssetId::EASSETS_CORNER_RIGHT_REAR_VIEW_5x, l_pRightRearView_5x});
        // l_pAssitFrontView     ->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_ASSIST_FRONT_VIEW, l_pAssitFrontView}); l_pAssitRearView      ->addAsset(new
        // ViewportCorner{customFramework,   AssetId::EASSETS_CORNER_ASSIST_REAR_VIEW, l_pAssitRearView});
        // l_pFrontWheelLeftView ->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_FRONT_LEFT_WHEEL_VIEW, l_pFrontWheelLeftView, true, true, false, false});
        // l_pFrontWheelRightView->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_FRONT_RIGHT_WHEEL_VIEW, l_pFrontWheelRightView, false, false, true, true});
        // l_pRearWheelLeftView  ->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_REAR_LEFT_WHEEL_VIEW, l_pRearWheelLeftView, true, true, false, false});
        // l_pRearWheelRightView ->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_REAR_RIGHT_WHEEL_VIEW, l_pRearWheelRightView, false, false, true, true});
        // l_pRearJunctionView   ->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_REAR_JUNCTION_VIEW, l_pRearJunctionView}); l_pFrontJunctionView  ->addAsset(new
        // ViewportCorner{customFramework,   AssetId::EASSETS_CORNER_FRONT_JUNCTION_VIEW, l_pFrontJunctionView});
        // l_pBothWheelLeftView ->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_BOTH_LEFT_WHEEL_VIEW, l_pBothWheelLeftView, true, true, false, false});
        // l_pBothWheelRightView ->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_BOTH_LEFT_WHEEL_VIEW, l_pBothWheelRightView, false, false, true, true});

#if ENABLE_VERTICAL_MODE
        //! VIEW CORNER VERT
        //! vertMainViewport
        // l_pVertSurroundView->addAsset(new ViewportCorner{customFramework, AssetId::EASSETS_CORNER_SURROUND_VIEW_VERT,
        // l_pVertSurroundView}); l_pVertFrontView->addAsset(   new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_FRONT_VIEW_VERT,    l_pVertFrontView}); l_pVertRightView->addAsset(   new
        // ViewportCorner{customFramework, AssetId::EASSETS_CORNER_RIGHT_VIEW_VERT,    l_pVertRightView});
        // l_pVertRearView->addAsset(    new ViewportCorner{customFramework, AssetId::EASSETS_CORNER_REAR_VIEW_VERT,
        // l_pVertRearView}); l_pVertLeftView->addAsset(    new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_LEFT_VIEW_VERT,     l_pVertLeftView});

        // //! vertPlanViewport
        // l_pVertPlanView->addAsset(new ViewportCorner{customFramework, AssetId::EASSETS_CORNER_PLAN_VIEW_VERT,
        // l_pVertPlanView});
        // //! vertParkingPlanViewport
        // l_pVertParkingPlanView->addAsset(new ViewportCorner{customFramework, AssetId::EASSETS_CORNER_PLAN_VIEW_VERT,
        // l_pVertParkingPlanView}); l_pVertParkingFloorPlanView->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_PLAN_VIEW_VERT, l_pVertParkingPlanView});

        // //! vertSideViewport
        // l_pVertSideFrontView->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_SIDE_FRONT_VIEW_VERT, l_pVertSideFrontView}); l_pVertSideRearView->addAsset( new
        // ViewportCorner{customFramework, AssetId::EASSETS_CORNER_SIDE_REAR_VIEW_VERT,  l_pVertSideRearView});
        // l_pVertSideLeftView->addAsset( new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_SIDE_LEFT_VIEW_VERT,  l_pVertSideLeftView}); l_pVertSideRightView->addAsset(new
        // ViewportCorner{customFramework, AssetId::EASSETS_CORNER_SIDE_RIGHT_VIEW_VERT, l_pVertSideRightView});

        // //! vertWheelLeftViewport
        // l_pVertFrontWheelLeftView->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_FRONT_LEFT_WHEEL_VIEW_VERT, l_pVertFrontWheelLeftView, false, true, false, true});
        // l_pVertWheelLeftView->addAsset(     new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_LEFT_WHEEL_VIEW_VERT,       l_pVertWheelLeftView, false, true, false, true});
        // l_pVertRearWheelLeftView->addAsset( new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_REAR_LEFT_WHEEL_VIEW_VERT,  l_pVertRearWheelLeftView,  false, true, false, true});
        // //! vertWheelRightViewport

        // l_pVertFrontWheelRightView->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_FRONT_RIGHT_WHEEL_VIEW_VERT, l_pVertFrontWheelRightView, true, false, true, false});
        // l_pVertWheelRightView->addAsset(     new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_RIGHT_WHEEL_VIEW_VERT,       l_pVertFrontWheelRightView, true, false, true, false});
        // l_pVertRearWheelRightView->addAsset( new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_REAR_RIGHT_WHEEL_VIEW_VERT,  l_pVertRearWheelRightView,  true, false, true, false});

        // //! vertFullscreenViewport
        // l_pVertPlanViewFullscreen->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_FULLSCREEN_VIEW_VERT,  l_pVertPlanViewFullscreen});

#endif // ENABLE_VERTICAL_MODE

        //! VEHICLE 2D
        // l_pPlanViewVehicle2D->addAsset(new ViewportCorner{customFramework, AssetId::EASSETS_CORNER_PLAN_VIEW,
        // l_pPlanViewVehicle2D}); l_pRemotePlanViewVehicle2D->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_PLAN_VIEW, l_pRemotePlanViewVehicle2D}); l_pVertPlanViewVehicle2D->addAsset(new
        // ViewportCorner{customFramework, AssetId::EASSETS_CORNER_PLAN_VIEW, l_pVertPlanViewVehicle2D});

        // l_pHoriParkingPlanViewVehicle2D->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_PLAN_VIEW, l_pHoriParkingPlanViewVehicle2D});
        // l_pVertParkingPlanViewVehicle2D->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_PLAN_VIEW, l_pVertParkingPlanViewVehicle2D});
        // l_pVertPlanViewFullscreenVehicle2D->addAsset(new ViewportCorner{customFramework,
        // AssetId::EASSETS_CORNER_PLAN_VIEW, l_pVertPlanViewFullscreenVehicle2D});
    }

    //! VIEW SPACING
    // {
    //     using namespace cc::assets::viewcorner;
    //     using namespace cc::target::common;
    //     using namespace cc::core;
    //     auto spaceWidthUpdateVisitor = ViewCornerSpaceUpdateVisitor{};
    //     // //! HORI
    //     spaceWidthUpdateVisitor.setBatchSpacingMode(
    //         /*Left*/ FULL_WIDTH, /*Right*/ HALF_WIDTH, /*Top*/ FULL_WIDTH, /*Bottom*/ FULL_WIDTH);
    //     spaceWidthUpdateVisitor.setBatchEnableSpaceSide(/*Left*/ true, /*Right*/ true, /*Top*/ true, /*Bottom*/ true);
    //     l_pParkingView->accept(spaceWidthUpdateVisitor);

    //     //! VERT
    //     //! vertMainViewport
    //     spaceWidthUpdateVisitor.setBatchSpaceWidth(/*Left*/ 18.0, /*Right*/ 18.0, /*Top*/ 18.0, /*Bottom*/ 18.0);
    //     spaceWidthUpdateVisitor.setBatchSpacingMode(
    //         /*Left*/ FULL_WIDTH, /*Right*/ HALF_WIDTH, /*Top*/ FULL_WIDTH, /*Bottom*/ FULL_WIDTH);
    //     spaceWidthUpdateVisitor.setBatchEnableSpaceSide(/*Left*/ true, /*Right*/ true, /*Top*/ true, /*Bottom*/ true);
    //     l_pVertFrontView->accept(spaceWidthUpdateVisitor);
    //     l_pVertRightView->accept(spaceWidthUpdateVisitor);
    //     l_pVertRearView->accept(spaceWidthUpdateVisitor);
    //     l_pVertLeftView->accept(spaceWidthUpdateVisitor);
    //     l_pVertSurroundView->accept(spaceWidthUpdateVisitor);

    //     //! vertWheelSeparator
    //     spaceWidthUpdateVisitor.setBatchSpaceWidth(/*Left*/ 18.0, /*Right*/ 18.0, /*Top*/ 18.0, /*Bottom*/ 18.0);
    //     spaceWidthUpdateVisitor.setBatchSpacingMode(
    //         /*Left*/ FULL_WIDTH, /*Right*/ HALF_WIDTH, /*Top*/ HALF_WIDTH, /*Bottom*/ HALF_WIDTH);
    //     spaceWidthUpdateVisitor.setBatchEnableSpaceSide(/*Left*/ true, /*Right*/ true, /*Top*/ false, /*Bottom*/ false);
    //     l_pWheelSeparatorVerticalView->accept(spaceWidthUpdateVisitor);

    //     //! vertPlanViewport
    //     spaceWidthUpdateVisitor.setBatchSpaceWidth(/*Left*/ 18.0, /*Right*/ 47.0, /*Top*/ 18.0, /*Bottom*/ 18.0);
    //     spaceWidthUpdateVisitor.setBatchSpacingMode(
    //         /*Left*/ HALF_WIDTH, /*Right*/ FULL_WIDTH, /*Top*/ HALF_WIDTH, /*Bottom*/ FULL_WIDTH);
    //     spaceWidthUpdateVisitor.setBatchEnableSpaceSide(/*Left*/ true, /*Right*/ true, /*Top*/ true, /*Bottom*/ true);
    //     l_pVertPlanView->accept(spaceWidthUpdateVisitor);
    //     l_pVertPlanViewVehicle2D->accept(spaceWidthUpdateVisitor);
    //     l_pVertPlanViewUSSOverlay->accept(spaceWidthUpdateVisitor);

    //     //! vertParkingPlanViewport
    //     spaceWidthUpdateVisitor.setBatchSpaceWidth(/*Left*/ 18.0, /*Right*/ 18.0, /*Top*/ 18.0, /*Bottom*/ 18.0);
    //     spaceWidthUpdateVisitor.setBatchSpacingMode(
    //         /*Left*/ FULL_WIDTH, /*Right*/ HALF_WIDTH, /*Top*/ FULL_WIDTH, /*Bottom*/ FULL_WIDTH);
    //     spaceWidthUpdateVisitor.setBatchEnableSpaceSide(/*Left*/ true, /*Right*/ true, /*Top*/ true, /*Bottom*/ true);
    //     l_pVertParkingPlanView->accept(spaceWidthUpdateVisitor);
    //     l_pVertParkingPlanViewVehicle2D->accept(spaceWidthUpdateVisitor);
    //     l_pVertParkingPlanViewUSSOverlay->accept(spaceWidthUpdateVisitor);
    //     l_pVertParkingFloorPlanView->accept(spaceWidthUpdateVisitor);

    //     //! vertParkingUIViewport
    //     spaceWidthUpdateVisitor.setBatchSpaceWidth(/*Left*/ 18.0, /*Right*/ 47.0, /*Top*/ 18.0, /*Bottom*/ 18.0);
    //     spaceWidthUpdateVisitor.setBatchSpacingMode(
    //         /*Left*/ HALF_WIDTH, /*Right*/ FULL_WIDTH, /*Top*/ FULL_WIDTH, /*Bottom*/ FULL_WIDTH);
    //     spaceWidthUpdateVisitor.setBatchEnableSpaceSide(/*Left*/ true, /*Right*/ true, /*Top*/ true, /*Bottom*/ true);
    //     l_pParkingViewVert->accept(spaceWidthUpdateVisitor);

    //     //! vertFullscreenViewport
    //     spaceWidthUpdateVisitor.setBatchSpaceWidth(/*Left*/ 18.0, /*Right*/ 47.0, /*Top*/ 18.0, /*Bottom*/ 18.0);
    //     spaceWidthUpdateVisitor.setBatchSpacingMode(
    //         /*Left*/ FULL_WIDTH, /*Right*/ FULL_WIDTH, /*Top*/ FULL_WIDTH, /*Bottom*/ FULL_WIDTH);
    //     spaceWidthUpdateVisitor.setBatchEnableSpaceSide(/*Left*/ true, /*Right*/ true, /*Top*/ true, /*Bottom*/ true);
    //     l_pVertPlanViewFullscreen->accept(spaceWidthUpdateVisitor);
    //     l_pVertPlanViewFullscreenVehicle2D->accept(spaceWidthUpdateVisitor);
    //     l_pVertPlanViewFullscreenUSSOverlay->accept(spaceWidthUpdateVisitor);

    //     //! vertSideViewport
    //     spaceWidthUpdateVisitor.setBatchSpaceWidth(/*Left*/ 18.0, /*Right*/ 47.0, /*Top*/ 18.0, /*Bottom*/ 18.0);
    //     spaceWidthUpdateVisitor.setBatchSpacingMode(
    //         /*Left*/ HALF_WIDTH, /*Right*/ FULL_WIDTH, /*Top*/ FULL_WIDTH, /*Bottom*/ HALF_WIDTH);
    //     spaceWidthUpdateVisitor.setBatchEnableSpaceSide(/*Left*/ true, /*Right*/ true, /*Top*/ true, /*Bottom*/ true);
    //     l_pVertSideFrontView->accept(spaceWidthUpdateVisitor);
    //     l_pVertSideRearView->accept(spaceWidthUpdateVisitor);
    //     l_pVertSideLeftView->accept(spaceWidthUpdateVisitor);
    //     l_pVertSideRightView->accept(spaceWidthUpdateVisitor);

    //     //! vertWheelLeftViewport
    //     spaceWidthUpdateVisitor.setBatchSpaceWidth(/*Left*/ 18.0, /*Right*/ 18.0, /*Top*/ 18.0, /*Bottom*/ 18.0);
    //     spaceWidthUpdateVisitor.setBatchSpacingMode(
    //         /*Left*/ FULL_WIDTH, /*Right*/ HALF_WIDTH, /*Top*/ HALF_WIDTH, /*Bottom*/ FULL_WIDTH);
    //     spaceWidthUpdateVisitor.setBatchEnableSpaceSide(/*Left*/ true, /*Right*/ true, /*Top*/ false, /*Bottom*/ true);
    //     l_pVertFrontWheelLeftView->accept(spaceWidthUpdateVisitor);
    //     l_pVertWheelLeftView->accept(spaceWidthUpdateVisitor);
    //     l_pVertRearWheelLeftView->accept(spaceWidthUpdateVisitor);

    //     //! vertWheelRightViewport
    //     spaceWidthUpdateVisitor.setBatchSpaceWidth(/*Left*/ 18.0, /*Right*/ 18.0, /*Top*/ 18.0, /*Bottom*/ 18.0);
    //     spaceWidthUpdateVisitor.setBatchSpacingMode(
    //         /*Left*/ FULL_WIDTH, /*Right*/ HALF_WIDTH, /*Top*/ FULL_WIDTH, /*Bottom*/ HALF_WIDTH);
    //     spaceWidthUpdateVisitor.setBatchEnableSpaceSide(/*Left*/ true, /*Right*/ true, /*Top*/ true, /*Bottom*/ false);
    //     l_pVertFrontWheelRightView->accept(spaceWidthUpdateVisitor);
    //     l_pVertWheelRightView->accept(spaceWidthUpdateVisitor);
    //     l_pVertRearWheelRightView->accept(spaceWidthUpdateVisitor);
    // }

    //! PLANVIEW ENLARGE CALLBACK
    {
        using namespace cc::views::planview;
        using namespace cc::virtcam;
        l_pPlanView->setCameraUpdater(new PlanViewEnlargeCallback{
            l_pPlanView,
            getFramework(),
            g_positions->m_plan,
            "Plan View Enlarge Callback",
            &g_planViewEnlargeSettings->m_planViewEnlargeSettings,
            true});

        l_pPlanViewVehicle2D->setCameraUpdater(new PlanViewEnlargeCallback{
            l_pPlanViewVehicle2D,
            getFramework(),
            g_positions->m_plan,
            "Plan View Vehicle2D Enlarge Callback",
            &g_planViewEnlargeSettings->m_planViewEnlargeSettings});

        // l_pPlanViewVehicle2D->addUpdateCallback(new VehicleEnlargeCallback{
        //     l_pPlanViewVehicle2D,
        //     getFramework(),
        //     g_positions->m_plan,
        //     "Vehicle Enlarge Callback",
        //     g_planViewEnlargeSettings.get()});

        l_pPlanViewUSSOverlay->setCameraUpdater(new PlanViewEnlargeCallback{
            l_pPlanViewUSSOverlay,
            getFramework(),
            g_positions->m_plan,
            "Plan View Enlarge Callback",
            &g_planViewEnlargeSettings->m_planViewEnlargeSettings});

        // l_pPlanViewFullscreen->addUpdateCallback(new PlanViewEnlargeCallback{
        //     l_pPlanViewFullscreen,
        //     getFramework(),
        //     g_positions->m_plan,
        //     "Plan View Fullscreen Enlarge Callback",
        //     &g_planViewEnlargeSettings->m_planViewFullscreenEnlargeSettings});
        // l_pPlanViewFullscreenVehicle2D->addUpdateCallback(new PlanViewEnlargeCallback{
        //     l_pPlanViewFullscreenVehicle2D,
        //     getFramework(),
        //     g_positions->m_plan,
        //     "Plan View Fullscreen Enlarge Callback",
        //     &g_planViewEnlargeSettings->m_planViewFullscreenEnlargeSettings});
        // l_pPlanViewFullscreenUSSOverlay->addUpdateCallback(new PlanViewEnlargeCallback{
        //     l_pPlanViewFullscreenUSSOverlay,
        //     getFramework(),
        //     g_positions->m_plan,
        //     "Plan View Fullscreen Enlarge Callback",
        //     &g_planViewEnlargeSettings->m_planViewFullscreenEnlargeSettings});

        l_pImageinImagePlanView->setCameraUpdater(new PlanViewEnlargeCallback{
            l_pImageinImagePlanView,
            getFramework(),
            g_positions->m_plan,
            "Plan View Floatscreen Enlarge Callback",
            &g_planViewEnlargeSettings->m_planViewFloatscreenEnlargeSettings});
        l_pImageinImagePlanViewVehicle2D->setCameraUpdater(new PlanViewEnlargeCallback{
            l_pImageinImagePlanViewVehicle2D,
            getFramework(),
            g_positions->m_plan,
            "Plan View Floatscreen Enlarge Callback",
            &g_planViewEnlargeSettings->m_planViewFloatscreenEnlargeSettings});
        l_pImageinImagePlanViewFullscreenUSSOverlay->setCameraUpdater(new PlanViewEnlargeCallback{
            l_pImageinImagePlanViewFullscreenUSSOverlay,
            getFramework(),
            g_positions->m_plan,
            "Plan View Floatscreen Enlarge Callback",
            &g_planViewEnlargeSettings->m_planViewFloatscreenEnlargeSettings});

// #if ENABLE_VERTICAL_MODE
//         l_pVertPlanView->addUpdateCallback(new PlanViewEnlargeCallback{
//             l_pVertPlanView,
//             getFramework(),
//             g_positions->m_vertPlan,
//             "Vert Plan View Enlarge Callback",
//             &g_planViewEnlargeSettings->m_vertPlanViewEnlargeSettings});
//         l_pVertPlanViewVehicle2D->addUpdateCallback(new PlanViewEnlargeCallback{
//             l_pVertPlanViewVehicle2D,
//             getFramework(),
//             g_positions->m_vertPlan,
//             "Vert Plan View Enlarge Callback",
//             &g_planViewEnlargeSettings->m_vertPlanViewEnlargeSettings});
//         l_pVertPlanViewUSSOverlay->addUpdateCallback(new PlanViewEnlargeCallback{
//             l_pVertPlanViewUSSOverlay,
//             getFramework(),
//             g_positions->m_vertPlan,
//             "Vert Plan View Enlarge Callback",
//             &g_planViewEnlargeSettings->m_vertPlanViewEnlargeSettings});

//         l_pVertPlanViewFullscreen->addUpdateCallback(new PlanViewEnlargeCallback{
//             l_pVertPlanViewFullscreen,
//             getFramework(),
//             g_positions->m_vertPlan,
//             "Vert Plan View Fullscreen Enlarge Callback",
//             &g_planViewEnlargeSettings->m_vertPlanViewFullscreenEnlargeSettings});
//         l_pVertPlanViewFullscreenVehicle2D->addUpdateCallback(new PlanViewEnlargeCallback{
//             l_pVertPlanViewFullscreenVehicle2D,
//             getFramework(),
//             g_positions->m_vertPlan,
//             "Vert Plan View Fullscreen Enlarge Callback",
//             &g_planViewEnlargeSettings->m_vertPlanViewFullscreenEnlargeSettings});
//         l_pVertPlanViewFullscreenUSSOverlay->addUpdateCallback(new PlanViewEnlargeCallback{
//             l_pVertPlanViewFullscreenUSSOverlay,
//             getFramework(),
//             g_positions->m_vertPlan,
//             "Vert Plan View Fullscreen Enlarge Callback",
//             &g_planViewEnlargeSettings->m_vertPlanViewFullscreenEnlargeSettings});

// #endif // ENABLE_VERTICAL_MODE
    }

    //! USS WARNSYMBOL REFERENCE UPDATE
    {
        // l_ussWarnSymbolsOverlay->addReferenceView(l_pPlanViewVehicle2D);
        // l_ussWarnSymbolsOverlay->addReferenceView(l_pPlanViewFullscreenVehicle2D);
        // l_ussWarnSymbolsOverlay->addReferenceView(l_pHoriParkingPlanViewVehicle2D);
        // l_ussWarnSymbolsOverlay->addReferenceView(l_pVertPlanViewVehicle2D);
        // l_ussWarnSymbolsOverlay->addReferenceView(l_pVertPlanViewFullscreenVehicle2D);
        // l_ussWarnSymbolsOverlay->addReferenceView(l_pRemotePlanViewVehicle2D);
        // l_ussWarnSymbolsOverlay->addReferenceView(l_pVertParkingPlanViewVehicle2D);
        // l_ussWarnSymbolsOverlay->addReferenceView(l_pImageinImagePlanViewVehicle2D);
        // l_ussWarnSymbolsOverlay->addReferenceView(l_pImageinImageParkPlanView);
    }

    // Hori ParkInView has its size overlapped with Hori Parking Plan Floor View so we need to make sure it got render
    // before floor view
    // l_pParkSearchView->setRenderOrder(osg::Camera::PRE_RENDER, cc::core::BYD_PLANVIEW_RENDERBIN_ORDER_FLOOR);

#ifdef CHAMAELEON
    // Image Quality
    const auto l_imageQualityRoot = new rbp::vis::imp::iq::ImageQualityRoot();
    vfc::nop(osg::Group::addChild(l_imageQualityRoot));

    // Temporal noise filter (TNF)
    // should be inserted as first IQ node
    constexpr vfc::uint32_t l_numCams = static_cast<vfc::uint32_t>(pc::core::sysconf::Cameras::ALL_CAMERAS);
    const auto          l_tnfRoot = new rbp::vis::imp::tnf::TemporalNoiseFilterRoot(getFramework(), l_numCams);
    l_imageQualityRoot->addFeature(l_tnfRoot);

    // Brightness and Color Harmonization
    const auto l_chamaeleon = new rbp::vis::imp::chamaeleon::Chamaeleon(
        getFramework(),
        l_pRenderManagerRegistry,
        l_pFloor->getSV3DNode(),
        &virtcam::g_positions->getPosition(virtcam::VCAM_PLAN_VIEW));
    l_imageQualityRoot->addFeature(l_chamaeleon);

    // Sharpness Harmonization
    const auto l_sharpnessHarmonization = new rbp::vis::imp::sh::SharpnessHarmonization(getFramework());
    l_imageQualityRoot->addFeature(l_sharpnessHarmonization);

    // Custom Window Dump for IMP KPI framework
    const auto l_customWindowDump = new rbp::vis::imp::cwd::CustomWindowDump(getFramework(), l_pPlanView);
    l_imageQualityRoot->addFeature(l_customWindowDump);
#endif

    // insert the Views in the Scene
    // *************************************************************************************
    // addView(CustomViews::PARK_SEARCHING_VIEW, l_pParkSearchView); // keep first, for above reason
    addView(CustomViews::PLAN_VIEW, l_pPlanView);
    addView(CustomViews::PLAN_VIEW_VEHICLE2D, l_pPlanViewVehicle2D);
    addView(CustomViews::PLAN_VIEW_USS_OVERLAYS, l_pPlanViewUSSOverlay);
    addView(CustomViews::HORI_PARKING_PLAN_VIEW, l_pHoriParkingPlanView);
    addView(CustomViews::HORI_PARKING_PLAN_VIEW_VEHICLE2D, l_pHoriParkingPlanViewVehicle2D);
    addView(CustomViews::HORI_PARKING_PLAN_VIEW_USS_OVERLAYS, l_pHoriParkingPlanViewUSSOverlay);
    addView(CustomViews::HORI_PARKING_FLOOR_PLAN_VIEW, l_pHoriParkingFloorPlanView);
    addView(CustomViews::HORI_SEPARATOR_VIEW, l_pHoriSeparatorView);
    addView(CustomViews::FRONT_VIEW, l_frontView);
    addView(CustomViews::FRONT_VIEW_PANO, l_frontPanoramaView);
    addView(CustomViews::REAR_VIEW, l_rearView);
    addView(CustomViews::REAR_VIEW_PANO, l_rearPanoramaView);
    // addView(CustomViews::REAR_JUNCTION_VIEW, l_pRearJunctionView);
    // addView(CustomViews::FRONT_JUNCTION_VIEW, l_pFrontJunctionView);
    addView(CustomViews::BONNET_VIEW, l_pBonnetView);
    addView(CustomViews::SURROUND_VIEW, l_pSurroundView);
    addView(CustomViews::SURROUND_VIEW_FIND_CAR, l_pSurroundViewFindCar);
    addView(CustomViews::PLANETARY_VIEW, l_planetaryView);
    addView(CustomViews::ENGINEERING_VIEW, l_pEngineeringView, false); // set true for debugging.
    addView(CustomViews::CALIB_ENGINEERING_VIEW, l_pEnginneringViewCalib);
    addView(CustomViews::LSMGLSAEB_IMPOSTORPLAN_VIEW, l_pImpostorPlanView);
    // addView(CustomViews::PROFILING_VIEW, l_pProfilingView);
    addView(CustomViews::DAY_NIGHT_VIEW, l_pDayNightView);
    addView(CustomViews::RAW_FISHEYE_LEFT, l_pRawFisheyeLeft);
    addView(CustomViews::RAW_FISHEYE_RIGHT, l_pRawFisheyeRight);
    addView(CustomViews::RAW_FISHEYE_FRONT, l_pRawFisheyeFront);
    addView(CustomViews::RAW_FISHEYE_REAR, l_pRawFisheyeRear);
    addView(CustomViews::RAW_FISHEYE_QUAD, l_pRawFisheyeQuadCalibration);
    addView(CustomViews::REAR_LEFTVIEW_IN_FOUR_WINDOW, l_pLeftRearView_5x);
    addView(CustomViews::REAR_RIGHTVIEW_IN_FOUR_WINDOW, l_pRightRearView_5x);
    addView(CustomViews::SINGLE_LEFT_VIEW, l_pLeftView);
    addView(CustomViews::SINGLE_RIGHT_VIEW, l_pRightView);
    addView(CustomViews::SINGLE_REAR_LEFT_VIEW, l_pLeftViewRear);
    addView(CustomViews::SINGLE_REAR_RIGHT_VIEW, l_pRightViewRear);
    addView(CustomViews::BOTH_WHEEL_LEFT_VIEW, l_pBothWheelLeftView);
    addView(CustomViews::BOTH_WHEEL_RIGHT_VIEW, l_pBothWheelRightView);
    addView(CustomViews::FRONT_WHEEL_LEFT_VIEW, l_pFrontWheelLeftView);
    addView(CustomViews::FRONT_WHEEL_RIGHT_VIEW, l_pFrontWheelRightView);
    addView(CustomViews::REAR_WHEEL_LEFT_VIEW, l_pRearWheelLeftView);
    addView(CustomViews::REAR_WHEEL_RIGHT_VIEW, l_pRearWheelRightView);
    addView(CustomViews::BOTH_WHEEL_LEFT_ENLARGED_VIEW, l_pBothWheelLeftEnlargedView);
    addView(CustomViews::BOTH_WHEEL_RIGHT_ENLARGED_VIEW, l_pBothWheelRightEnlargedView);
    addView(CustomViews::FRONT_WHEEL_LEFT_ENLARGED_VIEW, l_pFrontWheelLeftEnlargedView);
    addView(CustomViews::FRONT_WHEEL_RIGHT_ENLARGED_VIEW, l_pFrontWheelRightEnlargedView);
    addView(CustomViews::REAR_WHEEL_LEFT_ENLARGED_VIEW, l_pRearWheelLeftEnlargedView);
    addView(CustomViews::REAR_WHEEL_RIGHT_ENLARGED_VIEW, l_pRearWheelRightEnlargedView);
    addView(CustomViews::REMOTE_PLAN_VIEW, l_pRemotePlanView);
    addView(CustomViews::REMOTE_FRONT_VIEW, l_pRemoteFrontView);
    addView(CustomViews::REMOTE_REAR_VIEW, l_pRemoteRearView);
    addView(CustomViews::REMOTE_LEFT_VIEW, l_pRemoteLeftView);
    addView(CustomViews::REMOTE_RIGHT_VIEW, l_pRemoteRightView);
    addView(CustomViews::REMOTE_PLAN_VIEW_VEHICLE2D, l_pRemotePlanViewVehicle2D);
    addView(CustomViews::PLAN_VIEW_FULLSCREEN, l_pPlanViewFullscreen);
    addView(CustomViews::PLAN_VIEW_FULLSCREEN_VEHICLE2D, l_pPlanViewFullscreenVehicle2D);
    addView(CustomViews::PLAN_VIEW_FULLSCREEN_USS_OVERLAYS, l_pPlanViewFullscreenUSSOverlay);
#if ENABLE_VERTICAL_MODE
    addView(CustomViews::VERTICAL_PLAN_VIEW, l_pVertPlanView);
    addView(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, l_pVertPlanViewVehicle2D);
    addView(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, l_pVertPlanViewUSSOverlay);
    addView(CustomViews::VERTICAL_FRONT_VIEW, l_pVertFrontView);
    addView(CustomViews::VERTICAL_REAR_VIEW, l_pVertRearView);
    addView(CustomViews::VERTICAL_LEFT_VIEW, l_pVertLeftView);
    addView(CustomViews::VERTICAL_RIGHT_VIEW, l_pVertRightView);
    addView(CustomViews::VERTICAL_FRONT_WHEEL_LEFT_VIEW, l_pVertFrontWheelLeftView);
    addView(CustomViews::VERTICAL_FRONT_WHEEL_RIGHT_VIEW, l_pVertFrontWheelRightView);
    addView(CustomViews::VERTICAL_WHEEL_LEFT_VIEW, l_pVertWheelLeftView);
    addView(CustomViews::VERTICAL_WHEEL_RIGHT_VIEW, l_pVertWheelRightView);
    addView(CustomViews::VERTICAL_REAR_WHEEL_LEFT_VIEW, l_pVertRearWheelLeftView);
    addView(CustomViews::VERTICAL_REAR_WHEEL_RIGHT_VIEW, l_pVertRearWheelRightView);
    addView(CustomViews::VERTICAL_FRONT_JUNCTION_VIEW, l_pVertFrontJunctionView);
    addView(CustomViews::VERTICAL_REAR_JUNCTION_VIEW, l_pVertRearJunctionView);
    addView(CustomViews::VERTICAL_SURROUND_VIEW, l_pVertSurroundView);
    addView(CustomViews::VERTICAL_BONNET_VIEW, l_pVertBonnetView);
    addView(CustomViews::WHEEL_SEPARATOR_VERTICAL_VIEW, l_pWheelSeparatorVerticalView);
    addView(CustomViews::VERTICAL_SIDE_FRONT_VIEW, l_pVertSideFrontView);
    addView(CustomViews::VERTICAL_SIDE_REAR_VIEW, l_pVertSideRearView);
    addView(CustomViews::VERTICAL_SIDE_LEFT_VIEW, l_pVertSideLeftView);
    addView(CustomViews::VERTICAL_SIDE_RIGHT_VIEW, l_pVertSideRightView);
    addView(CustomViews::VERTICAL_PLAN_VIEW_FULLSCREEN, l_pVertPlanViewFullscreen);
    addView(CustomViews::VERTICAL_PLAN_VIEW_FULLSCREEN_VEHICLE2D, l_pVertPlanViewFullscreenVehicle2D);
    addView(CustomViews::VERTICAL_PLAN_VIEW_FULLSCREEN_USS_OVERLAYS, l_pVertPlanViewFullscreenUSSOverlay);
// addView(CustomViews::PARK_ASSIST_REAR_VIEW_VERT,      l_pAssistVertRearView);
#endif
    addView(CustomViews::WHEEL_SEPARATOR_HORIZONTAL_VIEW, l_pWheelSeparatorHorizontalView);
    addView(CustomViews::CPC_DEBUG_OVERLAY_VIEW, l_CPCCpcDebugScreen);
#if ENABLE_VERTICAL_MODE
    addView(CustomViews::VERT_PARKING_PLAN_VIEW, l_pVertParkingPlanView);
    addView(CustomViews::VERT_PARKING_PLAN_VIEW_VEHICLE2D, l_pVertParkingPlanViewVehicle2D);
    addView(CustomViews::VERT_PARKING_PLAN_VIEW_USS_OVERLAYS, l_pVertParkingPlanViewUSSOverlay);
    addView(CustomViews::VERT_PARKING_FLOOR_PLAN_VIEW, l_pVertParkingFloorPlanView);
    addView(CustomViews::VERT_SEPARATOR_VIEW, l_pVertSeparatorView);
    addView(CustomViews::VERT_PARKING_VIEW, l_pParkingViewVert);
#endif
    addView(CustomViews::PARKING_VIEW, l_pParkingView);
    // addView(CustomViews::PARK_DYNAMIC_GEAR_VIEW, l_dynamicGearView);
    addView(CustomViews::PARK_ASSIST_FRONT_VIEW, l_pAssitFrontView);
    addView(CustomViews::PARK_ASSIST_REAR_VIEW, l_pAssitRearView);
    addView(CustomViews::FRONT_BUMPER_VIEW,               l_pFrontBumperView);
    addView(CustomViews::REAR_BUMPER_VIEW,                l_pRearBumperView);
    addView(CustomViews::IMAGE_IMAGE_LEFT, l_pImageinImage_Left);
    addView(CustomViews::IMAGE_IMAGE_RIGHT, l_pImageinImage_Right);
    addView(CustomViews::IMAGE_IMAGE_PLANVIEW, l_pImageinImagePlanView);
    addView(CustomViews::IMAGE_IN_IMAGE_VEHICLE_2D, l_pImageinImagePlanViewVehicle2D);
    addView(CustomViews::IMAGE_IN_IMAGE_USS_OVERLAY, l_pImageinImagePlanViewFullscreenUSSOverlay);
    // addView(CustomViews::SUPER_TRANSPARENT_VIEW, l_pSuperTransparentView);
    addView(CustomViews::FULL_SCREEN_TURN_AROUND_IN_PLACE, l_pPlanViewFullscreenTR);
    addView(CustomViews::FULL_SCREEN_HORIZONTAL_MOVE, l_pPlanViewFullscreenHM);
    addView(CustomViews::IMAGE_IN_IMAGE_FRONT,              l_frontViewPark);
    addView(CustomViews::IMAGE_IN_IMAGE_REAR,               l_rearViewPark);
    addView(CustomViews::IMAGE_IN_IMAGE_PARK_TOP,           l_pImageinImageParkPlanView);
    addView(CustomViews::IMAGE_IN_IMAGE_PARK_USS_OVERLAY,   l_pImageinImagePlanViewParkUSSOverlay);
    addView(CustomViews::IMAGE_IN_IMAGE_PARK_VEHICLE_2D,    l_pImageinImageParkPlanViewVehicle2D);
    addView(CustomViews::FRONT_COMBINED_VIEW,               l_pFrontCombinedLeftRightView);
    addView(CustomViews::REAR_COMBINED_VIEW,                l_pRearCombinedLeftRightView);
    addView(CustomViews::PLANETRY_VIEW_VEHICLE_2D,          l_pPlanetaryViewVehicle2D);
    addView(CustomViews::COMPASS_TURN_AROUND_IN_PLACE, l_pPlanViewCompassTR);
    addView(CustomViews::CARB_TURN_AROUND_IN_PLACE, l_pPlanViewCarbTR);
    addView(CustomViews::IMAGE_IMAGE_LEFT_REAR, l_pImageinImage_LeftRear);
    addView(CustomViews::IMAGE_IMAGE_RIGHT_REAR, l_pImageinImage_RightRear);

#if defined(ENABLE_IMGUI)
    if (m_enableImgui)
    {
        auto imguiView = new cc::views::imgui::ImGuiView(
            "ImGui View",
            g_views->m_usableCanvasViewport,
            virtcam::g_positions->getPosition(virtcam::VCAM_FRONT_VIEW),
            getFramework());
        addView(CustomViews::IMGUI_VIEW, imguiView);
    }
#endif

    // pf code. #code looks fine
    XLOG_INFO(g_EngineContext, "End of Custom Scene initialization"); // PRQA S 4060
}


pc::worker::bowlshaping::SuperEllipsePolarBowlLayoutGenerator* CustomScene::createSuperEllipseLayoutGenerator()
{
    return new pc::worker::bowlshaping::SuperEllipsePolarBowlLayoutGenerator(
        pc::worker::bowlshaping::g_bowlShaperDefault->m_numRadialSections,
        pc::vehicle::g_mechanicalData->getCenter(),
        pc::worker::bowlshaping::g_bowlShaperDefault->m_bowlDefault.m_semiaxis.x(),
        pc::worker::bowlshaping::g_bowlShaperDefault->m_bowlDefault.m_semiaxis.y(),
        static_cast<vfc::int32_t>(pc::worker::bowlshaping::g_bowlShaperDefault->m_bowlDefault.m_n));
}

const pc::core::Asset* CustomScene::getBowlAsset() const
{
    return m_pBowlAsset;
}

const pc::core::Asset* CustomScene::getFloorAsset() const
{
    return m_pFloorAsset;
}

} // namespace core
} // namespace cc
