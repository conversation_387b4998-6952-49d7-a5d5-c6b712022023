//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#include "ViewModeToggle.h"
#include "cc/core/inc/CustomScene.h"

#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/Scene.h"
#include "pc/svs/core/inc/View.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace core
{

void DefaultViewToggleAction::setViewEnabled(vfc::int32_t f_view, bool f_enabled)
{
    XLOG_INFO(g_AppContext, "**************** view: " << f_view << " ; enable: " << f_enabled << " ;*************");

    setViewEnabledHorizontal(f_view, f_enabled);
#if ENABLE_VERTICAL_MODE
    setViewEnabledVertical(f_view, f_enabled);
#endif //  ENABLE_VERTICAL_MODE
}

void DefaultViewToggleAction::setFrontViewEnabled(bool f_enabled) // PRQA S 4211
{
    if (!f_enabled)
    {
        m_scene->setViewEnabled(CustomViews::FRONT_VIEW_PANO, false);
        m_scene->setViewEnabled(CustomViews::FRONT_VIEW, false);
    }
    else if (m_distortionOn)
    {
        m_scene->setViewEnabled(CustomViews::FRONT_VIEW_PANO, f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_VIEW, !f_enabled);
    }
    else
    {
        m_scene->setViewEnabled(CustomViews::FRONT_VIEW_PANO, !f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_VIEW, f_enabled);
    }
}

void DefaultViewToggleAction::setRearViewEnabled(bool f_enabled) // PRQA S 4211
{
    if (!f_enabled)
    {
        m_scene->setViewEnabled(CustomViews::REAR_VIEW_PANO, false);
        m_scene->setViewEnabled(CustomViews::REAR_VIEW, false);
    }
    else if (m_distortionOn)
    {
        m_scene->setViewEnabled(CustomViews::REAR_VIEW_PANO, f_enabled);
        m_scene->setViewEnabled(CustomViews::REAR_VIEW, !f_enabled);
    }
    else
    {
        m_scene->setViewEnabled(CustomViews::REAR_VIEW_PANO, !f_enabled);
        m_scene->setViewEnabled(CustomViews::REAR_VIEW, f_enabled);
    }
}

void DefaultViewToggleAction::setTopViewEnabled(bool f_enabled)
{
    if (!f_enabled)
    {
        m_scene->setViewEnabled(CustomViews::FRONT_VIEW_PANO, false);
        m_scene->setViewEnabled(CustomViews::FRONT_VIEW, false);
        // m_scene->setViewEnabled(CustomViews::FRONT_JUNCTION_VIEW, false);
        m_scene->setViewEnabled(CustomViews::REAR_VIEW_PANO, false);
        m_scene->setViewEnabled(CustomViews::REAR_VIEW, false);
        // m_scene->setViewEnabled(CustomViews::REAR_JUNCTION_VIEW, false);
        return;
    }

    switch (m_screenIdStruct.m_topEnableStatus)
    {
    case cc::daddy::TOPVIEW_FRONT_ENABLE:
    case cc::daddy::TOPVIEW_FRONTJUNCTION_ENABLE:
    {
        setFrontViewEnabled(true);
        break;
    }
    // case cc::daddy::TOPVIEW_FRONTJUNCTION_ENABLE:
    //     m_scene->setViewEnabled(CustomViews::FRONT_JUNCTION_VIEW, f_enabled);
    //     break;
    case cc::daddy::TOPVIEW_REAR_ENABLE:
    case cc::daddy::TOPVIEW_REARJUNCTION_ENABLE:
    {
        setRearViewEnabled(true);
        break;
    }
    case cc::daddy::TOPVIEW_DISABLE:
    default:
    {
        break;
    }
    // case cc::daddy::TOPVIEW_REARJUNCTION_ENABLE:
    //     m_scene->setViewEnabled(CustomViews::REAR_JUNCTION_VIEW, f_enabled);
    //     break;
    }
}

void DefaultViewToggleAction::setSideViewEnabled(bool f_enabled)
{
    if (!f_enabled)
    {
        m_scene->setViewEnabled(CustomViews::FRONT_COMBINED_VIEW, false);
        m_scene->setViewEnabled(CustomViews::REAR_COMBINED_VIEW, false);
        m_scene->setViewEnabled(CustomViews::PARK_ASSIST_FRONT_VIEW, false);
        m_scene->setViewEnabled(CustomViews::PARK_ASSIST_REAR_VIEW, false);
        return;
    }

    switch (m_screenIdStruct.m_sideEnableStatus)
    {

    case cc::daddy::SIDEVIEW_FRONT_ENABLE:
    {
        m_scene->setViewEnabled(CustomViews::PARK_ASSIST_FRONT_VIEW, true);
        break;
    }
    case cc::daddy::SIDEVIEW_REAR_ENABLE:
    {
        m_scene->setViewEnabled(CustomViews::PARK_ASSIST_REAR_VIEW, true);
        break;
    }
    // case cc::daddy::SIDEVIEW_LEFT_ENABLE:
    //     m_scene->setViewEnabled(CustomViews::FRONT_COMBINED_VIEW, true);
    //     break;
    // case cc::daddy::SIDEVIEW_RIGHT_ENABLE:
    //     m_scene->setViewEnabled(CustomViews::REAR_COMBINED_VIEW, true);
    //     break;
    case cc::daddy::SIDEVIEW_LEFT_ENABLE:
    case cc::daddy::SIDEVIEW_RIGHT_ENABLE:
    case cc::daddy::SIDEVIEW_LEFT_RIGHT_ENABLE:
    {
        m_scene->setViewEnabled(CustomViews::REAR_COMBINED_VIEW, false);
        m_scene->setViewEnabled(CustomViews::FRONT_COMBINED_VIEW, true);
        break;
    }
    case cc::daddy::SIDEVIEW_LEFT_REAR_ENABLE:
    case cc::daddy::SIDEVIEW_RIGHT_REAR_ENABLE:
    case cc::daddy::SIDEVIEW_LEFT_RIGHT_REAR_ENABLE:
    {
        m_scene->setViewEnabled(CustomViews::REAR_COMBINED_VIEW, true);
        m_scene->setViewEnabled(CustomViews::FRONT_COMBINED_VIEW, false);
        break;
    }
    case cc::daddy::SIDEVIEW_DISABLE:
    {
        break;
    }
    default:
    {
        break;
    }
    }
}

void DefaultViewToggleAction::setImageinImageViewportEnable(bool f_enabled)
{
    // ImageInimage left&right
    m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_FRONT, f_enabled);
    m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_REAR, f_enabled);
    // ImageInimage left&right
    m_scene->setViewEnabled(CustomViews::IMAGE_IMAGE_LEFT, f_enabled);
    m_scene->setViewEnabled(CustomViews::IMAGE_IMAGE_RIGHT, f_enabled);
    // ImageInimage planview
    m_scene->setViewEnabled(CustomViews::IMAGE_IMAGE_PLANVIEW, f_enabled);
    m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_USS_OVERLAY, f_enabled);
    m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_VEHICLE_2D, f_enabled);
    // Freeparking
    m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW, f_enabled);
    m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW_USS_OVERLAYS, f_enabled);
    m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW_VEHICLE2D, f_enabled);
    // ImageInimage park planview
    m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_PARK_TOP, f_enabled);
    m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_PARK_USS_OVERLAY, f_enabled);
    m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_PARK_VEHICLE_2D, f_enabled);
}

void DefaultViewToggleAction::setViewEnabledHorizontal(vfc::int32_t f_view, bool f_enabled)
{
    switch (f_view)
    {
    case EScreenID_SINGLE_REAR_NORMAL_ON_ROAD:
    case EScreenID_MODEL_B_VIEW_ENLARGEMENT:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        setRearViewEnabled(f_enabled);
        setSideViewEnabled(f_enabled);
        break;
    }
    case EScreenID_SINGLE_FRONT_NORMAL:
    case EScreenID_MODEL_F_VIEW_ENLARGEMENT:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        setFrontViewEnabled(f_enabled);
        setSideViewEnabled(f_enabled);
        break;
    }
    case EScreenID_SINGLE_FRONT_JUNCTION:
    {
        setFrontViewEnabled(f_enabled);
        // m_scene->setViewEnabled(CustomViews::FRONT_JUNCTION_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        setSideViewEnabled(f_enabled);
        break;
    }
    case EScreenID_SINGLE_REAR_JUNCTION:
    {
        setRearViewEnabled(f_enabled);
        // m_scene->setViewEnabled(CustomViews::REAR_JUNCTION_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        setSideViewEnabled(f_enabled);
        break;
    }
    case EScreenID_WHEEL_REAR_DUAL:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::REAR_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::REAR_WHEEL_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::WHEEL_SEPARATOR_HORIZONTAL_VIEW, f_enabled);
        setSideViewEnabled(f_enabled);
        break;
    }
    case EScreenID_SINGLE_FRONT_RAW:
    {
        m_scene->setViewEnabled(CustomViews::RAW_FISHEYE_FRONT, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        break;
    }
    case EScreenID_QUAD_RAW:
    {
        m_scene->setViewEnabled(CustomViews::RAW_FISHEYE_QUAD, f_enabled);
        m_scene->setViewEnabled(CustomViews::CPC_DEBUG_OVERLAY_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        break;
    }
    case EScreenID_PERSPECTIVE_FL:
    case EScreenID_PERSPECTIVE_FR:
    case EScreenID_PERSPECTIVE_RL:
    case EScreenID_PERSPECTIVE_RR:
    case EScreenID_PERSPECTIVE_KL:
    case EScreenID_PERSPECTIVE_KR:
    case EScreenID_PERSPECTIVE_PFR:
    case EScreenID_PERSPECTIVE_PRI:
    case EScreenID_PERSPECTIVE_PRE:
    case EScreenID_PERSPECTIVE_PLE:
    case EScreenID_NORMAL3D_KEEP:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::SURROUND_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_COMBINED_VIEW, f_enabled);
        // m_scene->setViewEnabled(CustomViews::REAR_COMBINED_VIEW, f_enabled);
        setSideViewEnabled(f_enabled);
        break;
    }
    case EScreenID_FRONT_BUMPER:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_BUMPER_VIEW, f_enabled);
        setSideViewEnabled(f_enabled);
        break;
    }
    case EScreenID_REAR_BUMPER:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::REAR_BUMPER_VIEW, f_enabled);
        setSideViewEnabled(f_enabled);
        break;
    }
    case EScreenID_PLANETARY_VIEW:
    {
        // m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        // m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        // m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLANETARY_VIEW, f_enabled);
        //  m_scene->setViewEnabled(CustomViews::PLAN_VIEW_FULLSCREEN_VEHICLE2D, f_enabled);
        //  m_scene->setViewEnabled(CustomViews::PLAN_VIEW_FULLSCREEN_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLANETRY_VIEW_VEHICLE_2D, f_enabled);
        setSideViewEnabled(f_enabled);
        break;
    }
    case EScreenID_ULTRA_WIDE_SURROUND_VIEW:
    {
        // m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        // m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        // m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::SURROUND_VIEW, f_enabled);
        //  m_scene->setViewEnabled(CustomViews::PLAN_VIEW_FULLSCREEN_VEHICLE2D, f_enabled);
        //  m_scene->setViewEnabled(CustomViews::PLAN_VIEW_FULLSCREEN_USS_OVERLAYS, f_enabled);
        setSideViewEnabled(f_enabled);
        break;
    }
    case EScreenID_THREAT_FRONT:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_THREAT_VIEW, f_enabled);
        break;
    }
    case EScreenID_THREAT_REAR:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::REAR_THREAT_VIEW, f_enabled);
        break;
    }
    case EScreenID_SINGLE_LEFT:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::SINGLE_LEFT_VIEW, f_enabled);
        break;
    }
    case EScreenID_SINGLE_RIGHT:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::SINGLE_RIGHT_VIEW, f_enabled);
        break;
    }
    case EScreenID_SINGLE_LEFT_REAR:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::SINGLE_REAR_LEFT_VIEW, f_enabled);
        break;
    }
    case EScreenID_SINGLE_RIGHT_REAR:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::SINGLE_REAR_RIGHT_VIEW, f_enabled);
        break;
    }
    case EScreenID_WHEEL_FRONT_DUAL:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_WHEEL_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::WHEEL_SEPARATOR_HORIZONTAL_VIEW, f_enabled);
        setSideViewEnabled(f_enabled);
        break;
    }
    case EScreenID_PLANVIEW_WITH_SEPARATOR:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::BOTH_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::BOTH_WHEEL_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::WHEEL_SEPARATOR_HORIZONTAL_VIEW, f_enabled);
        setSideViewEnabled(f_enabled);
        break;
    }
    case EScreenID_WHEEL_FRONT_DUAL_ENLARGED:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_WHEEL_LEFT_ENLARGED_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_WHEEL_RIGHT_ENLARGED_VIEW, f_enabled);
        setSideViewEnabled(f_enabled);
        break;
    }
    case EScreenID_PLANVIEW_WITH_SEPARATOR_ENLARGED:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::BOTH_WHEEL_LEFT_ENLARGED_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::BOTH_WHEEL_RIGHT_ENLARGED_VIEW, f_enabled);
        setSideViewEnabled(f_enabled);
        break;
    }
    case EScreenID_WHEEL_REAR_DUAL_ENLARGED:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::REAR_WHEEL_LEFT_ENLARGED_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::REAR_WHEEL_RIGHT_ENLARGED_VIEW, f_enabled);
        setSideViewEnabled(f_enabled);
        break;
    }
    case EScreenID_FULL_SCREEN:
    {
        m_scene->setViewEnabled(CustomViews::FULL_SCREEN_VIEW, f_enabled);
        break;
    }
    case EScreenID_CAM_CALIB_ENG:
    {
        m_scene->setViewEnabled(CustomViews::RAW_FISHEYE_QUAD, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::CALIB_ENGINEERING_VIEW, f_enabled);
        break;
    }
    // Remote mode
    case EScreenID_REMOTE_SCREEN_FRONT:
    {
        m_scene->setViewEnabled(CustomViews::REMOTE_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::REMOTE_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::REMOTE_FRONT_VIEW, f_enabled);
        break;
    }
    case EScreenID_REMOTE_SCREEN_REAR:
    {
        m_scene->setViewEnabled(CustomViews::REMOTE_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::REMOTE_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::REMOTE_REAR_VIEW, f_enabled);
        break;
    }
    case EScreenID_REMOTE_SCREEN_LEFT:
    {
        m_scene->setViewEnabled(CustomViews::REMOTE_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::REMOTE_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::REMOTE_LEFT_VIEW, f_enabled);
        break;
    }
    case EScreenID_REMOTE_SCREEN_RIGHT:
    {
        m_scene->setViewEnabled(CustomViews::REMOTE_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::REMOTE_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::REMOTE_RIGHT_VIEW, f_enabled);
        break;
    }
    case EScreenID_LEFTRIGHTVIEW_FRONT_VIEW: // TO DO - check with chencheng
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::PARK_ASSIST_FRONT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_COMBINED_VIEW, f_enabled);
        // m_scene->setViewEnabled(CustomViews::REAR_COMBINED_VIEW, f_enabled);
        break;
    }
    case EScreenID_LEFTRIGHTVIEW_REAR_VIEW:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::PARK_ASSIST_REAR_VIEW, f_enabled); // TODO
        m_scene->setViewEnabled(CustomViews::REAR_LEFTVIEW_IN_FOUR_WINDOW, f_enabled);
        m_scene->setViewEnabled(CustomViews::REAR_RIGHTVIEW_IN_FOUR_WINDOW, f_enabled);
        break;
    }
    case EScreenID_HORI_PARKING_FRONT:
    {
        m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW_VEHICLE2D, f_enabled);
        // m_scene->setViewEnabled(CustomViews::PARK_SEARCHING_VIEW, f_enabled);
        setFrontViewEnabled(f_enabled);
        break;
    }
    case EScreenID_HORI_PARKING_REAR:
    {
        m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW_VEHICLE2D, f_enabled);
        // m_scene->setViewEnabled(CustomViews::PARK_SEARCHING_VIEW, f_enabled);
        setRearViewEnabled(f_enabled);
        break;
    }
    case EScreenID_PARKING_FREEPARKING:
    {
        // m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_FRONT, f_enabled);
        // m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_REAR, f_enabled);
        // m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_PARK_TOP, f_enabled);
        // m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_PARK_USS_OVERLAY, f_enabled);
        // m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_PARK_VEHICLE_2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW_VEHICLE2D, f_enabled);
        // m_scene->setViewEnabled(CustomViews::PARK_SEARCHING_VIEW, f_enabled);
        break;
    }
    case EScreenID_PARKING_FRONT:
    {
        setFrontViewEnabled(f_enabled);
        // m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_FRONT, f_enabled);
        // m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_REAR, f_enabled);
        // m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_PARK_TOP, f_enabled);
        // m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_PARK_USS_OVERLAY, f_enabled);
        // m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_PARK_VEHICLE_2D, f_enabled);
        // m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW, f_enabled);
        // m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        // m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW_VEHICLE2D, f_enabled);
        // m_scene->setViewEnabled(CustomViews::PARK_SEARCHING_VIEW, f_enabled);
        break;
    }
    case EScreenID_PARKING_REAR:
    {
        setRearViewEnabled(f_enabled);
        // m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_FRONT, f_enabled);
        // m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_REAR, f_enabled);
        // m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_PARK_TOP, f_enabled);
        // m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_PARK_USS_OVERLAY, f_enabled);
        // m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_PARK_VEHICLE_2D, f_enabled);
        // m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW, f_enabled);
        // m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        // m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW_VEHICLE2D, f_enabled);
        // m_scene->setViewEnabled(CustomViews::PARK_SEARCHING_VIEW, f_enabled);
        break;
    }
    case EScreenID_SINGLE_STB:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::BONNET_VIEW, f_enabled);
        break;
    }
    case EScreenID_FULLSCREEN:
    case EScreenID_FULLSCREEN_FRONT_ENLARGE:
    case EScreenID_FULLSCREEN_REAR_ENLARGE:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        setSideViewEnabled(f_enabled);
        // m_scene->setViewEnabled(CustomViews::PLAN_VIEW_FULLSCREEN, f_enabled);
        // m_scene->setViewEnabled(CustomViews::PLAN_VIEW_FULLSCREEN_VEHICLE2D, f_enabled);
        // m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        // m_scene->setViewEnabled(CustomViews::PLAN_VIEW_FULLSCREEN_USS_OVERLAYS, f_enabled);
        break;
    }
    case EScreenID_IMAGE_IN_IMAGE_LEFT:
    {
        m_scene->setViewEnabled(CustomViews::IMAGE_IMAGE_LEFT, f_enabled);
        break;
    }
    case EScreenID_IMAGE_IN_IMAGE_RIGHT:
    {
        m_scene->setViewEnabled(CustomViews::IMAGE_IMAGE_RIGHT, f_enabled);
        break;
    }
    case EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT:
    {
        m_scene->setViewEnabled(CustomViews::IMAGE_IMAGE_LEFT, f_enabled);
        m_scene->setViewEnabled(CustomViews::IMAGE_IMAGE_RIGHT, f_enabled);
        break;
    }
    case EScreenID_IMAGE_IN_IMAGE_PLANVIEW:
    case EScreenID_IMAGE_IN_IMAGE_PLANVIEW_FRONT_ENLARGE:
    case EScreenID_IMAGE_IN_IMAGE_PLANVIEW_REAR_ENLARGE:
    {
        m_scene->setViewEnabled(CustomViews::IMAGE_IMAGE_PLANVIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_USS_OVERLAY, f_enabled);
        m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_VEHICLE_2D, f_enabled);
        break;
    }
    case EScreenID_PARKING_TOP:
    {
        // m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_FRONT, f_enabled);
        // m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_REAR, f_enabled);
        m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_PARK_TOP, f_enabled);
        m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_PARK_USS_OVERLAY, f_enabled);
        m_scene->setViewEnabled(CustomViews::IMAGE_IN_IMAGE_PARK_VEHICLE_2D, f_enabled);
        // m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW, f_enabled);
        // m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        // m_scene->setViewEnabled(CustomViews::HORI_PARKING_PLAN_VIEW_VEHICLE2D, f_enabled);
        // m_scene->setViewEnabled(CustomViews::PARK_SEARCHING_VIEW, f_enabled);
        break;
    }
    case EScreenID_AVM3D_VIEW_FULL_SCREEN_2D_VOT:
    {
        m_scene->setViewEnabled(CustomViews::FULL_SCREEN_TURN_AROUND_IN_PLACE, f_enabled);
        break;
    }
    case EScreenID_COMPASS_TURN:
    {
        m_scene->setViewEnabled(CustomViews::COMPASS_TURN_AROUND_IN_PLACE, f_enabled);
        break;
    }
    case EScreenID_CARB_TURN:
    {
        m_scene->setViewEnabled(CustomViews::CARB_TURN_AROUND_IN_PLACE, f_enabled);
        break;
    }
    case EscreenID_FULL_SCREEN_3D:
    {
        m_scene->setViewEnabled(CustomViews::SURROUND_VIEW, f_enabled);
        break;
    }
    case EScreenID_FIND_CAR_VIEW:
    {
        m_scene->setViewEnabled(CustomViews::REMOTE_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::REMOTE_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::REMOTE_REAR_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::SURROUND_VIEW_FIND_CAR, f_enabled);
        break;
    }
    case EScreenID_UPVIEWCOFIG_WHEEL_REAR_DUAL:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        // m_scene->setViewEnabled(CustomViews::REAR_WHEEL_LEFT_VIEW, f_enabled);
        // m_scene->setViewEnabled(CustomViews::REAR_WHEEL_RIGHT_VIEW, f_enabled);
        // m_scene->setViewEnabled(CustomViews::WHEEL_SEPARATOR_HORIZONTAL_VIEW, f_enabled);
        setSideViewEnabled(f_enabled);
        setTopViewEnabled(f_enabled);
        break;
    }
    case EScreenID_UPVIEWCOFIG_WHEEL_FRONT_DUAL:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        // m_scene->setViewEnabled(CustomViews::FRONT_WHEEL_LEFT_VIEW, f_enabled);
        // m_scene->setViewEnabled(CustomViews::FRONT_WHEEL_RIGHT_VIEW, f_enabled);
        // m_scene->setViewEnabled(CustomViews::WHEEL_SEPARATOR_HORIZONTAL_VIEW, f_enabled);
        setSideViewEnabled(f_enabled);
        setTopViewEnabled(f_enabled);
        break;
    }
    case EScreenID_UPVIEWCOFIG_WHEEL_BOTH:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        // m_scene->setViewEnabled(CustomViews::BOTH_WHEEL_LEFT_VIEW, f_enabled);
        // m_scene->setViewEnabled(CustomViews::BOTH_WHEEL_RIGHT_VIEW, f_enabled);
        // m_scene->setViewEnabled(CustomViews::WHEEL_SEPARATOR_HORIZONTAL_VIEW, f_enabled);
        setSideViewEnabled(f_enabled);
        setTopViewEnabled(f_enabled);
        break;
    }
    case AVM3D_VIEW_FULL_SCREEN_2D_VSW:
    {
        m_scene->setViewEnabled(CustomViews::FULL_SCREEN_HORIZONTAL_MOVE, f_enabled);
        break;
    }
    case EScreenID_APA_FRONT_HUBVIEW:
    {
        m_scene->setViewEnabled(CustomViews::FRONT_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_WHEEL_RIGHT_VIEW, f_enabled);
        break;
    }
    case EScreenID_IMAGE_IN_IMAGE_LEFT_REAR:
    {
        m_scene->setViewEnabled(CustomViews::IMAGE_IMAGE_LEFT_REAR, f_enabled);
        break;
    }
    case EScreenID_IMAGE_IN_IMAGE_RIGHT_REAR:
    {
        m_scene->setViewEnabled(CustomViews::IMAGE_IMAGE_RIGHT_REAR, f_enabled);
        break;
    }
    case EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT_REAR:
    {
        m_scene->setViewEnabled(CustomViews::IMAGE_IMAGE_LEFT_REAR, f_enabled);
        m_scene->setViewEnabled(CustomViews::IMAGE_IMAGE_RIGHT_REAR, f_enabled);
        break;
    }
    case EScreenID_WHEEL_FRONT_SINGLE_FRONT_DUAL:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_WHEEL_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_VIEW, f_enabled);
        break;
    }
    case EScreenID_WHEEL_FRONT_SINGLE_REAR_DUAL:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::REAR_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::REAR_WHEEL_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_VIEW, f_enabled);
        break;
    }
    case EScreenID_WHEEL_REAR_SINGLE_FRONT_DUAL:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_WHEEL_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::REAR_VIEW, f_enabled);
        break;
    }
    case EScreenID_WHEEL_REAR_SINGLE_REAR_DUAL:
    {
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::REAR_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::REAR_WHEEL_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::REAR_VIEW, f_enabled);
        break;
    }
    case EScreenID_WHEEL_FRONT_SINGLE_FRONT_DUAL_WITHOUT_PLAN:
    {
        m_scene->setViewEnabled(CustomViews::FRONT_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_WHEEL_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_VIEW, f_enabled);
        break;
    }
    case EScreenID_WHEEL_FRONT_SINGLE_REAR_DUAL_WITHOUT_PLAN:
    {
        m_scene->setViewEnabled(CustomViews::REAR_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::REAR_WHEEL_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_VIEW, f_enabled);
        break;
    }
    case EScreenID_WHEEL_REAR_SINGLE_FRONT_DUAL_WITHOUT_PLAN:
    {
        m_scene->setViewEnabled(CustomViews::FRONT_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::FRONT_WHEEL_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::REAR_VIEW, f_enabled);
        break;
    }
    case EScreenID_WHEEL_REAR_SINGLE_REAR_DUAL_WITHOUT_PLAN:
    {
        m_scene->setViewEnabled(CustomViews::REAR_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::REAR_WHEEL_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::REAR_VIEW, f_enabled);
        break;
    }
    case EScreenID_NO_VIDEO_SYSTEM:
    case EScreenID_NO_VIDEO_USER:
    default:
    {
        //! do nothing, empty screen
        break;
    }
    }
}

void DefaultViewToggleAction::setViewEnabledVertical(vfc::int32_t f_view, bool f_enabled)
{
    switch (f_view)
    {
    case EScreenID_VERT_SINGLE_FRONT:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_FRONT_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_SINGLE_REAR:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_REAR_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_SINGLE_FRONT_JUNCTION:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_FRONT_JUNCTION_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_SINGLE_REAR_JUNCTION:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_REAR_JUNCTION_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_SINGLE_LEFT:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_LEFT_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_SINGLE_RIGHT:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_RIGHT_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_WHEEL_FRONT_DUAL:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_FRONT_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_FRONT_WHEEL_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::WHEEL_SEPARATOR_VERTICAL_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_WHEEL_REAR_DUAL:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_REAR_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_REAR_WHEEL_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::WHEEL_SEPARATOR_VERTICAL_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_PLANVIEW_WITH_SEPARATOR:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_WHEEL_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::WHEEL_SEPARATOR_VERTICAL_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_2D_FRONTVIEW_RIGHTVIEW:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_FRONT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_RIGHT_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_2D_FRONTVIEW_LEFTVIEW:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_FRONT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_LEFT_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_2D_REARVIEW_RIGHTVIEW:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_REAR_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_RIGHT_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_2D_REARVIEW_LEFTVIEW:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_REAR_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_LEFT_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_PERSPECTIVE_PFR:
    case EScreenID_VERT_PERSPECTIVE_PRE:
    case EScreenID_VERT_PERSPECTIVE_RL:
    case EScreenID_VERT_PERSPECTIVE_RR:
    case EScreenID_VERT_PERSPECTIVE_FL:
    case EScreenID_VERT_PERSPECTIVE_FR:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SURROUND_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_PERSPECTIVE_FR_L:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SURROUND_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_LEFT_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_PERSPECTIVE_FR_R:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SURROUND_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_RIGHT_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_PERSPECTIVE_FL_L:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SURROUND_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_LEFT_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_PERSPECTIVE_FL_R:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SURROUND_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_RIGHT_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_PERSPECTIVE_RR_L:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SURROUND_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_LEFT_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_PERSPECTIVE_RR_R:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SURROUND_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_RIGHT_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_PERSPECTIVE_RL_L:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SURROUND_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_LEFT_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_PERSPECTIVE_RL_R:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SURROUND_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_RIGHT_VIEW, f_enabled);
        break;
        break;
    }
    case EScreenID_VERT_PARKING_FRONT:
    {
        m_scene->setViewEnabled(CustomViews::VERT_PARKING_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERT_PARKING_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERT_PARKING_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERT_PARKING_FLOOR_PLAN_VIEW, f_enabled);
        // m_scene->setViewEnabled(CustomViews::VERTICAL_FRONT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERT_PARKING_VIEW, f_enabled);
        // m_scene->setViewEnabled(CustomViews::PARK_SEARCHING_VIEW, f_enabled); // Denza parking ui view
        m_scene->setViewEnabled(CustomViews::PARK_DYNAMIC_GEAR_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_PARKING_REAR:
    {
        m_scene->setViewEnabled(CustomViews::VERT_PARKING_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERT_PARKING_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERT_PARKING_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERT_PARKING_FLOOR_PLAN_VIEW, f_enabled);
        // m_scene->setViewEnabled(CustomViews::VERTICAL_REAR_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERT_PARKING_VIEW, f_enabled);
        // m_scene->setViewEnabled(CustomViews::PARK_SEARCHING_VIEW, f_enabled); // Denza parking ui view
        m_scene->setViewEnabled(CustomViews::PARK_DYNAMIC_GEAR_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_SINGLE_STB:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_BONNET_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__LEFT_SIDE:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_FRONT_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__RIGHT_SIDE:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_FRONT_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__LEFT_SIDE:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_REAR_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__RIGHT_SIDE:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_REAR_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_FULLSCREEN:
    case EScreenID_VERT_FULLSCREEN_FRONT_ENLARGE:
    case EScreenID_VERT_FULLSCREEN_REAR_ENLARGE:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_FULLSCREEN, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_FULLSCREEN_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_FULLSCREEN_USS_OVERLAYS, f_enabled);
        break;
    }
    case EScreenID_VERT_PLAN__DUAL_FRONT_WHEEL__FRONT_SIDE:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_FRONT_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_FRONT_WHEEL_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_FRONT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::WHEEL_SEPARATOR_VERTICAL_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_PLAN__DUAL_FRONT_WHEEL__REAR_SIDE:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_FRONT_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_FRONT_WHEEL_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_REAR_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::WHEEL_SEPARATOR_VERTICAL_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_PLAN__DUAL_REAR_WHEEL__FRONT_SIDE:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_REAR_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_REAR_WHEEL_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_FRONT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::WHEEL_SEPARATOR_VERTICAL_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_PLAN__DUAL_REAR_WHEEL__REAR_SIDE:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_REAR_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_REAR_WHEEL_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_REAR_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::WHEEL_SEPARATOR_VERTICAL_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_PLAN__DUAL_WHEEL__FRONT_SIDE:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_WHEEL_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_FRONT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::WHEEL_SEPARATOR_VERTICAL_VIEW, f_enabled);
        break;
    }
    case EScreenID_VERT_PLAN__DUAL_WHEEL__REAR_SIDE:
    {
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_PLAN_VIEW_USS_OVERLAYS, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_WHEEL_LEFT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_WHEEL_RIGHT_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::VERTICAL_SIDE_REAR_VIEW, f_enabled);
        m_scene->setViewEnabled(CustomViews::WHEEL_SEPARATOR_VERTICAL_VIEW, f_enabled);
        break;
    }
    default:
    {
        break;
    }
    }
}

void DefaultViewToggleAction::onAction()
{
    setViewEnabled(m_mode.m_prev, false);
    setViewEnabled(m_preView, false);
    setViewEnabled(m_mode.m_curr, true);
    m_preView = m_mode.m_curr;
}

cc::virtcam::VirtualCamEnum convertCameraModeToVirtualCameraPosition(vfc::int32_t f_mode)
{
    switch (f_mode)
    {
    case EScreenID_PERSPECTIVE_FL:
    {
        return cc::virtcam::VCAM_FRONT_LEFT_VIEW;
    }
    case EScreenID_PERSPECTIVE_KL:
    {
        return cc::virtcam::VCAM_KERB_LEFT_VIEW;
    }
    case EScreenID_PERSPECTIVE_RL:
    {
        return cc::virtcam::VCAM_REAR_LEFT_VIEW;
    }
    case EScreenID_NORMAL3D_KEEP:
    {
        return cc::virtcam::VCAM_NORMAL_3D_VIEW;
    }
    case EscreenID_FULL_SCREEN_3D:
    {
        return cc::virtcam::VCAM_FULL_SCREEN_3D_VIEW;
    }
    case EScreenID_PERSPECTIVE_FR:
    {
        return cc::virtcam::VCAM_FRONT_RIGHT_VIEW;
    }
    case EScreenID_PERSPECTIVE_KR:
    {
        return cc::virtcam::VCAM_KERB_RIGHT_VIEW;
    }
    case EScreenID_PERSPECTIVE_RR:
    {
        return cc::virtcam::VCAM_REAR_RIGHT_VIEW;
    }
    case EScreenID_PERSPECTIVE_PFR:
    case EScreenID_FIND_CAR_VIEW:
    {
        return cc::virtcam::VCAM_PERS_FRONT_VIEW;
    }
    case EScreenID_PERSPECTIVE_PRI:
    {
        return cc::virtcam::VCAM_PERS_RIGHT_VIEW;
    }
    case EScreenID_PERSPECTIVE_PRE:
    {
        return cc::virtcam::VCAM_PERS_REAR_VIEW;
    }
    case EScreenID_PERSPECTIVE_PLE:
    {
        return cc::virtcam::VCAM_PERS_LEFT_VIEW;
    }
    case EScreenID_SINGLE_REAR_HITCH:
    {
        return cc::virtcam::VCAM_TRAILER_VIEW;
    }
    case EScreenID_SINGLE_REAR_HITCH_ZOOM:
    {
        return cc::virtcam::VCAM_TRAILER_ZOOM_VIEW;
    }
    case EScreenID_PLANETARY_VIEW:
    {
        return cc::virtcam::VCAM_PLANETRAY_VIEW;
    }
    case EScreenID_ULTRA_WIDE_SURROUND_VIEW:
    {
        return cc::virtcam::VCAM_ULTRA_WIDE_SURROUND_VIEW;
    }
    case EScreenID_FRONT_BUMPER:
    {
        return cc::virtcam::VCAM_FRONT_BUMPER_VIEW;
    }
    case EScreenID_REAR_BUMPER:
    {
        return cc::virtcam::VCAM_REAR_BUMPER_VIEW;
    }
#if ENABLE_VERTICAL_MODE
    case EScreenID_VERT_PERSPECTIVE_PFR:
        return cc::virtcam::VCAM_VERT_PERS_FRONT_VIEW;
    case EScreenID_VERT_PERSPECTIVE_PRE:
        return cc::virtcam::VCAM_VERT_PERS_REAR_VIEW;
    case EScreenID_VERT_PERSPECTIVE_RL:
        return cc::virtcam::VCAM_VERT_REAR_LEFT_VIEW;
    case EScreenID_VERT_PERSPECTIVE_RR:
        return cc::virtcam::VCAM_VERT_REAR_RIGHT_VIEW;
    case EScreenID_VERT_PERSPECTIVE_FL:
        return cc::virtcam::VCAM_VERT_FRONT_LEFT_VIEW;
    case EScreenID_VERT_PERSPECTIVE_FR:
        return cc::virtcam::VCAM_VERT_FRONT_RIGHT_VIEW;

    case EScreenID_VERT_PERSPECTIVE_FL_L:
        return cc::virtcam::VCAM_VERT_FRONT_LEFT_VIEW;
    case EScreenID_VERT_PERSPECTIVE_FL_R:
        return cc::virtcam::VCAM_VERT_FRONT_LEFT_VIEW;

    case EScreenID_VERT_PERSPECTIVE_FR_L:
        return cc::virtcam::VCAM_VERT_FRONT_RIGHT_VIEW;
    case EScreenID_VERT_PERSPECTIVE_FR_R:
        return cc::virtcam::VCAM_VERT_FRONT_RIGHT_VIEW;

    case EScreenID_VERT_PERSPECTIVE_RL_L:
        return cc::virtcam::VCAM_VERT_REAR_LEFT_VIEW;
    case EScreenID_VERT_PERSPECTIVE_RL_R:
        return cc::virtcam::VCAM_VERT_REAR_LEFT_VIEW;

    case EScreenID_VERT_PERSPECTIVE_RR_L:
        return cc::virtcam::VCAM_VERT_REAR_RIGHT_VIEW;
    case EScreenID_VERT_PERSPECTIVE_RR_R:
        return cc::virtcam::VCAM_VERT_REAR_RIGHT_VIEW;
#endif
    case EScreenID_NO_VIDEO_USER: // fallthrough
    default:
    {
        return cc::virtcam::NUMBER_OF_VIRT_CAMS; // Indicate invalid camera mode
    }
    }
}

bool isSurroundView(vfc::int32_t f_mode)
{
    switch (f_mode)
    {
    case EScreenID_PERSPECTIVE_FL:
    case EScreenID_PERSPECTIVE_KL:
    case EScreenID_PERSPECTIVE_RL:
    case EScreenID_PERSPECTIVE_FR:
    case EScreenID_PERSPECTIVE_KR:
    case EScreenID_PERSPECTIVE_RR:
    case EScreenID_PERSPECTIVE_PFR:
    case EScreenID_PERSPECTIVE_PRI:
    case EScreenID_PERSPECTIVE_PRE:
    case EScreenID_PERSPECTIVE_PLE:
    // case EScreenID_PLANETARY_VIEW:
    case EScreenID_ULTRA_WIDE_SURROUND_VIEW:
    // case EScreenID_FRONT_BUMPER:
    // case EScreenID_REAR_BUMPER:
    case EscreenID_FULL_SCREEN_3D:
    case EScreenID_NORMAL3D_KEEP:
    case EScreenID_FIND_CAR_VIEW:
    {
        // case EScreenID_HORI_PARKING_FRONT       :
        // case EScreenID_HORI_PARKING_REAR        :
        return true;
    }
    default:
    {
        return false;
    }
    }
}

bool isSurroundViewAngleNeedKeep(vfc::int32_t f_mode)
{
    switch (f_mode)
    {
    case EScreenID_PERSPECTIVE_FL:
    case EScreenID_PERSPECTIVE_KL:
    case EScreenID_PERSPECTIVE_RL:
    case EScreenID_PERSPECTIVE_FR:
    case EScreenID_PERSPECTIVE_KR:
    case EScreenID_PERSPECTIVE_RR:
    case EScreenID_PERSPECTIVE_PFR:
    case EScreenID_PERSPECTIVE_PRI:
    case EScreenID_PERSPECTIVE_PRE:
    case EScreenID_PERSPECTIVE_PLE:
    case EscreenID_FULL_SCREEN_3D:
    case EScreenID_NORMAL3D_KEEP:
    {
        return true;
    }
    default:
    {
        return false;
    }
    }
}

bool isParkingView(vfc::int32_t f_mode)
{
    switch (f_mode)
    {
    case EScreenID_HORI_PARKING_FRONT:
    case EScreenID_HORI_PARKING_REAR:
    {
        return true;
    }
    default:
    {
        return false;
    }
    }
}

#if ENABLE_VERTICAL_MODE
bool isVertSurroundView(vfc::int32_t f_mode)
{
    switch (f_mode)
    {
    case EScreenID_VERT_PERSPECTIVE_PFR:
    case EScreenID_VERT_PERSPECTIVE_PRE:
    case EScreenID_VERT_PERSPECTIVE_RL:
    case EScreenID_VERT_PERSPECTIVE_RR:
    case EScreenID_VERT_PERSPECTIVE_FL:
    case EScreenID_VERT_PERSPECTIVE_FR:
    case EScreenID_VERT_PERSPECTIVE_FL_L:
    case EScreenID_VERT_PERSPECTIVE_FL_R:
    case EScreenID_VERT_PERSPECTIVE_FR_L:
    case EScreenID_VERT_PERSPECTIVE_FR_R:
    case EScreenID_VERT_PERSPECTIVE_RL_L:
    case EScreenID_VERT_PERSPECTIVE_RL_R:
    case EScreenID_VERT_PERSPECTIVE_RR_L:
    case EScreenID_VERT_PERSPECTIVE_RR_R:
        // case EScreenID_VERT_PARKING_FRONT   :
        // case EScreenID_VERT_PARKING_REAR    :
        return true;
    default:
        return false;
    }
}

bool isVertParkingView(vfc::int32_t f_mode)
{
    switch (f_mode)
    {
    case EScreenID_VERT_PARKING_FRONT:
    case EScreenID_VERT_PARKING_REAR:
        return true;
    default:
        return false;
    }
}

#endif
bool isKerbView(vfc::int32_t f_mode)
{
    switch (f_mode)
    {
    case EScreenID_PERSPECTIVE_KL:
    case EScreenID_PERSPECTIVE_KR:
    {
        return true;
    }
    default:
    {
        return false;
    }
    }
}

bool isFullscreenSurroundView(vfc::int32_t f_mode)
{
    switch (f_mode)
    {
    // case EScreenID_PLANETARY_VIEW:
    case EScreenID_ULTRA_WIDE_SURROUND_VIEW:
    case EscreenID_FULL_SCREEN_3D:
    {
        return true;
    }
    default:
    {
        return false;
    }
    }
}

bool isFullscreenPlanView(vfc::int32_t f_mode)
{
    switch (f_mode)
    {
    case EScreenID_FULLSCREEN:
    case EScreenID_FULLSCREEN_FRONT_ENLARGE:
    case EScreenID_FULLSCREEN_REAR_ENLARGE:
    {
        return true;
    }
    default:
    {
        return false;
    }
    }
}

bool isFullscreenPlanOrSurroundView(vfc::int32_t f_mode)
{
    return isFullscreenPlanView(f_mode) || isFullscreenSurroundView(f_mode);
}

bool isAVMRunBackground(vfc::int32_t f_mode)
{
    return (f_mode == EScreenID_NO_CHANGE);
}

static const pc::core::Viewport* getVehicle2DViewport(vfc::int32_t f_mode)
{
    switch (f_mode)
    {
    case EScreenID_SINGLE_REAR_NORMAL_ON_ROAD:
    case EScreenID_MODEL_B_VIEW_ENLARGEMENT:
    case EScreenID_SINGLE_FRONT_NORMAL:
    case EScreenID_MODEL_F_VIEW_ENLARGEMENT:
    case EScreenID_SINGLE_FRONT_JUNCTION:
    case EScreenID_SINGLE_REAR_JUNCTION:
    case EScreenID_WHEEL_REAR_DUAL:
    case EScreenID_SINGLE_FRONT_RAW:
    case EScreenID_SINGLE_REAR_RAW_DIAG:
    case EScreenID_SINGLE_ML_RAW:
    case EScreenID_SINGLE_MR_RAW:
    case EScreenID_PERSPECTIVE_FL:
    case EScreenID_PERSPECTIVE_FR:
    case EScreenID_PERSPECTIVE_RL:
    case EScreenID_PERSPECTIVE_RR:
    case EScreenID_PERSPECTIVE_KL:
    case EScreenID_PERSPECTIVE_KR:
    case EScreenID_PERSPECTIVE_PFR:
    case EScreenID_PERSPECTIVE_PRI:
    case EScreenID_PERSPECTIVE_PRE:
    case EScreenID_PERSPECTIVE_PLE:
    case EScreenID_FRONT_BUMPER:
    case EScreenID_REAR_BUMPER:
    case EScreenID_ULTRA_WIDE_SURROUND_VIEW:
    case EScreenID_THREAT_FRONT:
    case EScreenID_THREAT_REAR:
    case EScreenID_SINGLE_LEFT:
    case EScreenID_SINGLE_RIGHT:
    case EScreenID_WHEEL_FRONT_DUAL:
    case EScreenID_PLANVIEW_WITH_SEPARATOR:
    case EScreenID_CAM_CALIB_ENG:
    case EScreenID_LEFTRIGHTVIEW_FRONT_VIEW:
    case EScreenID_LEFTRIGHTVIEW_REAR_VIEW:
    case EScreenID_SINGLE_STB:
    {
        return &cc::core::g_views->m_planViewport;
    }
    // Remote mode
    case EScreenID_REMOTE_SCREEN_FRONT:
    case EScreenID_REMOTE_SCREEN_REAR:
    case EScreenID_REMOTE_SCREEN_LEFT:
    case EScreenID_REMOTE_SCREEN_RIGHT:
    {
        return &cc::core::g_views->m_remotePlanViewport;
    }
    case EScreenID_VERT_SINGLE_FRONT:
    case EScreenID_VERT_SINGLE_REAR:
    case EScreenID_VERT_SINGLE_LEFT:
    case EScreenID_VERT_SINGLE_RIGHT:
    case EScreenID_VERT_WHEEL_FRONT_DUAL:
    case EScreenID_VERT_WHEEL_REAR_DUAL:
    case EScreenID_VERT_PLANVIEW_WITH_SEPARATOR:
    case EScreenID_VERT_2D_FRONTVIEW_RIGHTVIEW:
    case EScreenID_VERT_2D_FRONTVIEW_LEFTVIEW:
    case EScreenID_VERT_2D_REARVIEW_RIGHTVIEW:
    case EScreenID_VERT_2D_REARVIEW_LEFTVIEW:
    case EScreenID_VERT_PERSPECTIVE_PFR:
    case EScreenID_VERT_PERSPECTIVE_PRE:
    case EScreenID_VERT_PERSPECTIVE_RL:
    case EScreenID_VERT_PERSPECTIVE_RR:
    case EScreenID_VERT_PERSPECTIVE_FL:
    case EScreenID_VERT_PERSPECTIVE_FR:
    case EScreenID_VERT_PERSPECTIVE_FR_L:
    case EScreenID_VERT_PERSPECTIVE_FR_R:
    case EScreenID_VERT_PERSPECTIVE_FL_L:
    case EScreenID_VERT_PERSPECTIVE_FL_R:
    case EScreenID_VERT_PERSPECTIVE_RR_L:
    case EScreenID_VERT_PERSPECTIVE_RR_R:
    case EScreenID_VERT_PERSPECTIVE_RL_L:
    case EScreenID_VERT_PERSPECTIVE_RL_R:
    case EScreenID_VERT_SINGLE_STB:
    case EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__LEFT_SIDE:
    case EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__RIGHT_SIDE:
    case EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__LEFT_SIDE:
    case EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__RIGHT_SIDE:
    case EScreenID_VERT_PLAN__DUAL_FRONT_WHEEL__FRONT_SIDE:
    case EScreenID_VERT_PLAN__DUAL_FRONT_WHEEL__REAR_SIDE:
    case EScreenID_VERT_PLAN__DUAL_REAR_WHEEL__FRONT_SIDE:
    case EScreenID_VERT_PLAN__DUAL_REAR_WHEEL__REAR_SIDE:
    case EScreenID_VERT_PLAN__DUAL_WHEEL__FRONT_SIDE:
    case EScreenID_VERT_PLAN__DUAL_WHEEL__REAR_SIDE:
    case EScreenID_VERT_PARKING_FRONT:
    case EScreenID_VERT_PARKING_REAR:
    {
        return &cc::core::g_views->m_vertParkingPlanViewport;
    }
    case EScreenID_HORI_PARKING_FRONT: // For HC23 is for Parking view with front view
    case EScreenID_HORI_PARKING_REAR:
    {
        return &cc::core::g_views->m_apaParkingPlanViewport;
    }
    case EScreenID_VERT_FULLSCREEN:
    case EScreenID_VERT_FULLSCREEN_FRONT_ENLARGE:
    case EScreenID_VERT_FULLSCREEN_REAR_ENLARGE:
    {
        return &cc::core::g_views->m_vertFullScreenViewport;
    }
    case EScreenID_FULLSCREEN:
    case EScreenID_FULLSCREEN_FRONT_ENLARGE:
    case EScreenID_FULLSCREEN_REAR_ENLARGE:
    {
        return &cc::core::g_views->m_usableCanvasViewport;
        break;
    }
    case EScreenID_IMAGE_IN_IMAGE_PLANVIEW:
    case EScreenID_IMAGE_IN_IMAGE_PLANVIEW_FRONT_ENLARGE:
    case EScreenID_IMAGE_IN_IMAGE_PLANVIEW_REAR_ENLARGE:
    {
        return &cc::core::g_views->m_imageInimagePlanviewViewport;
    }
    default:
    {
        return &cc::core::g_views->m_planViewport;
    }
    }
}

bool isEnlargeFront(vfc::int32_t f_mode)
{
    switch (f_mode)
    {
    case EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__LEFT_SIDE:
    case EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__RIGHT_SIDE:
    case EScreenID_VERT_FULLSCREEN_FRONT_ENLARGE:
    case EScreenID_MODEL_F_VIEW_ENLARGEMENT:
    case EScreenID_FULLSCREEN_FRONT_ENLARGE:
    case EScreenID_IMAGE_IN_IMAGE_PLANVIEW_FRONT_ENLARGE:
    {
        return true;
    }
    default:
    {
        return false;
    }
    }
}

bool isEnlargeRear(vfc::int32_t f_mode)
{
    switch (f_mode)
    {
    case EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__LEFT_SIDE:
    case EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__RIGHT_SIDE:
    case EScreenID_VERT_FULLSCREEN_REAR_ENLARGE:
    case EScreenID_MODEL_B_VIEW_ENLARGEMENT:
    case EScreenID_FULLSCREEN_REAR_ENLARGE:
    case EScreenID_IMAGE_IN_IMAGE_PLANVIEW_REAR_ENLARGE:
    {
        return true;
    }
    default:
    {
        return false;
    }
    }
}

bool isFullscreen(vfc::int32_t f_mode)
{
    switch (f_mode)
    {
    case EScreenID_VERT_FULLSCREEN:
    case EScreenID_VERT_FULLSCREEN_FRONT_ENLARGE:
    case EScreenID_VERT_FULLSCREEN_REAR_ENLARGE:
    case EScreenID_FULLSCREEN:
    case EScreenID_FULLSCREEN_FRONT_ENLARGE:
    case EScreenID_FULLSCREEN_REAR_ENLARGE:
    {
        return true;
    }
    default:
    {
        return false;
    }
    }
}

bool isEnlarge(vfc::int32_t f_mode)
{
    switch (f_mode)
    {
    case EScreenID_VERT_FULLSCREEN_FRONT_ENLARGE:
    case EScreenID_VERT_FULLSCREEN_REAR_ENLARGE:
    case EScreenID_FULLSCREEN_FRONT_ENLARGE:
    case EScreenID_FULLSCREEN_REAR_ENLARGE:
    {
        return true;
    }
    default:
    {
        return false;
    }
    }
}

bool isFrontView(vfc::int32_t f_mode)
{
    switch (f_mode)
    {
    case EScreenID_SINGLE_FRONT_JUNCTION:
    case EScreenID_SINGLE_FRONT_NORMAL:
    case EScreenID_MODEL_F_VIEW_ENLARGEMENT:
    case EScreenID_HORI_PARKING_FRONT:
    case EScreenID_PARKING_FRONT:
    case EScreenID_UPVIEWCOFIG_WHEEL_REAR_DUAL:
    case EScreenID_UPVIEWCOFIG_WHEEL_FRONT_DUAL:
    case EScreenID_UPVIEWCOFIG_WHEEL_BOTH:
    {
        return true;
    }
    default:
    {
        return false;
    }
    }
}

bool isRearView(vfc::int32_t f_mode)
{
    switch (f_mode)
    {
    case EScreenID_SINGLE_REAR_NORMAL_ON_ROAD:
    case EScreenID_SINGLE_REAR_JUNCTION:
    case EScreenID_MODEL_B_VIEW_ENLARGEMENT:
    case EScreenID_HORI_PARKING_REAR:
    case EScreenID_PARKING_REAR:
    case EScreenID_UPVIEWCOFIG_WHEEL_FRONT_DUAL:
    case EScreenID_UPVIEWCOFIG_WHEEL_REAR_DUAL:
    case EScreenID_UPVIEWCOFIG_WHEEL_BOTH:
    case EScreenID_SINGLE_REAR_NORMAL_OFF_ROAD:
    case EScreenID_SINGLE_REAR_RAW_DIAG:
    case EScreenID_REMOTE_SCREEN_REAR:
    {
        return true;
    }
    default:
    {
        return false;
    }
    }
}

bool isImageInImageView(vfc::int32_t f_view)
{
    switch (f_view)
    {
    case EScreenID_IMAGE_IN_IMAGE_LEFT:
    case EScreenID_IMAGE_IN_IMAGE_RIGHT:
    case EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT:
    case EScreenID_IMAGE_IN_IMAGE_PLANVIEW:
    case EScreenID_IMAGE_IN_IMAGE_PLANVIEW_FRONT_ENLARGE:
    case EScreenID_IMAGE_IN_IMAGE_PLANVIEW_REAR_ENLARGE:
    case EScreenID_PARKING_TOP:
    case EScreenID_PARKING_FRONT:
    case EScreenID_PARKING_REAR:
    case EScreenID_PARKING_FREEPARKING:
    {
        return true;
    }
    default:
    {
        return false;
    }
    }
}

bool isWheelView(vfc::int32_t f_view)
{
    switch (f_view)
    {
    case EScreenID_WHEEL_FRONT_DUAL:
    case EScreenID_PLANVIEW_WITH_SEPARATOR:
    case EScreenID_WHEEL_REAR_DUAL:
    case EScreenID_WHEEL_FRONT_DUAL_REAR:
    case EScreenID_PLANVIEW_WITH_SEPARATOR_REAR:
    case EScreenID_WHEEL_REAR_DUAL_REAR:
    case EScreenID_UPVIEWCOFIG_WHEEL_FRONT_DUAL:
    case EScreenID_WHEEL_FRONT_SINGLE_FRONT_DUAL:
    case EScreenID_WHEEL_FRONT_SINGLE_REAR_DUAL:
    case EScreenID_WHEEL_REAR_SINGLE_FRONT_DUAL:
    case EScreenID_WHEEL_REAR_SINGLE_REAR_DUAL:
    case EScreenID_WHEEL_FRONT_DUAL_ENLARGED:
    case EScreenID_PLANVIEW_WITH_SEPARATOR_ENLARGED:
    case EScreenID_WHEEL_REAR_DUAL_ENLARGED:
    case EScreenID_WHEEL_FRONT_SINGLE_FRONT_DUAL_WITHOUT_PLAN:
    case EScreenID_WHEEL_FRONT_SINGLE_REAR_DUAL_WITHOUT_PLAN:
    case EScreenID_WHEEL_REAR_SINGLE_FRONT_DUAL_WITHOUT_PLAN:
    case EScreenID_WHEEL_REAR_SINGLE_REAR_DUAL_WITHOUT_PLAN:
    {
        return true;
    }
    default:
    {
        return false;
    }
    }
    return false;
}

std::string getScreenName(vfc::int32_t f_screenId)
{
    switch (f_screenId)
    {
    case EScreenID_NO_CHANGE:
    {
        return "EScreenID_NO_CHANGE";
    }
    case EScreenID_NO_VIDEO_USER:
    {
        return "EScreenID_NO_VIDEO_USER";
    }
    case EScreenID_LSMG:
    {
        return "EScreenID_LSMG";
    }
    case EScreenID_CONTEXT_ON_ROAD:
    {
        return "EScreenID_CONTEXT_ON_ROAD";
    }
    case EScreenID_CONTEXT_OFF_ROAD:
    {
        return "EScreenID_CONTEXT_OFF_ROAD";
    }
    case EScreenID_CONTEXT_TOWING:
    {
        return "EScreenID_CONTEXT_TOWING";
    }
    case EScreenID_CONTEXT_JAPANESE:
    {
        return "EScreenID_CONTEXT_JAPANESE";
    }
    case EScreenID_CONTEXT_THREAT:
    {
        return "EScreenID_CONTEXT_THREAT";
    }
    case EScreenID_SINGLE_FRONT_NORMAL:
    {
        return "EScreenID_SINGLE_FRONT_NORMAL";
    }
    case EScreenID_SINGLE_STB:
    {
        return "EScreenID_SINGLE_STB";
    }
    case EScreenID_SINGLE_FRONT_JUNCTION:
    {
        return "EScreenID_SINGLE_FRONT_JUNCTION";
    }
    case EScreenID_SINGLE_REAR_NORMAL_OFF_ROAD:
    {
        return "EScreenID_SINGLE_REAR_NORMAL_OFF_ROAD";
    }
    case EScreenID_PARK_ASSIST_FRONT:
    {
        return "EScreenID_PARK_ASSIST_FRONT";
    }
    case EScreenID_SINGLE_FRONT_RAW:
    {
        return "EScreenID_SINGLE_FRONT_RAW";
    }
    case EScreenID_SINGLE_REAR_NORMAL_ON_ROAD:
    {
        return "EScreenID_SINGLE_REAR_NORMAL_ON_ROAD";
    }
    case EScreenID_SINGLE_REAR_JUNCTION:
    {
        return "EScreenID_SINGLE_REAR_JUNCTION";
    }
    case EScreenID_SINGLE_REAR_RAW_DIAG:
    {
        return "EScreenID_SINGLE_REAR_RAW_DIAG";
    }
    case EScreenID_SINGLE_REAR_HITCH:
    {
        return "EScreenID_SINGLE_REAR_HITCH";
    }
    case EScreenID_SINGLE_REAR_HITCH_ZOOM:
    {
        return "EScreenID_SINGLE_REAR_HITCH_ZOOM";
    }
    case EScreenID_PARK_ASSIST_REAR:
    {
        return "EScreenID_PARK_ASSIST_REAR";
    }
    case EScreenID_SINGLE_REAR_TRAILER:
    {
        return "EScreenID_SINGLE_REAR_TRAILER";
    }
    case EScreenID_PERSPECTIVE_KL:
    {
        return "EScreenID_PERSPECTIVE_KL";
    }
    case EScreenID_PARK_ASSIST_FRONT_JAP:
    {
        return "EScreenID_PARK_ASSIST_FRONT_JAP";
    }
    case EScreenID_PARK_ASSIST_REAR_JAP:
    {
        return "EScreenID_PARK_ASSIST_REAR_JAP";
    }
    case EScreenID_SINGLE_ML_RAW:
    {
        return "EScreenID_SINGLE_ML_RAW";
    }
    case EScreenID_PERSPECTIVE_KR:
    {
        return "EScreenID_PERSPECTIVE_KR";
    }
    case EScreenID_SINGLE_LEFT:
    {
        return "EScreenID_SINGLE_LEFT";
    }
    case EScreenID_SINGLE_RIGHT:
    {
        return "EScreenID_SINGLE_RIGHT";
    }
    case EScreenID_SINGLE_MR_RAW:
    {
        return "EScreenID_SINGLE_MR_RAW";
    }
    case EScreenID_THREAT_FRONT:
    {
        return "EScreenID_THREAT_FRONT";
    }
    case EScreenID_THREAT_REAR:
    {
        return "EScreenID_THREAT_REAR";
    }
    case EScreenID_WHEEL_FRONT_DUAL:
    {
        return "EScreenID_WHEEL_FRONT_DUAL";
    }
    case EScreenID_PERSPECTIVE_FL:
    {
        return "EScreenID_PERSPECTIVE_FL";
    }
    case EScreenID_PERSPECTIVE_FR:
    {
        return "EScreenID_PERSPECTIVE_FR";
    }
    case EScreenID_PERSPECTIVE_RL:
    {
        return "EScreenID_PERSPECTIVE_RL";
    }
    case EScreenID_PERSPECTIVE_RR:
    {
        return "EScreenID_PERSPECTIVE_RR";
    }
    case EScreenID_DUAL_FRONT_ML:
    {
        return "EScreenID_DUAL_FRONT_ML";
    }
    case EScreenID_DUAL_FRONT_MR:
    {
        return "EScreenID_DUAL_FRONT_MR";
    }
    case EScreenID_PERSPECTIVE_PRE:
    {
        return "EScreenID_PERSPECTIVE_PRE";
    }
    case EScreenID_PERSPECTIVE_PLE:
    {
        return "EScreenID_PERSPECTIVE_PLE";
    }
    case EScreenID_DUAL_FRONT_JAP:
    {
        return "EScreenID_DUAL_FRONT_JAP";
    }
    case EScreenID_CAM_CALIB_ENG:
    {
        return "EScreenID_CAM_CALIB_ENG";
    }
    case EScreenID_DUAL_REAR_JAP:
    {
        return "EScreenID_DUAL_REAR_JAP";
    }
    case EScreenID_TOW_ASSIST_ENG:
    {
        return "EScreenID_TOW_ASSIST_ENG";
    }
    case EScreenID_LSM_ENG:
    {
        return "EScreenID_LSM_ENG";
    }
    case EScreenID_WHEEL_REAR_DUAL:
    {
        return "EScreenID_WHEEL_REAR_DUAL";
    }
    case EScreenID_LSMG_LSAEB_ENG:
    {
        return "EScreenID_LSMG_LSAEB_ENG";
    }
    case EScreenID_FULL_SCREEN:
    {
        return "EScreenID_FULL_SCREEN";
    }
    case EScreenID_TRIPLE_ML_FV_MR:
    {
        return "EScreenID_TRIPLE_ML_FV_MR";
    }
    case EScreenID_TRIPLE_REAR_ML_MR_TOW_ASSIST:
    {
        return "EScreenID_TRIPLE_REAR_ML_MR_TOW_ASSIST";
    }
    case EScreenID_QUAD_RAW:
    {
        return "EScreenID_QUAD_RAW";
    }
    case EScreenID_NO_VIDEO_SYSTEM:
    {
        return "EScreenID_NO_VIDEO_SYSTEM";
    }
    case EScreenID_PERSPECTIVE_PFR:
    {
        return "EScreenID_PERSPECTIVE_PFR";
    }
    case EScreenID_FIND_CAR_VIEW:
    {
        return "EScreenID_FIND_CAR_VIEW";
    }
    case EScreenID_PERSPECTIVE_PRI:
    {
        return "EScreenID_PERSPECTIVE_PRI";
    }
    case EScreenID_BACK:
    {
        return "EScreenID_BACK";
    }
    case EScreenID_PRK_MODE_SELECT:
    {
        return "EScreenID_PRK_MODE_SELECT";
    }
    case EScreenID_PRK_SEARCHING:
    {
        return "EScreenID_PRK_SEARCHING";
    }
    case EScreenID_PRK_CONFIRMING:
    {
        return "EScreenID_PRK_CONFIRMING";
    }
    case EScreenID_VERT_SINGLE_FRONT:
    {
        return "EScreenID_VERT_SINGLE_FRONT";
    }
    case EScreenID_VERT_SINGLE_REAR:
    {
        return "EScreenID_VERT_SINGLE_REAR";
    }
    case EScreenID_VERT_SINGLE_FRONT_JUNCTION:
    {
        return "EScreenID_VERT_SINGLE_FRONT_JUNCTION";
    }
    case EScreenID_VERT_SINGLE_REAR_JUNCTION:
    {
        return "EScreenID_VERT_SINGLE_REAR_JUNCTION";
    }
    case EScreenID_VERT_SINGLE_LEFT:
    {
        return "EScreenID_VERT_SINGLE_LEFT";
    }
    case EScreenID_VERT_SINGLE_RIGHT:
    {
        return "EScreenID_VERT_SINGLE_RIGHT";
    }
    case EScreenID_VERT_WHEEL_FRONT_DUAL:
    {
        return "EScreenID_VERT_WHEEL_FRONT_DUAL";
    }
    case EScreenID_VERT_WHEEL_REAR_DUAL:
    {
        return "EScreenID_VERT_WHEEL_REAR_DUAL";
    }
    case EScreenID_VERT_PLANVIEW_WITH_SEPARATOR:
    {
        return "EScreenID_VERT_PLANVIEW_WITH_SEPARATOR";
    }
    case EScreenID_VERT_PERSPECTIVE_PFR:
    {
        return "EScreenID_VERT_PERSPECTIVE_PFR";
    }
    case EScreenID_VERT_PERSPECTIVE_PRE:
    {
        return "EScreenID_VERT_PERSPECTIVE_PRE";
    }
    case EScreenID_VERT_PERSPECTIVE_RL:
    {
        return "EScreenID_VERT_PERSPECTIVE_RL";
    }
    case EScreenID_VERT_PERSPECTIVE_RR:
    {
        return "EScreenID_VERT_PERSPECTIVE_RR";
    }
    case EScreenID_VERT_PERSPECTIVE_FL:
    {
        return "EScreenID_VERT_PERSPECTIVE_FL";
    }
    case EScreenID_VERT_PERSPECTIVE_FR:
    {
        return "EScreenID_VERT_PERSPECTIVE_FR";
    }
    case EScreenID_VERT_SINGLE_STB:
    {
        return "EScreenID_VERT_SINGLE_STB";
    }
    case EScreenID_HORI_PARKING_FRONT:
    {
        return "EScreenID_HORI_PARKING_FRONT";
    }
    case EScreenID_HORI_PARKING_REAR:
    {
        return "EScreenID_HORI_PARKING_REAR";
    }
    case EScreenID_VERT_PARKING_FRONT:
    {
        return "EScreenID_VERT_PARKING_FRONT";
    }
    case EScreenID_VERT_PARKING_REAR:
    {
        return "EScreenID_VERT_PARKING_REAR";
    }
    case EScreenID_PLANVIEW_WITH_SEPARATOR:
    {
        return "EScreenID_PLANVIEW_WITH_SEPARATOR";
    }
    case EScreenID_REMOTE_SCREEN_FRONT:
    {
        return "EScreenID_REMOTE_SCREEN_FRONT";
    }
    case EScreenID_REMOTE_SCREEN_REAR:
    {
        return "EScreenID_REMOTE_SCREEN_REAR";
    }
    case EScreenID_REMOTE_SCREEN_LEFT:
    {
        return "EScreenID_REMOTE_SCREEN_LEFT";
    }
    case EScreenID_REMOTE_SCREEN_RIGHT:
    {
        return "EScreenID_REMOTE_SCREEN_RIGHT";
    }
    case EScreenID_LEFTRIGHTVIEW_FRONT_VIEW:
    {
        return "EScreenID_LEFTRIGHTVIEW_FRONT_VIEW";
    }
    case EScreenID_DEBUG:
    {
        return "EScreenID_DEBUG";
    }
    case EScreenID_LEFTRIGHTVIEW_REAR_VIEW:
    {
        return "EScreenID_LEFTRIGHTVIEW_REAR_VIEW";
    }
    case EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__LEFT_SIDE:
    {
        return "EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__LEFT_SIDE";
    }
    case EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__RIGHT_SIDE:
    {
        return "EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__RIGHT_SIDE";
    }
    case EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__LEFT_SIDE:
    {
        return "EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__LEFT_SIDE";
    }
    case EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__RIGHT_SIDE:
    {
        return "EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__RIGHT_SIDE";
    }
    case EScreenID_VERT_FULLSCREEN:
    {
        return "EScreenID_VERT_FULLSCREEN";
    }
    case EScreenID_VERT_FULLSCREEN_FRONT_ENLARGE:
    {
        return "EScreenID_VERT_FULLSCREEN_FRONT_ENLARGE";
    }
    case EScreenID_VERT_FULLSCREEN_REAR_ENLARGE:
    {
        return "EScreenID_VERT_FULLSCREEN_REAR_ENLARGE";
    }
    case EScreenID_VERT_PLAN__DUAL_FRONT_WHEEL__FRONT_SIDE:
    {
        return "EScreenID_VERT_PLAN__DUAL_FRONT_WHEEL__FRONT_SIDE";
    }
    case EScreenID_VERT_PLAN__DUAL_FRONT_WHEEL__REAR_SIDE:
    {
        return "EScreenID_VERT_PLAN__DUAL_FRONT_WHEEL__REAR_SIDE";
    }
    case EScreenID_VERT_PLAN__DUAL_REAR_WHEEL__FRONT_SIDE:
    {
        return "EScreenID_VERT_PLAN__DUAL_REAR_WHEEL__FRONT_SIDE";
    }
    case EScreenID_VERT_PLAN__DUAL_REAR_WHEEL__REAR_SIDE:
    {
        return "EScreenID_VERT_PLAN__DUAL_REAR_WHEEL__REAR_SIDE";
    }
    case EScreenID_VERT_PLAN__DUAL_WHEEL__FRONT_SIDE:
    {
        return "EScreenID_VERT_PLAN__DUAL_WHEEL__FRONT_SIDE";
    }
    case EScreenID_VERT_PLAN__DUAL_WHEEL__REAR_SIDE:
    {
        return "EScreenID_VERT_PLAN__DUAL_WHEEL__REAR_SIDE";
    }
    case EScreenID_VERT_PERSPECTIVE_FL_R:
    {
        return "EScreenID_VERT_PERSPECTIVE_FL_R";
    }
    case EScreenID_VERT_PERSPECTIVE_FL_L:
    {
        return "EScreenID_VERT_PERSPECTIVE_FL_L";
    }
    case EScreenID_VERT_PERSPECTIVE_FR_R:
    {
        return "EScreenID_VERT_PERSPECTIVE_FR_R";
    }
    case EScreenID_VERT_PERSPECTIVE_FR_L:
    {
        return "EScreenID_VERT_PERSPECTIVE_FR_L";
    }
    case EScreenID_VERT_PERSPECTIVE_RL_R:
    {
        return "EScreenID_VERT_PERSPECTIVE_RL_R";
    }
    case EScreenID_VERT_PERSPECTIVE_RL_L:
    {
        return "EScreenID_VERT_PERSPECTIVE_RL_L";
    }
    case EScreenID_VERT_PERSPECTIVE_RR_R:
    {
        return "EScreenID_VERT_PERSPECTIVE_RR_R";
    }
    case EScreenID_VERT_PERSPECTIVE_RR_L:
    {
        return "EScreenID_VERT_PERSPECTIVE_RR_L";
    }
    case EScreenID_VERT_2D_FRONTVIEW_RIGHTVIEW:
    {
        return "EScreenID_VERT_2D_FRONTVIEW_RIGHTVIEW";
    }
    case EScreenID_VERT_2D_FRONTVIEW_LEFTVIEW:
    {
        return "EScreenID_VERT_2D_FRONTVIEW_LEFTVIEW";
    }
    case EScreenID_VERT_2D_REARVIEW_RIGHTVIEW:
    {
        return "EScreenID_VERT_2D_REARVIEW_RIGHTVIEW";
    }
    case EScreenID_VERT_2D_REARVIEW_LEFTVIEW:
    {
        return "EScreenID_VERT_2D_REARVIEW_LEFTVIEW";
    }
    case EScreenID_MODEL_F_VIEW_ENLARGEMENT:
    {
        return "EScreenID_MODEL_F_VIEW_ENLARGEMENT";
    }
    case EScreenID_MODEL_B_VIEW_ENLARGEMENT:
    {
        return "EScreenID_MODEL_B_VIEW_ENLARGEMENT";
    }
    case EScreenID_FULLSCREEN:
    {
        return "EScreenID_FULLSCREEN";
    }
    case EScreenID_FULLSCREEN_FRONT_ENLARGE:
    {
        return "EScreenID_FULLSCREEN_FRONT_ENLARGE";
    }
    case EScreenID_FULLSCREEN_REAR_ENLARGE:
    {
        return "EScreenID_FULLSCREEN_REAR_ENLARGE";
    }
    case EScreenID_FRONT_BUMPER:
    {
        return "EScreenID_FRONT_BUMPER";
    }
    case EScreenID_REAR_BUMPER:
    {
        return "EScreenID_REAR_BUMPER";
    }
    case EScreenID_ULTRA_WIDE_SURROUND_VIEW:
    {
        return "EScreenID_ULTRA_WIDE_SURROUND_VIEW";
    }
    case EScreenID_PLANETARY_VIEW:
    {
        return "EScreenID_PLANETARY_VIEW";
    }
    case EScreenID_SUPER_TRANSPARENT_VIEW:
    {
        return "EScreenID_SUPER_TRANSPARENT_VIEW";
    }
    case EScreenID_SINGLE_REAR_NORMAL_ON_ROAD_SGHL_L:
    {
        return "EScreenID_SINGLE_REAR_NORMAL_ON_ROAD_SGHL_L";
    }
    case EScreenID_MODEL_B_VIEW_ENLARGEMENT_SGHL_L:
    {
        return "EScreenID_MODEL_B_VIEW_ENLARGEMENT_SGHL_L";
    }
    case EScreenID_IMAGE_IN_IMAGE_LEFT:
    {
        return "EScreenID_IMAGE_IN_IMAGE_LEFT";
    }
    case EScreenID_IMAGE_IN_IMAGE_RIGHT:
    {
        return "EScreenID_IMAGE_IN_IMAGE_RIGHT";
    }
    case EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT:
    {
        return "EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT";
    }
    case EScreenID_IMAGE_IN_IMAGE_PLANVIEW:
    {
        return "EScreenID_IMAGE_IN_IMAGE_PLANVIEW";
    }
    case EScreenID_AVM3D_VIEW_FULL_SCREEN_2D_VOT:
    {
        return "EScreenID_AVM3D_VIEW_FULL_SCREEN_2D_VOT";
    }
    case EscreenID_FULL_SCREEN_3D:
    {
        return "EscreenID_FULL_SCREEN_3D";
    }
    case EScreenID_IMAGE_IN_IMAGE_PLANVIEW_FRONT_ENLARGE:
    {
        return "EScreenID_IMAGE_IN_IMAGE_PLANVIEW_FRONT_ENLARGE";
    }
    case EScreenID_IMAGE_IN_IMAGE_PLANVIEW_REAR_ENLARGE:
    {
        return "EScreenID_IMAGE_IN_IMAGE_PLANVIEW_REAR_ENLARGE";
    }
    case EScreenID_UPVIEWCOFIG_WHEEL_REAR_DUAL:
    {
        return "EScreenID_UPVIEWCOFIG_WHEEL_REAR_DUAL";
    }
    case EScreenID_UPVIEWCOFIG_WHEEL_FRONT_DUAL:
    {
        return "EScreenID_UPVIEWCOFIG_WHEEL_FRONT_DUAL";
    }
    case EScreenID_UPVIEWCOFIG_WHEEL_BOTH:
    {
        return "EScreenID_UPVIEWCOFIG_WHEEL_BOTH";
    }
    case EScreenID_PARKING_FREEPARKING:
    {
        return "EScreenID_PARKING_FREEPARKING";
    }
    case EScreenID_PARKING_TOP:
    {
        return "EScreenID_PARKING_TOP";
    }
    case EScreenID_PARKING_FRONT:
    {
        return "EScreenID_PARKING_FRONT";
    }
    case EScreenID_PARKING_REAR:
    {
        return "EScreenID_PARKING_REAR";
    }
    case EScreenID_SINGLE_FRONT_NORMAL_LEFT:
    {
        return "EScreenID_SINGLE_FRONT_NORMAL_LEFT";
    }
    case EScreenID_SINGLE_REAR_NORMAL_ON_ROAD_LEFT:
    {
        return "EScreenID_SINGLE_REAR_NORMAL_ON_ROAD_LEFT";
    }
    case EScreenID_PERSPECTIVE_RL_LEFT:
    {
        return "EScreenID_PERSPECTIVE_RL_LEFT";
    }
    case EScreenID_PERSPECTIVE_RR_LEFT:
    {
        return "EScreenID_PERSPECTIVE_RR_LEFT";
    }
    case EScreenID_PERSPECTIVE_PRE_LEFT:
    {
        return "EScreenID_PERSPECTIVE_PRE_LEFT";
    }
    case EScreenID_PERSPECTIVE_PLE_LEFT:
    {
        return "EScreenID_PERSPECTIVE_PLE_LEFT";
    }
    case EScreenID_PERSPECTIVE_PRI_LEFT:
    {
        return "EScreenID_PERSPECTIVE_PRI_LEFT";
    }
    case EScreenID_PERSPECTIVE_FL_LEFT:
    {
        return "EScreenID_PERSPECTIVE_FL_LEFT";
    }
    case EScreenID_PERSPECTIVE_FR_LEFT:
    {
        return "EScreenID_PERSPECTIVE_FR_LEFT";
    }
    case EScreenID_PERSPECTIVE_PFR_LEFT:
    {
        return "EScreenID_PERSPECTIVE_PFR_LEFT";
    }
    case EScreenID_WHEEL_FRONT_DUAL_REAR:
    {
        return "EScreenID_WHEEL_FRONT_DUAL_REAR";
    }
    case EScreenID_PLANVIEW_WITH_SEPARATOR_REAR:
    {
        return "EScreenID_PLANVIEW_WITH_SEPARATOR_REAR";
    }
    case EScreenID_WHEEL_REAR_DUAL_REAR:
    {
        return "EScreenID_WHEEL_REAR_DUAL_REAR";
    }
    case EScreenID_UPVIEWCOFIG_WHEEL_FRONT_DUAL_REAR:
    {
        return "EScreenID_UPVIEWCOFIG_WHEEL_FRONT_DUAL_REAR";
    }
    case EScreenID_UPVIEWCOFIG_WHEEL_BOTH_REAR:
    {
        return "EScreenID_UPVIEWCOFIG_WHEEL_BOTH_REAR";
    }
    case EScreenID_UPVIEWCOFIG_WHEEL_REAR_DUAL_REAR:
    {
        return "EScreenID_UPVIEWCOFIG_WHEEL_REAR_DUAL_REAR";
    }
    case EScreenID_FRONT_BUMPER_RIGHT:
    {
        return "EScreenID_FRONT_BUMPER_RIGHT";
    }
    case EScreenID_REAR_BUMPER_RIGHT:
    {
        return "EScreenID_REAR_BUMPER_RIGHT";
    }
    case EScreenID_NORMAL3D_KEEP:
    {
        return "EScreenID_NORMAL3D_KEEP";
    }
    default:
    {
        return "Invalid f_screenId";
    }
    }
    return "Invalid";
}

bool isEnlargeFrontRearView(vfc::int32_t f_view)
{
    switch (f_view)
    {
    // case EScreenID_IMAGE_IN_IMAGE_LEFT:
    // case EScreenID_IMAGE_IN_IMAGE_RIGHT:
    // case EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT:
    // case EScreenID_IMAGE_IN_IMAGE_PLANVIEW:
    case EScreenID_IMAGE_IN_IMAGE_PLANVIEW_FRONT_ENLARGE:
    case EScreenID_IMAGE_IN_IMAGE_PLANVIEW_REAR_ENLARGE:
    {
        return true;
    }
    // case EScreenID_FULL_SCREEN:
    // case EScreenID_FULLSCREEN:
    case EScreenID_FULLSCREEN_FRONT_ENLARGE:
    case EScreenID_FULLSCREEN_REAR_ENLARGE:
    {
        return true;
    }
    default:
    {
        return false;
    }
    }
}

PlanViewType checkPlanViewType(vfc::int32_t f_view)
{
    PlanViewType l_type = PlanViewType::NORMAL;
    switch (f_view)
    {
    case EScreenID_IMAGE_IN_IMAGE_LEFT:
    case EScreenID_IMAGE_IN_IMAGE_RIGHT:
    case EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT:
    case EScreenID_IMAGE_IN_IMAGE_PLANVIEW:
    case EScreenID_IMAGE_IN_IMAGE_PLANVIEW_FRONT_ENLARGE:
    case EScreenID_IMAGE_IN_IMAGE_PLANVIEW_REAR_ENLARGE:
    {
        l_type = PlanViewType::PICTURE_IN_PICTURE;
        break;
    }
    case EScreenID_FULL_SCREEN:
    case EScreenID_FULLSCREEN:
    case EScreenID_FULLSCREEN_FRONT_ENLARGE:
    case EScreenID_FULLSCREEN_REAR_ENLARGE:
    {
        l_type = PlanViewType::FULLSCREEN;
        break;
    }
    default:
    {
        l_type = PlanViewType::NORMAL;
        break;
    }
    }
    return l_type;
}

} // namespace core
} // namespace cc
