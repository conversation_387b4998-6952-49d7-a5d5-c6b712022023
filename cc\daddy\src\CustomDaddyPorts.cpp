//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: EIK2LR Karim Eid (CC-DA/EAV1)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomDaddyPorts.cpp
/// @brief
//=============================================================================

#include "cc/daddy/inc/CustomDaddyPorts.h"

namespace cc
{
namespace daddy
{
using ::daddy::TSenderPort; using ::daddy::TMemPool;

//! INPUT ports --------
//! Socket command for set default status
TSenderPort< SocketCmdDaddy_t > CustomDaddyPorts::sm_SocketCmdDaddy_SenderPort;
TMemPool< SocketCmdDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SocketCmdDaddy_MemPool;

//! Parking Spots
TSenderPort<ParkingSpotsListDaddy> CustomDaddyPorts::sm_parkingInSpots_SenderPort;
TMemPool<ParkingSpotsListDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> CustomDaddyPorts::sm_parkingInSpots_MemPool;

TSenderPort<ParkingSpotDaddy> CustomDaddyPorts::sm_parkingOutSpot_SenderPort;
TMemPool<ParkingSpotDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> CustomDaddyPorts::sm_parkingOutSpot_MemPool;

//! PMA travel distance desired
TSenderPort<APG_VehiclePathDaddy> CustomDaddyPorts::sm_PMA_TravelDistDesired_SenderPort;
TMemPool<APG_VehiclePathDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> CustomDaddyPorts::sm_PMA_TravelDistDesired_MemPool;

//! PMA control manager data
TSenderPort<PmaCtlMrgrDaddy> CustomDaddyPorts::sm_pmaCtlMrgr_SenderPort;
TMemPool<PmaCtlMrgrDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> CustomDaddyPorts::sm_pmaCtlMrgr_MemPool;

//! PP & SDW status
TSenderPort< PasWarnToneDaddy_t > CustomDaddyPorts::sm_PasWarnToneDaddy_SenderPort;
TMemPool< PasWarnToneDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PasWarnToneDaddy_MemPool;

TSenderPort< PasButtonStatusDaddy_t > CustomDaddyPorts::sm_PasButtonStatusDaddy_SenderPort;
TMemPool< PasButtonStatusDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PasButtonStatusDaddy_MemPool;

TSenderPort< PasStatusDaddy_t > CustomDaddyPorts::sm_PasStatusDaddy_SenderPort;
TMemPool< PasStatusDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PasStatusDaddy_MemPool;

TSenderPort< SdwStatusFDaddy_t > CustomDaddyPorts::sm_SdwFStatusDaddy_SenderPort;
TMemPool< SdwStatusFDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SdwFStatusDaddy_MemPool;

TSenderPort< SdwStatusDaddy_t > CustomDaddyPorts::sm_SdwStatusDaddy_SenderPort;
TMemPool< SdwStatusDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SdwStatusDaddy_MemPool;

TSenderPort< SdwStatusFMDaddy_t > CustomDaddyPorts::sm_SdwFMStatusDaddy_SenderPort;
TMemPool< SdwStatusFMDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SdwFMStatusDaddy_MemPool;

TSenderPort< SdwStatusRMDaddy_t > CustomDaddyPorts::sm_SdwRMStatusDaddy_SenderPort;
TMemPool< SdwStatusRMDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SdwRMStatusDaddy_MemPool;

TSenderPort< SdwStatusRDaddy_t > CustomDaddyPorts::sm_SdwRStatusDaddy_SenderPort;
TMemPool< SdwStatusRDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SdwRStatusDaddy_MemPool;

//! Park status input from parkhmi
TSenderPort< ParkStatusDaddy_t > CustomDaddyPorts::sm_ParkStatusDaddy_SenderPort;
TMemPool< ParkStatusDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkStatusDaddy_MemPool;

TSenderPort< ParkTypeDaddy_t > CustomDaddyPorts::sm_ParkTypeDaddy_SenderPort;
TMemPool< ParkTypeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkTypeDaddy_MemPool;

TSenderPort< ParkTypeVariantDaddy_t > CustomDaddyPorts::sm_ParkTypeVariantDaddy_SenderPort;
TMemPool< ParkTypeVariantDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkTypeVariantDaddy_MemPool;

TSenderPort< ParkModeDaddy_t > CustomDaddyPorts::sm_ParkModeDaddy_SenderPort;
TMemPool< ParkModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkModeDaddy_MemPool;

TSenderPort< ParkSideDaddy_t > CustomDaddyPorts::sm_ParkSideDaddy_SenderPort;
TMemPool< ParkSideDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkSideDaddy_MemPool;

TSenderPort< ParkFunctionIndDaddy_t > CustomDaddyPorts::sm_ParkFunctionIndDaddy_SenderPort;
TMemPool< ParkFunctionIndDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkFunctionIndDaddy_MemPool;

TSenderPort< ParkQuitIndDaddy_t > CustomDaddyPorts::sm_ParkQuitIndDaddy_SenderPort;
TMemPool< ParkQuitIndDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkQuitIndDaddy_MemPool;

TSenderPort< ParkQuitIndExtDaddy_t > CustomDaddyPorts::sm_ParkQuitIndExtDaddy_SenderPort;
TMemPool< ParkQuitIndExtDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkQuitIndExtDaddy_MemPool;

TSenderPort< ParkRecoverIndDaddy_t > CustomDaddyPorts::sm_ParkRecoverIndDaddy_SenderPort;
TMemPool< ParkRecoverIndDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkRecoverIndDaddy_MemPool;

TSenderPort< ParkDriverIndDaddy_t > CustomDaddyPorts::sm_ParkDriverIndDaddy_SenderPort;
TMemPool< ParkDriverIndDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkDriverIndDaddy_MemPool;

TSenderPort< ParkDriverIndExtDaddy_t > CustomDaddyPorts::sm_ParkDriverIndExtDaddy_SenderPort;
TMemPool< ParkDriverIndExtDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkDriverIndExtDaddy_MemPool;

TSenderPort< ParkOutSideAvlDaddy_t > CustomDaddyPorts::sm_ParkOutSideAvlDaddy_SenderPort;
TMemPool< ParkOutSideAvlDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkOutSideAvlDaddy_MemPool;

TSenderPort< ParkReqReleaseBtnDaddy_t > CustomDaddyPorts::sm_ParkReqReleaseBtnDaddy_SenderPort;
TMemPool< ParkReqReleaseBtnDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkReqReleaseBtnDaddy_MemPool;

TSenderPort< ParkAPA_ParkSpace_t > CustomDaddyPorts::sm_ParkAPA_ParkSpaceDaddy_SenderPort;
TMemPool< ParkAPA_ParkSpace_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkAPA_ParkSpaceDaddy_MemPool;

TSenderPort< ParkAPA_ParkSpaceMark_t > CustomDaddyPorts::sm_ParkAPA_ParkSpaceMarkDaddy_SenderPort;
TMemPool< ParkAPA_ParkSpaceMark_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkAPA_ParkSpaceMarkDaddy_MemPool;

TSenderPort< ParkPSDirectionSelected_t > CustomDaddyPorts::sm_ParkPSDirectionSelectedDaddy_SenderPort;
TMemPool< ParkPSDirectionSelected_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkPSDirectionSelectedDaddy_MemPool;

TSenderPort< ParkParkngTypeSeld_t > CustomDaddyPorts::sm_ParkParkngTypeSeldDaddy_SenderPort;
TMemPool< ParkParkngTypeSeld_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkParkngTypeSeldDaddy_MemPool;

TSenderPort< ParkAPAPARKMODE_t > CustomDaddyPorts::sm_ParkAPAPARKMODE_SenderPort;
TMemPool< ParkAPAPARKMODE_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkAPAPARKMODE_MemPool;

TSenderPort< ParkSpaceDaddy_t > CustomDaddyPorts::sm_ParkSpaceDaddy_SenderPort;
TMemPool< ParkSpaceDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkSpaceDaddy_MemPool;

TSenderPort< ParkFrontObjDaddy_t > CustomDaddyPorts::sm_ParkFrontObjDaddy_SenderPort;
TMemPool< ParkFrontObjDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkFrontObjDaddy_MemPool;

//! Park end position during searching
TSenderPort< ParkEndPositionSearchingDaddy_t > CustomDaddyPorts::sm_ParkEndPositionSearchingDaddy_SenderPort;
TMemPool< ParkEndPositionSearchingDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkEndPositionSearchingDaddy_MemPool;

//! Park final end position during guidance
TSenderPort< ParkFinalEndPositionDaddy_t > CustomDaddyPorts::sm_ParkFinalEndPositionDaddy_SenderPort;
TMemPool< ParkFinalEndPositionDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkFinalEndPositionDaddy_MemPool;

//! Park target position of current move during guidance
TSenderPort< ParkCurMoveTargetPositionDaddy_t > CustomDaddyPorts::sm_ParkCurMoveTargetPositionDaddy_SenderPort;
TMemPool< ParkCurMoveTargetPositionDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkCurMoveTargetPositionDaddy_MemPool;

// ! Dynamic Gear Overlay
TSenderPort< ParkCarmoveNumberDaddy_t > CustomDaddyPorts::sm_ParkCarmoveNumberDaddy_SenderPort;
TMemPool< ParkCarmoveNumberDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkCarmoveNumberDaddy_MemPool;

TSenderPort< ParkCartravelDistDesiredDaddy_t > CustomDaddyPorts::sm_ParkCartravelDistDesiredDaddy_SenderPort;
TMemPool< ParkCartravelDistDesiredDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkCartravelDistDesiredDaddy_MemPool;

//! Park whether it is last move
TSenderPort< ParkIsLastMoveDaddy_t > CustomDaddyPorts::sm_ParkIsLastMoveDaddy_SenderPort;
TMemPool< ParkIsLastMoveDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkIsLastMoveDaddy_MemPool;

TSenderPort< ParkRPADriverSelectedDaddy_t > CustomDaddyPorts::sm_ParkRPADriverSelectedDaddy_SenderPort;
TMemPool< ParkRPADriverSelectedDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkRPADriverSelectedDaddy_MemPool;

TSenderPort< ParkBreakPedalBeenReleasedBf_t > CustomDaddyPorts::sm_brakePedalBeenReleasedBeforeDaddy_SenderPort;
TMemPool< ParkBreakPedalBeenReleasedBf_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_brakePedalBeenReleasedBeforeDaddy_MemPool;

TSenderPort< ParkbrkPedlAppld_t > CustomDaddyPorts::sm_brakePedalAppliedDaddy_SenderPort;
TMemPool< ParkbrkPedlAppld_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_brakePedalAppliedDaddy_MemPool;

TSenderPort< ParkDisp2TouchStsDaddy_t > CustomDaddyPorts::sm_ParkDisp2TouchStsDaddy_SenderPort;
TMemPool< ParkDisp2TouchStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkDisp2TouchStsDaddy_MemPool;

TSenderPort< ParkDisp2TouchSlotSelectionDaddy_t > CustomDaddyPorts::sm_ParkDisp2TouchSlotSelectionDaddy_SenderPort;
TMemPool< ParkDisp2TouchSlotSelectionDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkDisp2TouchSlotSelectionDaddy_MemPool;

TSenderPort< ParkDispLowPolyModelStsDaddy_t > CustomDaddyPorts::sm_ParkDispLowPolyModelStsDaddy_SenderPort;
TMemPool< ParkDispLowPolyModelStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkDispLowPolyModelStsDaddy_MemPool;

//! Free parking
TSenderPort<cc::daddy::FreeparkingData_Daddy> CustomDaddyPorts::sm_freeparkingDataSenderPort;
TMemPool<cc::daddy::FreeparkingData_Daddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> CustomDaddyPorts::sm_freeparkingDataMemPool;

TSenderPort<cc::daddy::FreeparkingRectInfo_Daddy> CustomDaddyPorts::sm_freeparkingRectInfoSenderPort;
TMemPool<cc::daddy::FreeparkingRectInfo_Daddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> CustomDaddyPorts::sm_freeparkingRectInfoMemPool;

TSenderPort<cc::daddy::FreeparkingSocket_Daddy> CustomDaddyPorts::sm_freeparkingSocketSenderPort;
TMemPool<cc::daddy::FreeparkingSocket_Daddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> CustomDaddyPorts::sm_freeparkingSocketMemPool;

TSenderPort< ParkFreeParkingActive_t > CustomDaddyPorts::sm_ParkFreeParkingActiveDaddy_SenderPort;
TMemPool< ParkFreeParkingActive_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkFreeParkingActiveDaddy_MemPool;

TSenderPort< FreeParkingButtonPress_t > CustomDaddyPorts::sm_FreeParkingButtonPressDaddy_SenderPort;
TMemPool< FreeParkingButtonPress_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_FreeParkingButtonPressDaddy_MemPool;

TSenderPort< ButtonPress_t > CustomDaddyPorts::sm_StartPauseConfirmButtonPressDaddy_SenderPort;
TMemPool< ButtonPress_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_StartPauseConfirmButtonPressDaddy_MemPool;

TSenderPort< ButtonPress_t > CustomDaddyPorts::sm_ContinueButtonPressDaddy_SenderPort;
TMemPool< ButtonPress_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ContinueButtonPressDaddy_MemPool;

TSenderPort< ButtonPress_t > CustomDaddyPorts::sm_QuitButtonPressDaddy_SenderPort;
TMemPool< ButtonPress_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_QuitButtonPressDaddy_MemPool;

TSenderPort< ButtonPress_t > CustomDaddyPorts::sm_SuspendButtonPressDaddy_SenderPort;
TMemPool< ButtonPress_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SuspendButtonPressDaddy_MemPool;

TSenderPort< ButtonPress_t > CustomDaddyPorts::sm_parkoutLeftButtonPressDaddy_SenderPort;
TMemPool< ButtonPress_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_parkoutLeftButtonPressDaddy_MemPool;

TSenderPort< ButtonPress_t > CustomDaddyPorts::sm_parkoutRightButtonPressDaddy_SenderPort;
TMemPool< ButtonPress_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_parkoutRightButtonPressDaddy_MemPool;

TSenderPort< FreeParkingConfirmButtonStDaddy_t > CustomDaddyPorts::sm_FreeParkingConfirmButtonStDaddy_SenderPort;
TMemPool< FreeParkingConfirmButtonStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_FreeParkingConfirmButtonStDaddy_MemPool;

TSenderPort< FreeParkingReturnButtonStDaddy_t > CustomDaddyPorts::sm_FreeParkingReturnButtonStDaddy_SenderPort;
TMemPool< FreeParkingReturnButtonStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_FreeParkingReturnButtonStDaddy_MemPool;

TSenderPort< FreeParkingSpaceTypeButtonStDaddy_t > CustomDaddyPorts::sm_FreeParkingSpaceTypeButtonStDaddy_SenderPort;
TMemPool< FreeParkingSpaceTypeButtonStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_FreeParkingSpaceTypeButtonStDaddy_MemPool;

//! Park status output to parkhmi
TSenderPort< PasButtonPressedStDaddy_t > CustomDaddyPorts::sm_PasButtonPressedStDaddy_SenderPort;
TMemPool< PasButtonPressedStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PasButtonPressedStDaddy_MemPool;

TSenderPort< SettingAutoCamStDaddy_t > CustomDaddyPorts::sm_AutoCamActivButtonPressedStDaddy_SenderPort;
TMemPool< SettingAutoCamStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_AutoCamActivButtonPressedStDaddy_MemPool;

TSenderPort< ParkConfirmButtonStDaddy_t > CustomDaddyPorts::sm_ParkConfirmButtonStDaddy_SenderPort;
TMemPool< ParkConfirmButtonStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkConfirmButtonStDaddy_MemPool;

TSenderPort< ParkOutSideButtonStDaddy_t > CustomDaddyPorts::sm_ParkOutSideButtonStDaddy_SenderPort;
TMemPool< ParkOutSideButtonStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkOutSideButtonStDaddy_MemPool;

TSenderPort< ParkModeSelectedStDaddy_t > CustomDaddyPorts::sm_ParkModeSelectedStDaddy_SenderPort;
TMemPool< ParkModeSelectedStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkModeSelectedStDaddy_MemPool;

TSenderPort< ParkTypeSelectedStDaddy_t > CustomDaddyPorts::sm_ParkTypeSelectedStDaddy_SenderPort;
TMemPool< ParkTypeSelectedStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkTypeSelectedStDaddy_MemPool;

TSenderPort< ParkSlotSelectedStDaddy_t > CustomDaddyPorts::sm_ParkSlotSelectedStDaddy_SenderPort;
TMemPool< ParkSlotSelectedStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkSlotSelectedStDaddy_MemPool;

TSenderPort< ParkDrvFuncOffStDaddy_t > CustomDaddyPorts::sm_ParkDrvFuncOffStDaddy_SenderPort;
TMemPool< ParkDrvFuncOffStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkDrvFuncOffStDaddy_MemPool;

TSenderPort< ParkPauseButton_t > CustomDaddyPorts::sm_ParkPauseButtonStDaddy_SenderPort;
TMemPool< ParkPauseButton_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkPauseButtonStDaddy_MemPool;

TSenderPort< PARkDirection_t > CustomDaddyPorts::sm_ParkDirectionStDaddy_SenderPort;
TMemPool< PARkDirection_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkDirectionStDaddy_MemPool;

TSenderPort< ParkDriverIndSearchDaddy_t > CustomDaddyPorts::sm_ParkDriverIndSearchDaddy_SenderPort;
TMemPool< ParkDriverIndSearchDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkDriverIndSearchDaddy_MemPool;

TSenderPort< ParkRPAAvaliableDaddy_t > CustomDaddyPorts::sm_ParkRPAAvaliableDaddy_SenderPort;
TMemPool< ParkRPAAvaliableDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkRPAAvaliableDaddy_MemPool;

//! State machine switch of obstacle
TSenderPort< SMObstacleSwitchDaddy_t > CustomDaddyPorts::sm_SMObstacleSwitchDaddy_Socket_SenderPort;
TMemPool< SMObstacleSwitchDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SMObstacleSwitchDaddy_Socket_MemPool;

//! Vehicle input
TSenderPort< DriverSteeringWheelAngleDaddy_t > CustomDaddyPorts::sm_DriverSteeringWheelAngleDaddy_SenderPort;
TMemPool< DriverSteeringWheelAngleDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_DriverSteeringWheelAngleDaddy_MemPool;

//! HU interface
TSenderPort< HUDislayModeSwitchDaddy_t > CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort;
TMemPool< HUDislayModeSwitchDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_MemPool;

TSenderPort< HUCalibrationFlagDaddy_t > CustomDaddyPorts::sm_HUCalibrationStartDaddy_SenderPort;
TMemPool< HUCalibrationFlagDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUCalibrationStartDaddy_MemPool;

TSenderPort< HUDislayModeView5xDaddy_t > CustomDaddyPorts::sm_HUDislayModeView5xDaddy_SenderPort;
TMemPool< HUDislayModeView5xDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUDislayModeView5xDaddy_MemPool;

TSenderPort< HUDislayModeExpandDaddy_t > CustomDaddyPorts::sm_HUDislayModeExpandDaddy_SenderPort;
TMemPool< HUDislayModeExpandDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUDislayModeExpandDaddy_MemPool;

TSenderPort< HUImageWorkModeDaddy_t > CustomDaddyPorts::sm_HUImageWorkModeDaddy_SenderPort;
TMemPool< HUImageWorkModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUImageWorkModeDaddy_MemPool;

TSenderPort< HUTransparencyModeDaddy_t > CustomDaddyPorts::sm_HUTransparentModeDaddy_SenderPort;
TMemPool< HUTransparencyModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUTransparentModeDaddy_MemPool;

TSenderPort< HURadarWallButtonDaddy_t > CustomDaddyPorts::sm_HURadarWallButtonDaddy_SenderPort;
TMemPool< HURadarWallButtonDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HURadarWallButtonDaddy_MemPool;

TSenderPort< TrailerModeDaddy_t > CustomDaddyPorts::sm_HUTrailerButtonDaddy_SenderPort;
TMemPool< TrailerModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUTrailerButtonDaddy_MemPool;

TSenderPort< HUDislayModeExpandNewDaddy_t > CustomDaddyPorts::sm_HUDislayModeExpandNewDaddy_SenderPort;
TMemPool< HUDislayModeExpandNewDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUDislayModeExpandNewDaddy_MemPool;

TSenderPort< HURotateStatusDaddy_t > CustomDaddyPorts::sm_HURotateStatusDaddy_SenderPort;
TMemPool< HURotateStatusDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HURotateStatusDaddy_MemPool;

TSenderPort< HUShoWReqDaddy_t > CustomDaddyPorts::sm_HUShoWReqDaddy_SenderPort;
TMemPool< HUShoWReqDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUShoWReqDaddy_MemPool;

TSenderPort< HURemoteScreenReqDaddy_t > CustomDaddyPorts::sm_HURemoteScreenReqDaddy_SenderPort;
TMemPool< HURemoteScreenReqDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HURemoteScreenReqDaddy_MemPool;

TSenderPort< HUShoWSuspendDaddy_t > CustomDaddyPorts::sm_HUShoWSuspendDaddy_SenderPort;
TMemPool< HUShoWSuspendDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUShoWSuspendDaddy_MemPool;

TSenderPort< HUselSVSModeDaddy_t > CustomDaddyPorts::sm_HUselSVSModeDaddy_SenderPort;
TMemPool< HUselSVSModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUselSVSModeDaddy_MemPool;

TSenderPort< HUsvsAutoCamDaddy_t > CustomDaddyPorts::sm_HUsvsAutoCamDaddy_SenderPort;
TMemPool< HUsvsAutoCamDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUsvsAutoCamDaddy_MemPool;

TSenderPort< HUsvsIntegtOpenDaddy_t > CustomDaddyPorts::sm_HUsvsIntegtOpenDaddy_SenderPort;
TMemPool< HUsvsIntegtOpenDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUsvsIntegtOpenDaddy_MemPool;

TSenderPort< HUtouchEvenTypeDaddy_t > CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_SenderPort;
TMemPool< HUtouchEvenTypeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_MemPool;


TSenderPort< HUTwoFingerTouchEvenTypeDaddy_t > CustomDaddyPorts::sm_HUTwoFingerTouchEvenTypeDaddy_SenderPort;
TMemPool< HUTwoFingerTouchEvenTypeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUTwoFingerTouchEvenTypeDaddy_MemPool;

TSenderPort< HUShowStsDaddy_t > CustomDaddyPorts::sm_HUShowStsDaddy_SenderPort;
TMemPool< HUShowStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUShowStsDaddy_MemPool;

TSenderPort< HUrestoreSVSSetDaddy_t > CustomDaddyPorts::sm_HUrestoreSVSSetDaddy_SenderPort;
TMemPool< HUrestoreSVSSetDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUrestoreSVSSetDaddy_MemPool;

TSenderPort< HUbasePlateReqDaddy_t > CustomDaddyPorts::sm_HUbasePlateReqDaddy_SenderPort;
TMemPool< HUbasePlateReqDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUbasePlateReqDaddy_MemPool;

TSenderPort< HUvehTransReq_t > CustomDaddyPorts::sm_HUvehTransReqDaddy_SenderPort;
TMemPool< HUvehTransReq_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUvehTransReqDaddy_MemPool;

TSenderPort< HUvehTransLevel_t > CustomDaddyPorts::sm_HUvehTransLevelDaddy_SenderPort;
TMemPool< HUvehTransLevel_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUvehTransLevelDaddy_MemPool;

TSenderPort< HUFreemodeAngleDaddy_t > CustomDaddyPorts::sm_HUfreemodeAngleAzimuthDaddy_SenderPort;
TMemPool< HUFreemodeAngleDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUfreemodeAngleAzimuthDaddy_MemPool;

TSenderPort< HUFreemodeAngleDaddy_t > CustomDaddyPorts::sm_HUfreemodeAngleElevationDaddy_SenderPort;
TMemPool< HUFreemodeAngleDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUfreemodeAngleElevationDaddy_MemPool;

TSenderPort< HUoverlayDistReqDaddy_t > CustomDaddyPorts::sm_HUoverlayDistReqDaddy_SenderPort;
TMemPool< HUoverlayDistReqDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUoverlayDistReqDaddy_MemPool;

TSenderPort< HUoverlayReqDaddy_t > CustomDaddyPorts::sm_HUoverlayReqDaddy_SenderPort;
TMemPool< HUoverlayReqDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUoverlayReqDaddy_MemPool;

TSenderPort< HU3DCruseDaddy_t > CustomDaddyPorts::sm_HU3DCruseDaddy_SenderPort;
TMemPool< HU3DCruseDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HU3DCruseDaddy_MemPool;

TSenderPort< HUgestureStatusDaddy_t > CustomDaddyPorts::sm_HUgestureStatusDaddy_SenderPort;
TMemPool< HUgestureStatusDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUgestureStatusDaddy_MemPool;

TSenderPort< SVSShowReqDaddy_t > CustomDaddyPorts::sm_SVSShowReqDaddy_SenderPort;
TMemPool< SVSShowReqDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSShowReqDaddy_MemPool;

TSenderPort< SVSCurrentViewDaddy_t > CustomDaddyPorts::sm_SVSCurrentViewDaddy_SenderPort;
TMemPool< SVSCurrentViewDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSCurrentViewDaddy_MemPool;

TSenderPort< SVSIntegtOpenStsDaddy_t > CustomDaddyPorts::sm_SVSIntegtOpenStsDaddy_SenderPort;
TMemPool< SVSIntegtOpenStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSIntegtOpenStsDaddy_MemPool;

TSenderPort< SVSAutoCamStsDaddy_t > CustomDaddyPorts::sm_SVSAutoCamStsDaddy_SenderPort;
TMemPool< SVSAutoCamStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSAutoCamStsDaddy_MemPool;

TSenderPort< SVSViewModeStsDaddy_t > CustomDaddyPorts::sm_SVSViewModeStsDaddy_SenderPort;
TMemPool< SVSViewModeStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSViewModeStsDaddy_MemPool;

TSenderPort< SVSResolution_t > CustomDaddyPorts::sm_SVSResolutionDaddy_SenderPort;
TMemPool< SVSResolution_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSResolutionDaddy_MemPool;

TSenderPort< SVSOverlayDistStsDaddy_t > CustomDaddyPorts::sm_SVSOverlayDistStsDaddy_SenderPort;
TMemPool< SVSOverlayDistStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSOverlayDistStsDaddy_MemPool;

TSenderPort< SVSDynOverlayStsDaddy_t > CustomDaddyPorts::sm_SVSDynOverlayStsDaddy_SenderPort;
TMemPool< SVSDynOverlayStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSDynOverlayStsDaddy_MemPool;

TSenderPort< SVSBasePlateStsDaddy_t > CustomDaddyPorts::sm_SVSBasePlateStsDaddy_SenderPort;
TMemPool< SVSBasePlateStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSBasePlateStsDaddy_MemPool;

TSenderPort< SVSVehTransStsDaddy_t > CustomDaddyPorts::sm_SVSVehTransStsDaddy_SenderPort;
TMemPool< SVSVehTransStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSVehTransStsDaddy_MemPool;

TSenderPort< SVSVehTransStsInternalDaddy_t > CustomDaddyPorts::sm_SVSVehTransStsInternalDaddy_SenderPort;
TMemPool< SVSVehTransStsInternalDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSVehTransStsInternalDaddy_MemPool;

TSenderPort< SVS3DCruStsDaddy_t > CustomDaddyPorts::sm_SVS3DCruStsDaddy_SenderPort;
TMemPool< SVS3DCruStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVS3DCruStsDaddy_MemPool;

TSenderPort< SVSzoomStsDaddy_t > CustomDaddyPorts::sm_SVSzoomStsDaddy_SenderPort;
TMemPool< SVSzoomStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSzoomStsDaddy_MemPool;

// Start of BYD
TSenderPort< SVSViewStateDaddy_t > CustomDaddyPorts::sm_SVSViewStateDaddy_SenderPort;
TMemPool< SVSViewStateDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSViewStateDaddy_MemPool;

TSenderPort< SVSWorkModeDaddy_t > CustomDaddyPorts::sm_SVSWorkModeDaddy_SenderPort;
TMemPool< SVSWorkModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::smSVSWorkModeDaddy_MemPool;

TSenderPort< SVSOnOffStateDaddy_t > CustomDaddyPorts::sm_SVSOnOffStateDaddy_SenderPort;
TMemPool< SVSOnOffStateDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSOnOffStateDaddy_MemPool;

TSenderPort< SVSvidoutModeDaddy_t > CustomDaddyPorts::sm_SVSvidoutModeDaddy_SenderPort;
TMemPool< SVSvidoutModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSvidoutModeDaddy_MemPool;

TSenderPort< SVSRotateStatusDaddy_t > CustomDaddyPorts::sm_SVSRotateStatusDaddy_SenderPort;
TMemPool< SVSRotateStatusDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSRotateStatusDaddy_MemPool;

TSenderPort< DayNightThemeDaddy_t > CustomDaddyPorts::sm_dayNightThemeDaddy_SenderPort;
TMemPool< DayNightThemeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_dayNightThemeDaddy_MemPool;

TSenderPort< SVSTrajCfgStateDaddy_t > CustomDaddyPorts::sm_SVSTrajCfgStateDaddy_SenderPort;
TMemPool< SVSTrajCfgStateDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSTrajCfgStateDaddy_MemPool;

TSenderPort< SVSLVDSvidOutModeDaddy_t > CustomDaddyPorts::sm_SVSLVDSvidOutModeDaddy_SenderPort;
TMemPool< SVSLVDSvidOutModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSLVDSvidOutModeDaddy_MemPool;

TSenderPort< SVSImageConfigDaddy_t > CustomDaddyPorts::sm_SVSImageConfigDaddy_SenderPort;
TMemPool< SVSImageConfigDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSImageConfigDaddy_MemPool;

TSenderPort< SVSCarBodyDaddy_t > CustomDaddyPorts::sm_SVSCarBodyDaddy_SenderPort;
TMemPool< SVSCarBodyDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSCarBodyDaddy_MemPool;

TSenderPort< SVSExpandedViewStateDaddy_t > CustomDaddyPorts::sm_SVSExpandedViewStateDaddy_SenderPort;
TMemPool< SVSExpandedViewStateDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSExpandedViewStateDaddy_MemPool;

TSenderPort< SVSNewExpandedViewStateDaddy_t > CustomDaddyPorts::sm_SVSNewExpandedViewStateDaddy_SenderPort;
TMemPool< SVSNewExpandedViewStateDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSNewExpandedViewStateDaddy_MemPool;

TSenderPort< SVSVehColorAckDaddy_t > CustomDaddyPorts::sm_SVSVehColorAckDaddy_SenderPort;
TMemPool< SVSVehColorAckDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSVehColorAckDaddy_MemPool;

TSenderPort< SVSUnavlMsgsDaddy_t > CustomDaddyPorts::sm_SVSUnavlMsgsDaddy_SenderPort;
TMemPool< SVSUnavlMsgsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSUnavlMsgsDaddy_MemPool;

TSenderPort< pc::daddy::UltrasonicDataDaddy > CustomDaddyPorts::sm_customUltrasonicDataDaddySenderPort;
TMemPool< pc::daddy::UltrasonicDataDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_customUltrasonicDataDaddy_MemPool;

TSenderPort< cc::daddy::TileSplineInterpolateArrayDaddy > CustomDaddyPorts::sm_tileSplineInterpolateArrayDaddySenderPort;
TMemPool< cc::daddy::TileSplineInterpolateArrayDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_tileSplineInterpolateArrayDaddy_MemPool;

// End of BYD

TSenderPort< SVSViewStsDaddy_t > CustomDaddyPorts::sm_SVSFrViewStsDaddy_SenderPort;
TMemPool< SVSViewStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSFrViewStsDaddy_MemPool;

TSenderPort< SVSViewStsDaddy_t > CustomDaddyPorts::sm_SVSLeViewStsDaddy_SenderPort;
TMemPool< SVSViewStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSLeViewStsDaddy_MemPool;

TSenderPort< SVSViewStsDaddy_t > CustomDaddyPorts::sm_SVSReViewStsDaddy_SenderPort;
TMemPool< SVSViewStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSReViewStsDaddy_MemPool;

TSenderPort< SVSViewStsDaddy_t > CustomDaddyPorts::sm_SVSRiViewStsDaddy_SenderPort;
TMemPool< SVSViewStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSRiViewStsDaddy_MemPool;

TSenderPort< CpcOverlaySwitchDaddy_t > CustomDaddyPorts::sm_CpcOverlaySwitchDaddy_SenderPort;
TMemPool< CpcOverlaySwitchDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_CpcOverlaySwitchDaddy_MemPool;

TSenderPort< SwVersionShowSwitchDaddy_t > CustomDaddyPorts::sm_SwVersionShowSwitchDaddy_SenderPort;
TMemPool< SwVersionShowSwitchDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SwVersionShowSwitchDaddy_MemPool;

TSenderPort< SWInfoDaddy_t > CustomDaddyPorts::sm_SWInfoDaddy_SenderPort;
TMemPool< SWInfoDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SWInfoDaddy_MemPool;

TSenderPort< DoorLockStsDaddy_t > CustomDaddyPorts::sm_DoorLockSts_SenderPort;
TMemPool< DoorLockStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_DoorLockSts_MemPool;

TSenderPort< TimeStyleDaddy_t > CustomDaddyPorts::sm_TimeShow_SenderPort;
TMemPool< TimeStyleDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_TimeShow_MemPool;

//! View Mode State
TSenderPort< VMStateDaddy_t > CustomDaddyPorts::sm_ViewModeState_SenderPort;
TMemPool< VMStateDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_Debug_ViewModeState_MemPool;

//! System State
TSenderPort< SystemStateDaddy > CustomDaddyPorts::sm_systemState_SenderPort;
TMemPool< SystemStateDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_systemState_MemPool;

//! Closest Obstacle
TSenderPort< ClosestObstacleDaddy > CustomDaddyPorts::sm_EnvProcSoundUnitClosestObstacleDaddy_SenderPort;
TMemPool< ClosestObstacleDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ClosestObstacleDaddy_MemPool;

TSenderPort< TrailerSvsDaddy > CustomDaddyPorts::sm_TrailerSvs_SenderPort;
TMemPool< TrailerSvsDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_TrailerSvs_MemPool;

TSenderPort< ThreatDetectedDaddy_t > CustomDaddyPorts::sm_threatDetected_Socket_SenderPort;
TMemPool< ThreatDetectedDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_threatDetected_Socket_MemPool;

//! User Movement Intention
TSenderPort< UserMovementIntentionDaddy_t > CustomDaddyPorts::sm_userMovementIntention_Socket_SenderPort;
TMemPool< UserMovementIntentionDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_userMovementIntentionDaddy_Socket_MemPool;

//! LSMG Vehicle Movement
TSenderPort< LSMG_VehicleMovementDaddy_t > CustomDaddyPorts::sm_LSMG_VehicleMovement_Socket_SenderPort;
TMemPool< LSMG_VehicleMovementDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_LSMG_VehicleMovement_Socket_MemPool;

//! Vehicle Transparency
TSenderPort< VehicleAlphaDaddy > CustomDaddyPorts::sm_VehicleAlpha_Socket_SenderPort;
TMemPool< VehicleAlphaDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_VehicleAlpha_Socket_MemPool;

//! Vehicle diffuse color
TSenderPort< ColorDaddy > CustomDaddyPorts::sm_VehicleDiffuseColor_Socket_SenderPort;
TMemPool< ColorDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_VehicleDiffuseColor_Socket_MemPool;

//! Vehicle color index
TSenderPort< ColorIndexDaddy > CustomDaddyPorts::sm_VehicleDiffuseColorIndex_SenderPort;
TMemPool< ColorIndexDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_VehicleDiffuseColorIndex_MemPool;

//! Vehicle specular color1
TSenderPort< ColorDaddy > CustomDaddyPorts::sm_VehicleSpecColor1_Socket_SenderPort;
TMemPool< ColorDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_VehicleSpecColor1_Socket_MemPool;

//! Vehicle specular color2
TSenderPort< ColorDaddy > CustomDaddyPorts::sm_VehicleSpecColor2_Socket_SenderPort;
TMemPool< ColorDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_VehicleSpecColor2_Socket_MemPool;

//! Other Vehicle Model Parameters
TSenderPort< VehicleModelParamDaddy > CustomDaddyPorts::sm_VehicleModelParam_Socket_SenderPort;
TMemPool< VehicleModelParamDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_VehicleModelParam_Socket_MemPool;

//! Als Dipped Beam
TSenderPort< DippedBeamStateDaddy > CustomDaddyPorts::sm_AlsDippedBeamState_SenderPort;
TMemPool< DippedBeamStateDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_AlsDippedBeamState_MemPool;

//Distance to Stop Daddy
TSenderPort< DistanceToStopDaddy > CustomDaddyPorts::sm_DistanceToStopDaddy_SenderPort;
TMemPool< DistanceToStopDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_DistanceToStopDaddy_MemPool;

//Distance to Stop Location Daddy
TSenderPort< StopLineLocationDaddy > CustomDaddyPorts::sm_StopLineLocationDaddy_SenderPort;
TMemPool< StopLineLocationDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_StopLineLocationDaddy_MemPool;

//Automatic Parking Daddy
//TODO: clean up
TSenderPort< AutomaticParkingDaddy > CustomDaddyPorts::sm_AutomaticParkingDaddy_SenderPort;
TMemPool< AutomaticParkingDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_AutomaticParkingDaddy_MemPool;

//LMStatus Daddy
TSenderPort< LMStatusDaddy_t > CustomDaddyPorts::sm_LMStatusDaddy_SenderPort;
TMemPool< LMStatusDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_LMStatusDaddy_MemPool;

//IsOffroad Daddy
TSenderPort< IsOffroadDaddy_t > CustomDaddyPorts::sm_IsOffroadDaddy_SenderPort;
TMemPool< IsOffroadDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_IsOffroadDaddy_MemPool;

//CarMode Daddy
TSenderPort< CarModeDaddy_t > CustomDaddyPorts::sm_CarModeDaddy_SenderPort;
TMemPool< CarModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_CarModeDaddy_MemPool;

//PowerMode Daddy
TSenderPort< PowerModeDaddy_t > CustomDaddyPorts::sm_PowerModeDaddy_SenderPort;
TMemPool< PowerModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PowerModeDaddy_MemPool;

//FCTA/RCTA Daddy
TSenderPort< FCTARightDaddy_t > CustomDaddyPorts::sm_FCTARightDaddy_SenderPort;
TMemPool< FCTARightDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_FCTARightDaddy_MemPool;

TSenderPort< FCTALeftDaddy_t > CustomDaddyPorts::sm_FCTALeftDaddy_SenderPort;
TMemPool< FCTALeftDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_FCTALeftDaddy_MemPool;

TSenderPort< RCTARightDaddy_t > CustomDaddyPorts::sm_RCTARightDaddy_SenderPort;
TMemPool< RCTARightDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_RCTARightDaddy_MemPool;

TSenderPort< RCTALeftDaddy_t > CustomDaddyPorts::sm_RCTALeftDaddy_SenderPort;
TMemPool< RCTALeftDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_RCTALeftDaddy_MemPool;

//VR Switch / FCP SVM button
TSenderPort< VRSwitchSVMDaddy_t > CustomDaddyPorts::sm_VRSwitchSVMDaddy_SenderPort;
TMemPool< VRSwitchSVMDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_VRSwitchSVMDaddy_MemPool;

TSenderPort< FreeparkingParkable_t > CustomDaddyPorts::sm_FreeParkingParkableDaddy_SenderPort;
TMemPool< FreeparkingParkable_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_FreeParkingParkableDaddy_MemPool;

TSenderPort< VRSwitchFailStDaddy_t > CustomDaddyPorts::sm_VRSwitchFailStDaddy_SenderPort;
TMemPool< VRSwitchFailStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_VRSwitchFailStDaddy_MemPool;

TSenderPort< FCP_SVMButtonPressed_t > CustomDaddyPorts::sm_FCP_SVMButtonPressedDaddy_SenderPort;
TMemPool< FCP_SVMButtonPressed_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_FCP_SVMButtonPressedDaddy_MemPool;

//LSAEB Daddy
TSenderPort< AebVmcOpModeDaddy_t > CustomDaddyPorts::sm_LSAEBVmcOpModeDaddy_SenderPort;
TMemPool< AebVmcOpModeDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_LSAEBVmcOpModeDaddy_MemPool;

//C2W: the calibration status
TSenderPort< C2WCalibStatusDaddy_t > CustomDaddyPorts::sm_CamCalibrationStatusDaddy_SenderPort;
TMemPool< C2WCalibStatusDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_CamCalibrationStatusDaddy_MemPool;

//C2W: the nominal calibration values
TSenderPort<NormalCalibrationDaddy> CustomDaddyPorts::sm_CpcNorminalCalibDaddy_SenderPort;
TMemPool<NormalCalibrationDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> CustomDaddyPorts::sm_CpcNorminalCalibDaddy_MemPool;

//! Backchannel Rx Signals, remaining in case being used in other logic
TSenderPort< PIVI_ManualVideoSetupReq_t > CustomDaddyPorts::sm_PIVI_ManualVideoSetupReq_SenderPort;
TMemPool< PIVI_ManualVideoSetupReq_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PIVI_ManualVideoSetupReq_MemPool;

TSenderPort< PIVI_DayNightThemeReq_t > CustomDaddyPorts::sm_PIVI_DayNightThemeReq_SenderPort;
TMemPool< PIVI_DayNightThemeReq_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PIVI_DayNightThemeReq_MemPool;

TSenderPort< PIVI_ViewBufferStatusACK_t > CustomDaddyPorts::sm_PIVI_ViewBufferStatusACK_SenderPort;
TMemPool< PIVI_ViewBufferStatusACK_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PIVI_ViewBufferStatusACK_MemPool;

//! Backchannel Tx Signals, remaining in case being used in other logic
TSenderPort< NFSM_MovementDirectionUpdate_t > CustomDaddyPorts::sm_NFSM_MovementDirectionUpdate_SenderPort;
TMemPool< NFSM_MovementDirectionUpdate_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_NFSM_MovementDirectionUpdate_MemPool;

TSenderPort< NFSM_ViewBufferStatus_t > CustomDaddyPorts::sm_NFSM_ViewBufferStatus_SenderPort;
TMemPool< NFSM_ViewBufferStatus_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_NFSM_ViewBufferStatus_MemPool;

// FlexRay LSMGActivationSetReq
TSenderPort< LSMGActivationSetReq_t > CustomDaddyPorts::sm_FlexRayRx_LSMGActivationSetReq_SenderPort;
TMemPool< LSMGActivationSetReq_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_FlexRayRx_LSMGActivationSetReq_MemPool;

// PDM R5->Linux
TSenderPort< Pdm3DCruStsDaddy_t > CustomDaddyPorts::sm_PdmR5ToLinux_3DCruStsDaddy_SenderPort;
TMemPool< Pdm3DCruStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PdmR5ToLinux_3DCruStsDaddy_MemPool;

TSenderPort< PdmIntegtOpenStsDaddy_t > CustomDaddyPorts::sm_PdmR5ToLinux_IntegtOpenStsDaddy_SenderPort;
TMemPool< PdmIntegtOpenStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PdmR5ToLinux_IntegtOpenStsDaddy_MemPool;

TSenderPort< PdmAutoCamStsDaddy_t > CustomDaddyPorts::sm_PdmR5ToLinux_AutoCamStsDaddy_SenderPort;
TMemPool< PdmAutoCamStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PdmR5ToLinux_AutoCamStsDaddy_MemPool;

TSenderPort< PdmDynOverlayStsDaddy_t > CustomDaddyPorts::sm_PdmR5ToLinux_DynOverlayStsDaddy_SenderPort;
TMemPool< PdmDynOverlayStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PdmR5ToLinux_DynOverlayStsDaddy_MemPool;

TSenderPort< PdmOverlayDistStsDaddy_t > CustomDaddyPorts::sm_PdmR5ToLinux_OverlayDistStsDaddy_SenderPort;
TMemPool< PdmOverlayDistStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PdmR5ToLinux_OverlayDistStsDaddy_MemPool;

TSenderPort< PdmBasePlateStsDaddy_t > CustomDaddyPorts::sm_PdmR5ToLinux_BasePlateStsDaddy_SenderPort;
TMemPool< PdmBasePlateStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PdmR5ToLinux_BasePlateStsDaddy_MemPool;

TSenderPort< PdmVehTransStsDaddy_t > CustomDaddyPorts::sm_PdmR5ToLinux_VehTransStsDaddy_SenderPort;
TMemPool< PdmVehTransStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PdmR5ToLinux_VehTransStsDaddy_MemPool;

TSenderPort< ColorIndexDaddy > CustomDaddyPorts::sm_PdmR5ToLinux_VehColorStsDaddy_SenderPort;
TMemPool< ColorIndexDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PdmR5ToLinux_VehColorStsDaddy_MemPool;

// PDM Linux->R5
TSenderPort< Pdm3DCruStsDaddy_t > CustomDaddyPorts::sm_PdmLinuxToR5_3DCruStsDaddy_SenderPort;
TMemPool< Pdm3DCruStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PdmLinuxToR5_3DCruStsDaddy_MemPool;

TSenderPort< PdmIntegtOpenStsDaddy_t > CustomDaddyPorts::sm_PdmLinuxToR5_IntegtOpenStsDaddy_SenderPort;
TMemPool< PdmIntegtOpenStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PdmLinuxToR5_IntegtOpenStsDaddy_MemPool;

TSenderPort< PdmAutoCamStsDaddy_t > CustomDaddyPorts::sm_PdmLinuxToR5_AutoCamStsDaddy_SenderPort;
TMemPool< PdmAutoCamStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PdmLinuxToR5_AutoCamStsDaddy_MemPool;

TSenderPort< PdmDynOverlayStsDaddy_t > CustomDaddyPorts::sm_PdmLinuxToR5_DynOverlayStsDaddy_SenderPort;
TMemPool< PdmDynOverlayStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PdmLinuxToR5_DynOverlayStsDaddy_MemPool;

TSenderPort< PdmOverlayDistStsDaddy_t > CustomDaddyPorts::sm_PdmLinuxToR5_OverlayDistStsDaddy_SenderPort;
TMemPool< PdmOverlayDistStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PdmLinuxToR5_OverlayDistStsDaddy_MemPool;

TSenderPort< PdmBasePlateStsDaddy_t > CustomDaddyPorts::sm_PdmLinuxToR5_BasePlateStsDaddy_SenderPort;
TMemPool< PdmBasePlateStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PdmLinuxToR5_BasePlateStsDaddy_MemPool;

TSenderPort< PdmVehTransStsDaddy_t > CustomDaddyPorts::sm_PdmLinuxToR5_VehTransStsDaddy_SenderPort;
TMemPool< PdmVehTransStsDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PdmLinuxToR5_VehTransStsDaddy_MemPool;

TSenderPort< ColorIndexDaddy > CustomDaddyPorts::sm_PdmLinuxToR5_VehColorStsDaddy_SenderPort;
TMemPool< ColorIndexDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_PdmLinuxToR5_VehColorStsDaddy_MemPool;

//! CCF
TSenderPort< CcfDaddy > CustomDaddyPorts::sm_CCF_SenderPort;
TMemPool< CcfDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_CCF_MemPool;

//! LCF
TSenderPort< LcfDaddy > CustomDaddyPorts::sm_LCF_SenderPort;
TMemPool< LcfDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_LCF_MemPool;

//! Variant
TSenderPort< VariantDaddy > CustomDaddyPorts::sm_Variant_SenderPort;
TMemPool< VariantDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_Variant_MemPool;

// Vehicle Lights
TSenderPort< CustomVehicleLightsDaddy > CustomDaddyPorts::sm_CustomVehicleLights_SenderPort;
TMemPool< CustomVehicleLightsDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_CustomVehicleLights_MemPool;

//! Internal View Buffer Status
TSenderPort< NFSM_ViewBufferStatus_t > CustomDaddyPorts::sm_ViewBufferStatus_SenderPort;
TMemPool< NFSM_ViewBufferStatus_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ViewBufferStatus_MemPool;

//! GBC Vehicle Transparency
TSenderPort< GbcVehicleTransparency_t > CustomDaddyPorts::sm_GbcVehicleTransparency_SenderPort;
TMemPool< GbcVehicleTransparency_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_GbcVehicleTransparency_MemPool;

//! GBC Wheel Transparency
TSenderPort< GbcWheelTransparency_t > CustomDaddyPorts::sm_GbcWheelTransparency_SenderPort;
TMemPool< GbcWheelTransparency_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_GbcWheelTransparency_MemPool;

//! View Animation Completed
TSenderPort< ViewAnimationCompleted_t > CustomDaddyPorts::sm_viewAnimationCompleted_SenderPort;
TMemPool< ViewAnimationCompleted_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_viewAnimationCompleted_MemPool;

//!Diag Routine
TSenderPort< DiagRoutine_t > CustomDaddyPorts::sm_diagRoutine_SenderPort;
TMemPool< DiagRoutine_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_diagRoutine_MemPool;

//! HMI
TSenderPort< HmiData_Daddy > CustomDaddyPorts::sm_HmiData_SenderPort;
TMemPool< HmiData_Daddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HmiData_MemPool;

//! Hemispherical camera controller - return channel from controller to state machine
TSenderPort< HemisphereParameterData_Daddy > CustomDaddyPorts::sm_HemisphereParameterData_SenderPort;
TMemPool< HemisphereParameterData_Daddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HemisphereParameterData_MemPool;

//! HU interactive camera commands
TSenderPort< HUCameraCommandsDaddy > CustomDaddyPorts::sm_HUCameraCommandsDaddySenderPort;
TMemPool< HUCameraCommandsDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_HUCameraCommandsDaddyMemPool;

//! SolidBasePlateStateDaddy
TSenderPort<SolidBasePlateStateDaddy> CustomDaddyPorts::sm_groundplane_triangle_SenderPort;
TMemPool<SolidBasePlateStateDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> CustomDaddyPorts::sm_groundplane_triangle_MemPool;

//! free view mode camera position
TSenderPort< CameraPositionDaddy_t > CustomDaddyPorts::sm_CameraPositionDaddySenderPort;
TMemPool< CameraPositionDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_CameraPositionDaddyMemPool;

//! Degradation FIDs
TSenderPort< DegradationFid_t > CustomDaddyPorts::sm_degradationFid_SenderPort;
TMemPool< DegradationFid_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_degradationFid_MemPool;

//! Impostor transparency
TSenderPort<ImpostorTransparencyDaddy> CustomDaddyPorts::sm_impostorTransparencySenderPort;
TMemPool<ImpostorTransparencyDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> CustomDaddyPorts::sm_impostorTransparencyMemPool;

//! SVS displayed view
TSenderPort< SVSDisplayedViewDaddy_t > CustomDaddyPorts::sm_SVSDisplayedViewDaddy_SenderPort;
TMemPool< SVSDisplayedViewDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSDisplayedViewDaddy_MemPool;

//! SVS free view mode status
TSenderPort< SVSFreeModeStDaddy_t > CustomDaddyPorts::sm_SVSFreeModeStDaddy_SenderPort;
TMemPool< SVSFreeModeStDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSFreeModeStDaddy_MemPool;

//! SVS park spot data after matrix transformation
TSenderPort< ParkUISpotData_t > CustomDaddyPorts::sm_SVSParkUISpotDataDaddy_SenderPort;
TMemPool< ParkUISpotData_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSParkUISpotDataDaddy_MemPool;

//! SVS to State machine, park confirm interface exist or not
TSenderPort< ParkConfirmInterfaceExist_t > CustomDaddyPorts::sm_SVSParkConfirmInterfaceDataDaddy_SenderPort;
TMemPool< ParkConfirmInterfaceExist_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSParkConfirmInterfaceDataDaddy_MemPool;

//! SVS park spot data after matrix transformation
TSenderPort< ParkUIAvailableParkingSlot_t > CustomDaddyPorts::sm_SVSParkUIAvailableParkingSlotDaddy_SenderPort;
TMemPool< ParkUIAvailableParkingSlot_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SVSParkUIAvailableParkingSlotDaddy_MemPool;

//! Raw Data for Odometry
TSenderPort< CustomVhmAbstRaw_t > CustomDaddyPorts::sm_customVhmAbstRawDataDaddy_SenderPort;
TMemPool< CustomVhmAbstRaw_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_customVhmAbstRawDataDaddy_MemPool;

//! Extrinsic Data to cpc debug overlay
// TSenderPort< CpcCornerDetectionStatus_t > CustomDaddyPorts::sm_cpcCornerDetectionDataDaddy_SenderPort;
// TMemPool< CpcCornerDetectionStatus_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_cpcCornerDetectionDataDaddy_MemPool;

//! cpc status to cpc debug overlay
// TSenderPort< CpcStatus_st_t > CustomDaddyPorts::sm_cpcStatusDaddy_SenderPort;
// TMemPool< CpcStatus_st_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_cpcStatusDaddy_MemPool;

// //! CPC
TSenderPort<CpcToCpcWrapper_t> CustomDaddyPorts::sm_cpcToCpcWrapper_SenderPort;
TMemPool<CpcToCpcWrapper_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> CustomDaddyPorts::sm_cpcToCpcWrapper_MemPool;

TSenderPort<CpcWrapperToCpc_t> CustomDaddyPorts::sm_cpcWrapperToCpc_SenderPort;
TMemPool<CpcWrapperToCpc_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> CustomDaddyPorts::sm_cpcWrapperToCpc_MemPool;

TSenderPort<CpcToSvsOverlay_t> CustomDaddyPorts::sm_cpcToSvsOverlay_SenderPort;
TMemPool<CpcToSvsOverlay_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> CustomDaddyPorts::sm_cpcToSvsOverlay_MemPool;

TSenderPort<SvsOverlayToCpc_t> CustomDaddyPorts::sm_svsOverlayToCpc_SenderPort;
TMemPool<SvsOverlayToCpc_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE> CustomDaddyPorts::sm_svsOverlayToCpc_MemPool;

//! dynamic gear overlay
TSenderPort< DynamicGearActive_t > CustomDaddyPorts::sm_dynamicGearStatus_SenderPort;
TMemPool< DynamicGearActive_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_dynamicGearStatus_MemPool;

//! notice rolling
TSenderPort< NoticeRolling_t > CustomDaddyPorts::sm_noticeRolling_SenderPort;
TMemPool< NoticeRolling_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_noticeRolling_MemPool;

//! PlanViewEnlargeStatusDaddy
TSenderPort< PlanViewEnlargeStatusDaddy > CustomDaddyPorts::sm_planViewEnlargeStatus_SenderPort;
TMemPool< PlanViewEnlargeStatusDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_planViewEnlargeStatus_MemPool;

//! SideViewEnableStatusDaddy
TSenderPort< SideViewEnableStatusDaddy > CustomDaddyPorts::sm_sideViewEnableStatus_SenderPort;
TMemPool< SideViewEnableStatusDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_sideViewEnableStatus_MemPool;

//! TopViewEnableStatusDaddy
TSenderPort< TopViewEnableStatusDaddy > CustomDaddyPorts::sm_topViewEnableStatus_SenderPort;
TMemPool< TopViewEnableStatusDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_topViewEnableStatus_MemPool;

//! EViewAnimationDaddy
TSenderPort< EViewAnimationDaddy > CustomDaddyPorts::sm_animationDaddy_SenderPort;
TMemPool< EViewAnimationDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_animationDaddy_MemPool;

//! CamPosAxis2RqDaddy
TSenderPort< CamPosAxis2RqDaddy > CustomDaddyPorts::sm_camPosAxis2Rq_SenderPort;
TMemPool< CamPosAxis2RqDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_camPosAxis2Rq_MemPool;

//! Component test trigger
TSenderPort< ComponentTestSwitchDaddy > CustomDaddyPorts::sm_componentTestSwitch_SenderPort;
TMemPool< ComponentTestSwitchDaddy, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_componentTestSwitch_MemPool;

//! Virtual Reality
TSenderPort< ParkSlotDaddy_t > CustomDaddyPorts::sm_ParkSlotDaddy_SenderPort;
TMemPool< ParkSlotDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkSlotDaddy_MemPool;

TSenderPort< ParkSlotDaddy_t > CustomDaddyPorts::sm_ParkSlotRefinedDaddy_SenderPort;
TMemPool< ParkSlotDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkSlotRefinedDaddy_MemPool;

//! Fusion object
TSenderPort< FusionObjectDaddy_t > CustomDaddyPorts::sm_fusionObject_SenderPort;
TMemPool< FusionObjectDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_fusionObject_MemPool;

//! SitOcp
TSenderPort< SitOcpDaddy_t > CustomDaddyPorts::sm_sitOcp_SenderPort;
TMemPool< SitOcpDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_sitOcp_MemPool;

//! Pedestrian
TSenderPort< PedestrianDaddy_t > CustomDaddyPorts::sm_pedestrianObj_SenderPort;
TMemPool< PedestrianDaddy_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_pedestrianObj_MemPool;

//! ParkhmiToSvs
TSenderPort< ParkhmiToSvs_t > CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort;
TMemPool< ParkhmiToSvs_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkhmiToSvs_MemPool;

//! SvsToParkhmi
TSenderPort< SvsToParkhmi_t > CustomDaddyPorts::sm_SvsToParkhmi_SenderPort;
TMemPool< SvsToParkhmi_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SvsToParkhmi_MemPool;

//! FreeParkingSlot
TSenderPort< FreeParkingSlot_t > CustomDaddyPorts::sm_FreeParkingSlot_SenderPort;
TMemPool< FreeParkingSlot_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_FreeParkingSlot_MemPool;

//! FreeParkingSlot
TSenderPort< FreeParkingCorners_t > CustomDaddyPorts::sm_FreeParkingSlotInternal_SenderPort;
TMemPool< FreeParkingCorners_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_FreeParkingSlotInternal_MemPool;

//! SlotSelectedId
TSenderPort< SlotSelectedId_t > CustomDaddyPorts::sm_SlotSelectedId_SenderPort;
TMemPool< SlotSelectedId_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SlotSelectedId_MemPool;

//! ParkoutSelectedDirection
TSenderPort< ParkoutSelectedDirection_t > CustomDaddyPorts::sm_ParkoutSelectedDirection_SenderPort;
TMemPool< ParkoutSelectedDirection_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ParkoutSelectedDirection_MemPool;

//! AirSuspensionHeight
TSenderPort< AirSuspensionHeight_t > CustomDaddyPorts::sm_AirSuspensionHeight_SenderPort;
TMemPool< AirSuspensionHeight_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_AirSuspensionHeight_MemPool;

//! ZoomLevel HU -> SVS
TSenderPort< ZoomLevel_t > CustomDaddyPorts::sm_ZoomLevel_SenderPort;
TMemPool< ZoomLevel_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ZoomLevel_MemPool;

//! ZoomLevel SVS -> HU
TSenderPort< ZoomLevel_t > CustomDaddyPorts::sm_ZoomLevelIPC_SenderPort;
TMemPool< ZoomLevel_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_ZoomLevelIPC_MemPool;

//! RemoveDistortion
TSenderPort< RemoveDistortion_t > CustomDaddyPorts::sm_RemoveDistortion_SenderPort;
TMemPool< RemoveDistortion_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_RemoveDistortion_MemPool;

//! BirdEyeView switch
TSenderPort< BirdEyeViewSwitch_t > CustomDaddyPorts::sm_BirdEyeViewSwitch_SenderPort;
TMemPool< BirdEyeViewSwitch_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_BirdEyeViewSwitch_MemPool;

//! SurroundViewAngle
TSenderPort< SurroundViewRotateAngle_t > CustomDaddyPorts::sm_SurroundViewRotateAngle_SenderPort;
TMemPool< SurroundViewRotateAngle_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_SurroundViewRotateAngle_MemPool;

//! CrabGuideline
TSenderPort< CrabGuideline_t > CustomDaddyPorts::sm_CrabGuideline_SenderPort;
TMemPool< CrabGuideline_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_CrabGuideline_MemPool;

//! GoldenEmblem
TSenderPort< GoldenEmblem_t > CustomDaddyPorts::sm_GoldenEmblem_SenderPort;
TMemPool< GoldenEmblem_t, pc::daddy::s_DEFAULT_MEMPOOL_SIZE > CustomDaddyPorts::sm_GoldenEmblem_MemPool;

bool CustomDaddyPorts::sm_customerMemoryPoolsAreConnected = false;
bool CustomDaddyPorts::sm_customerMemoryPoolsHaveInitialData = false;

void CustomDaddyPorts::connectMemPools()    // PRQA S 6044
{
  //first, connect the base pools
  pc::daddy::BaseDaddyPorts::connectMemPools();
  //then, connect the custom pools
  if( false == sm_customerMemoryPoolsAreConnected )
  {
    sm_SocketCmdDaddy_SenderPort                          .connectMemPool( sm_SocketCmdDaddy_MemPool );
    sm_parkingInSpots_SenderPort                          .connectMemPool( sm_parkingInSpots_MemPool );
    sm_parkingOutSpot_SenderPort                          .connectMemPool( sm_parkingOutSpot_MemPool );
    sm_PMA_TravelDistDesired_SenderPort                   .connectMemPool( sm_PMA_TravelDistDesired_MemPool );
    sm_pmaCtlMrgr_SenderPort                              .connectMemPool( sm_pmaCtlMrgr_MemPool );
    sm_PasButtonStatusDaddy_SenderPort                    .connectMemPool( sm_PasButtonStatusDaddy_MemPool );
    sm_PasButtonPressedStDaddy_SenderPort                 .connectMemPool( sm_PasButtonPressedStDaddy_MemPool );
    sm_AutoCamActivButtonPressedStDaddy_SenderPort        .connectMemPool( sm_AutoCamActivButtonPressedStDaddy_MemPool);
    sm_ParkConfirmButtonStDaddy_SenderPort                .connectMemPool( sm_ParkConfirmButtonStDaddy_MemPool);
    sm_ParkModeSelectedStDaddy_SenderPort                 .connectMemPool( sm_ParkModeSelectedStDaddy_MemPool);
    sm_ParkTypeSelectedStDaddy_SenderPort                 .connectMemPool( sm_ParkTypeSelectedStDaddy_MemPool );
    sm_ParkSlotSelectedStDaddy_SenderPort                 .connectMemPool( sm_ParkSlotSelectedStDaddy_MemPool );
    sm_ParkDrvFuncOffStDaddy_SenderPort                   .connectMemPool( sm_ParkDrvFuncOffStDaddy_MemPool );
    sm_ParkPauseButtonStDaddy_SenderPort                  .connectMemPool( sm_ParkPauseButtonStDaddy_MemPool );
    sm_ParkDirectionStDaddy_SenderPort                    .connectMemPool( sm_ParkDirectionStDaddy_MemPool );
    sm_FreeParkingSpaceTypeButtonStDaddy_SenderPort       .connectMemPool( sm_FreeParkingSpaceTypeButtonStDaddy_MemPool );
    sm_FreeParkingConfirmButtonStDaddy_SenderPort         .connectMemPool( sm_FreeParkingConfirmButtonStDaddy_MemPool );
    sm_FreeParkingReturnButtonStDaddy_SenderPort          .connectMemPool( sm_FreeParkingReturnButtonStDaddy_MemPool );
    sm_ParkOutSideButtonStDaddy_SenderPort                .connectMemPool( sm_ParkOutSideButtonStDaddy_MemPool );
    sm_ParkDriverIndSearchDaddy_SenderPort                .connectMemPool( sm_ParkDriverIndSearchDaddy_MemPool );
    sm_ParkRPAAvaliableDaddy_SenderPort                   .connectMemPool( sm_ParkRPAAvaliableDaddy_MemPool );
    sm_PasWarnToneDaddy_SenderPort                        .connectMemPool( sm_PasWarnToneDaddy_MemPool );
    sm_PasStatusDaddy_SenderPort                          .connectMemPool( sm_PasStatusDaddy_MemPool );
    sm_SdwStatusDaddy_SenderPort                          .connectMemPool( sm_SdwStatusDaddy_MemPool );
    sm_SdwFStatusDaddy_SenderPort                         .connectMemPool( sm_SdwFStatusDaddy_MemPool );
    sm_SdwFMStatusDaddy_SenderPort                        .connectMemPool( sm_SdwFMStatusDaddy_MemPool );
    sm_SdwRMStatusDaddy_SenderPort                        .connectMemPool( sm_SdwRMStatusDaddy_MemPool );
    sm_SdwRStatusDaddy_SenderPort                         .connectMemPool( sm_SdwRStatusDaddy_MemPool );
    sm_ParkStatusDaddy_SenderPort                         .connectMemPool( sm_ParkStatusDaddy_MemPool );
    sm_ParkTypeDaddy_SenderPort                           .connectMemPool( sm_ParkTypeDaddy_MemPool );
    sm_ParkTypeVariantDaddy_SenderPort                    .connectMemPool( sm_ParkTypeVariantDaddy_MemPool );
    sm_ParkModeDaddy_SenderPort                           .connectMemPool( sm_ParkModeDaddy_MemPool );
    sm_ParkSideDaddy_SenderPort                           .connectMemPool( sm_ParkSideDaddy_MemPool );
    sm_ParkFunctionIndDaddy_SenderPort                    .connectMemPool( sm_ParkFunctionIndDaddy_MemPool );
    sm_ParkQuitIndDaddy_SenderPort                        .connectMemPool( sm_ParkQuitIndDaddy_MemPool );
    sm_ParkQuitIndExtDaddy_SenderPort                     .connectMemPool( sm_ParkQuitIndExtDaddy_MemPool );
    sm_ParkRecoverIndDaddy_SenderPort                     .connectMemPool( sm_ParkRecoverIndDaddy_MemPool );
    sm_ParkDriverIndDaddy_SenderPort                      .connectMemPool( sm_ParkDriverIndDaddy_MemPool );
    sm_ParkDriverIndExtDaddy_SenderPort                   .connectMemPool( sm_ParkDriverIndExtDaddy_MemPool );
    sm_ParkOutSideAvlDaddy_SenderPort                     .connectMemPool( sm_ParkOutSideAvlDaddy_MemPool );
    sm_ParkReqReleaseBtnDaddy_SenderPort                  .connectMemPool( sm_ParkReqReleaseBtnDaddy_MemPool );
    sm_ParkAPA_ParkSpaceDaddy_SenderPort                  .connectMemPool( sm_ParkAPA_ParkSpaceDaddy_MemPool );
    sm_ParkAPA_ParkSpaceMarkDaddy_SenderPort              .connectMemPool( sm_ParkAPA_ParkSpaceMarkDaddy_MemPool );
    sm_ParkPSDirectionSelectedDaddy_SenderPort            .connectMemPool( sm_ParkPSDirectionSelectedDaddy_MemPool );
    sm_ParkParkngTypeSeldDaddy_SenderPort                 .connectMemPool( sm_ParkParkngTypeSeldDaddy_MemPool );
    sm_ParkAPAPARKMODE_SenderPort                         .connectMemPool( sm_ParkAPAPARKMODE_MemPool );
    sm_ParkSpaceDaddy_SenderPort                          .connectMemPool( sm_ParkSpaceDaddy_MemPool );
    sm_ParkFrontObjDaddy_SenderPort                       .connectMemPool( sm_ParkFrontObjDaddy_MemPool);
    sm_ParkEndPositionSearchingDaddy_SenderPort           .connectMemPool( sm_ParkEndPositionSearchingDaddy_MemPool );
    sm_ParkFinalEndPositionDaddy_SenderPort               .connectMemPool( sm_ParkFinalEndPositionDaddy_MemPool );
    sm_ParkCurMoveTargetPositionDaddy_SenderPort          .connectMemPool( sm_ParkCurMoveTargetPositionDaddy_MemPool );
    sm_ParkCarmoveNumberDaddy_SenderPort                  .connectMemPool( sm_ParkCarmoveNumberDaddy_MemPool );
    sm_ParkCartravelDistDesiredDaddy_SenderPort           .connectMemPool( sm_ParkCartravelDistDesiredDaddy_MemPool );
    sm_ParkIsLastMoveDaddy_SenderPort                     .connectMemPool( sm_ParkIsLastMoveDaddy_MemPool );
    sm_ParkRPADriverSelectedDaddy_SenderPort              .connectMemPool( sm_ParkRPADriverSelectedDaddy_MemPool);
    sm_brakePedalBeenReleasedBeforeDaddy_SenderPort       .connectMemPool( sm_brakePedalBeenReleasedBeforeDaddy_MemPool);
    sm_brakePedalAppliedDaddy_SenderPort                   .connectMemPool( sm_brakePedalAppliedDaddy_MemPool);
    sm_ParkDisp2TouchStsDaddy_SenderPort                  .connectMemPool( sm_ParkDisp2TouchStsDaddy_MemPool);
    sm_ParkDisp2TouchSlotSelectionDaddy_SenderPort        .connectMemPool( sm_ParkDisp2TouchSlotSelectionDaddy_MemPool);
    sm_ParkDispLowPolyModelStsDaddy_SenderPort            .connectMemPool( sm_ParkDispLowPolyModelStsDaddy_MemPool);
    sm_SMObstacleSwitchDaddy_Socket_SenderPort            .connectMemPool( sm_SMObstacleSwitchDaddy_Socket_MemPool );
    sm_HUDislayModeSwitchDaddy_SenderPort                 .connectMemPool( sm_HUDislayModeSwitchDaddy_MemPool );
    sm_HUCalibrationStartDaddy_SenderPort                 .connectMemPool( sm_HUCalibrationStartDaddy_MemPool );
    sm_HUDislayModeView5xDaddy_SenderPort                 .connectMemPool( sm_HUDislayModeView5xDaddy_MemPool );
    sm_HUDislayModeExpandDaddy_SenderPort                 .connectMemPool( sm_HUDislayModeExpandDaddy_MemPool );
    sm_HUImageWorkModeDaddy_SenderPort                    .connectMemPool( sm_HUImageWorkModeDaddy_MemPool );
    sm_HUTransparentModeDaddy_SenderPort                  .connectMemPool( sm_HUTransparentModeDaddy_MemPool );
    sm_HURadarWallButtonDaddy_SenderPort                  .connectMemPool( sm_HURadarWallButtonDaddy_MemPool );
    sm_HUTrailerButtonDaddy_SenderPort                    .connectMemPool( sm_HUTrailerButtonDaddy_MemPool );
    sm_HUDislayModeExpandNewDaddy_SenderPort              .connectMemPool( sm_HUDislayModeExpandNewDaddy_MemPool );
    sm_HURotateStatusDaddy_SenderPort                     .connectMemPool( sm_HURotateStatusDaddy_MemPool );
    sm_HUShoWReqDaddy_SenderPort                          .connectMemPool( sm_HUShoWReqDaddy_MemPool );
    sm_HURemoteScreenReqDaddy_SenderPort                  .connectMemPool( sm_HURemoteScreenReqDaddy_MemPool );
    sm_HUShoWSuspendDaddy_SenderPort                      .connectMemPool( sm_HUShoWSuspendDaddy_MemPool );
    sm_DriverSteeringWheelAngleDaddy_SenderPort           .connectMemPool( sm_DriverSteeringWheelAngleDaddy_MemPool );
    sm_HUselSVSModeDaddy_SenderPort                       .connectMemPool( sm_HUselSVSModeDaddy_MemPool );
    sm_HUsvsAutoCamDaddy_SenderPort                       .connectMemPool( sm_HUsvsAutoCamDaddy_MemPool );
    sm_HUsvsIntegtOpenDaddy_SenderPort                    .connectMemPool( sm_HUsvsIntegtOpenDaddy_MemPool );
    sm_HUtouchEvenTypeDaddy_SenderPort                    .connectMemPool( sm_HUtouchEvenTypeDaddy_MemPool );
    sm_HUTwoFingerTouchEvenTypeDaddy_SenderPort           .connectMemPool( sm_HUTwoFingerTouchEvenTypeDaddy_MemPool);
    sm_HUShowStsDaddy_SenderPort                          .connectMemPool( sm_HUShowStsDaddy_MemPool );
    sm_HUrestoreSVSSetDaddy_SenderPort                    .connectMemPool( sm_HUrestoreSVSSetDaddy_MemPool );
    sm_HUbasePlateReqDaddy_SenderPort                     .connectMemPool( sm_HUbasePlateReqDaddy_MemPool );
    sm_HUvehTransReqDaddy_SenderPort                      .connectMemPool( sm_HUvehTransReqDaddy_MemPool);
    sm_HUvehTransLevelDaddy_SenderPort                    .connectMemPool( sm_HUvehTransLevelDaddy_MemPool);
    sm_HUfreemodeAngleAzimuthDaddy_SenderPort             .connectMemPool( sm_HUfreemodeAngleAzimuthDaddy_MemPool);
    sm_HUfreemodeAngleElevationDaddy_SenderPort           .connectMemPool( sm_HUfreemodeAngleElevationDaddy_MemPool);
    sm_HUoverlayDistReqDaddy_SenderPort                   .connectMemPool( sm_HUoverlayDistReqDaddy_MemPool );
    sm_HUoverlayReqDaddy_SenderPort                       .connectMemPool( sm_HUoverlayReqDaddy_MemPool );
    sm_HU3DCruseDaddy_SenderPort                          .connectMemPool( sm_HU3DCruseDaddy_MemPool );
    sm_HUgestureStatusDaddy_SenderPort                    .connectMemPool( sm_HUgestureStatusDaddy_MemPool );
    sm_SVSShowReqDaddy_SenderPort                         .connectMemPool( sm_SVSShowReqDaddy_MemPool );
    sm_SVSCurrentViewDaddy_SenderPort                     .connectMemPool( sm_SVSCurrentViewDaddy_MemPool );
    sm_SVSIntegtOpenStsDaddy_SenderPort                   .connectMemPool( sm_SVSIntegtOpenStsDaddy_MemPool );
    sm_SVSAutoCamStsDaddy_SenderPort                      .connectMemPool( sm_SVSAutoCamStsDaddy_MemPool );
    sm_SVSViewModeStsDaddy_SenderPort                     .connectMemPool( sm_SVSViewModeStsDaddy_MemPool );
    sm_SVSResolutionDaddy_SenderPort                      .connectMemPool( sm_SVSResolutionDaddy_MemPool );
    sm_SVSOverlayDistStsDaddy_SenderPort                  .connectMemPool( sm_SVSOverlayDistStsDaddy_MemPool );
    sm_SVSDynOverlayStsDaddy_SenderPort                   .connectMemPool( sm_SVSDynOverlayStsDaddy_MemPool );
    sm_SVSBasePlateStsDaddy_SenderPort                    .connectMemPool( sm_SVSBasePlateStsDaddy_MemPool );
    sm_SVSVehTransStsDaddy_SenderPort                     .connectMemPool( sm_SVSVehTransStsDaddy_MemPool );
    sm_SVSVehTransStsInternalDaddy_SenderPort             .connectMemPool( sm_SVSVehTransStsInternalDaddy_MemPool );
    sm_SVS3DCruStsDaddy_SenderPort                        .connectMemPool( sm_SVS3DCruStsDaddy_MemPool );
    sm_SVSzoomStsDaddy_SenderPort                         .connectMemPool( sm_SVSzoomStsDaddy_MemPool );
    sm_SVSFrViewStsDaddy_SenderPort                       .connectMemPool( sm_SVSFrViewStsDaddy_MemPool );
    sm_SVSLeViewStsDaddy_SenderPort                       .connectMemPool( sm_SVSLeViewStsDaddy_MemPool );
    sm_SVSReViewStsDaddy_SenderPort                       .connectMemPool( sm_SVSReViewStsDaddy_MemPool );
    sm_SVSRiViewStsDaddy_SenderPort                       .connectMemPool( sm_SVSRiViewStsDaddy_MemPool );
    sm_CpcOverlaySwitchDaddy_SenderPort                   .connectMemPool( sm_CpcOverlaySwitchDaddy_MemPool );
    sm_SwVersionShowSwitchDaddy_SenderPort                .connectMemPool( sm_SwVersionShowSwitchDaddy_MemPool );
    sm_SWInfoDaddy_SenderPort                             .connectMemPool( sm_SWInfoDaddy_MemPool );
    sm_DoorLockSts_SenderPort                             .connectMemPool( sm_DoorLockSts_MemPool );
    sm_TimeShow_SenderPort                                .connectMemPool( sm_TimeShow_MemPool );
    
    sm_EnvProcSoundUnitClosestObstacleDaddy_SenderPort    .connectMemPool( sm_ClosestObstacleDaddy_MemPool );
    sm_TrailerSvs_SenderPort                              .connectMemPool( sm_TrailerSvs_MemPool );
    sm_threatDetected_Socket_SenderPort                   .connectMemPool( sm_threatDetected_Socket_MemPool );
    sm_ViewModeState_SenderPort                           .connectMemPool( sm_Debug_ViewModeState_MemPool );
    sm_systemState_SenderPort                             .connectMemPool( sm_systemState_MemPool );
    sm_userMovementIntention_Socket_SenderPort            .connectMemPool( sm_userMovementIntentionDaddy_Socket_MemPool );
    sm_LSMG_VehicleMovement_Socket_SenderPort             .connectMemPool( sm_LSMG_VehicleMovement_Socket_MemPool );
    sm_VehicleAlpha_Socket_SenderPort                     .connectMemPool( sm_VehicleAlpha_Socket_MemPool );
    sm_VehicleDiffuseColor_Socket_SenderPort              .connectMemPool( sm_VehicleDiffuseColor_Socket_MemPool );
    sm_VehicleDiffuseColorIndex_SenderPort                .connectMemPool( sm_VehicleDiffuseColorIndex_MemPool );
    sm_VehicleSpecColor1_Socket_SenderPort                .connectMemPool( sm_VehicleSpecColor1_Socket_MemPool );
    sm_VehicleSpecColor2_Socket_SenderPort                .connectMemPool( sm_VehicleSpecColor2_Socket_MemPool );
    sm_VehicleModelParam_Socket_SenderPort                .connectMemPool( sm_VehicleModelParam_Socket_MemPool );
    sm_DistanceToStopDaddy_SenderPort                     .connectMemPool( sm_DistanceToStopDaddy_MemPool );
    sm_StopLineLocationDaddy_SenderPort                   .connectMemPool( sm_StopLineLocationDaddy_MemPool );
    sm_AutomaticParkingDaddy_SenderPort                   .connectMemPool( sm_AutomaticParkingDaddy_MemPool );
    sm_LMStatusDaddy_SenderPort                           .connectMemPool( sm_LMStatusDaddy_MemPool);
    sm_AlsDippedBeamState_SenderPort                      .connectMemPool( sm_AlsDippedBeamState_MemPool );
    sm_PIVI_ManualVideoSetupReq_SenderPort                .connectMemPool( sm_PIVI_ManualVideoSetupReq_MemPool );
    sm_PIVI_DayNightThemeReq_SenderPort                   .connectMemPool( sm_PIVI_DayNightThemeReq_MemPool );
    sm_PIVI_ViewBufferStatusACK_SenderPort                .connectMemPool( sm_PIVI_ViewBufferStatusACK_MemPool );
    sm_NFSM_MovementDirectionUpdate_SenderPort            .connectMemPool( sm_NFSM_MovementDirectionUpdate_MemPool );
    sm_NFSM_ViewBufferStatus_SenderPort                   .connectMemPool( sm_NFSM_ViewBufferStatus_MemPool );
    sm_IsOffroadDaddy_SenderPort                          .connectMemPool( sm_IsOffroadDaddy_MemPool );
    sm_CarModeDaddy_SenderPort                            .connectMemPool( sm_CarModeDaddy_MemPool );
    sm_PowerModeDaddy_SenderPort                          .connectMemPool( sm_PowerModeDaddy_MemPool);
    sm_LSAEBVmcOpModeDaddy_SenderPort                     .connectMemPool( sm_LSAEBVmcOpModeDaddy_MemPool );
    sm_CamCalibrationStatusDaddy_SenderPort               .connectMemPool( sm_CamCalibrationStatusDaddy_MemPool );
    sm_CpcNorminalCalibDaddy_SenderPort                   .connectMemPool( sm_CpcNorminalCalibDaddy_MemPool);
    sm_FCTALeftDaddy_SenderPort                           .connectMemPool( sm_FCTALeftDaddy_MemPool );
    sm_FCTARightDaddy_SenderPort                          .connectMemPool( sm_FCTARightDaddy_MemPool);
    sm_RCTALeftDaddy_SenderPort                           .connectMemPool( sm_RCTALeftDaddy_MemPool);
    sm_RCTARightDaddy_SenderPort                          .connectMemPool( sm_RCTARightDaddy_MemPool);
    sm_VRSwitchSVMDaddy_SenderPort                        .connectMemPool( sm_VRSwitchSVMDaddy_MemPool);
    sm_VRSwitchFailStDaddy_SenderPort                     .connectMemPool( sm_VRSwitchFailStDaddy_MemPool);
    sm_FreeParkingParkableDaddy_SenderPort                .connectMemPool( sm_FreeParkingParkableDaddy_MemPool);
    sm_FCP_SVMButtonPressedDaddy_SenderPort               .connectMemPool( sm_FCP_SVMButtonPressedDaddy_MemPool);

    // Start of BYD
    sm_SVSViewStateDaddy_SenderPort                       .connectMemPool( sm_SVSViewStateDaddy_MemPool);
    sm_SVSWorkModeDaddy_SenderPort                        .connectMemPool( smSVSWorkModeDaddy_MemPool);
    sm_SVSOnOffStateDaddy_SenderPort                      .connectMemPool( sm_SVSOnOffStateDaddy_MemPool);
    sm_SVSvidoutModeDaddy_SenderPort                      .connectMemPool( sm_SVSvidoutModeDaddy_MemPool);
    sm_SVSRotateStatusDaddy_SenderPort                    .connectMemPool( sm_SVSRotateStatusDaddy_MemPool);
    sm_dayNightThemeDaddy_SenderPort                      .connectMemPool( sm_dayNightThemeDaddy_MemPool);
    sm_SVSTrajCfgStateDaddy_SenderPort                    .connectMemPool( sm_SVSTrajCfgStateDaddy_MemPool);
    sm_SVSLVDSvidOutModeDaddy_SenderPort                  .connectMemPool( sm_SVSLVDSvidOutModeDaddy_MemPool);
    sm_SVSImageConfigDaddy_SenderPort                     .connectMemPool( sm_SVSImageConfigDaddy_MemPool);
    sm_SVSCarBodyDaddy_SenderPort                         .connectMemPool( sm_SVSCarBodyDaddy_MemPool);
    sm_SVSExpandedViewStateDaddy_SenderPort               .connectMemPool( sm_SVSExpandedViewStateDaddy_MemPool);
    sm_SVSNewExpandedViewStateDaddy_SenderPort            .connectMemPool( sm_SVSNewExpandedViewStateDaddy_MemPool);
    sm_SVSVehColorAckDaddy_SenderPort                     .connectMemPool( sm_SVSVehColorAckDaddy_MemPool);
    sm_SVSUnavlMsgsDaddy_SenderPort                       .connectMemPool( sm_SVSUnavlMsgsDaddy_MemPool);
    sm_customUltrasonicDataDaddySenderPort                .connectMemPool( sm_customUltrasonicDataDaddy_MemPool);
    sm_tileSplineInterpolateArrayDaddySenderPort          .connectMemPool( sm_tileSplineInterpolateArrayDaddy_MemPool);
    // End of BYD

    // Rx from FlexRay
    sm_FlexRayRx_LSMGActivationSetReq_SenderPort          .connectMemPool( sm_FlexRayRx_LSMGActivationSetReq_MemPool          );
    // Rx PDM
    sm_PdmR5ToLinux_3DCruStsDaddy_SenderPort              .connectMemPool( sm_PdmR5ToLinux_3DCruStsDaddy_MemPool              );
    sm_PdmR5ToLinux_IntegtOpenStsDaddy_SenderPort         .connectMemPool( sm_PdmR5ToLinux_IntegtOpenStsDaddy_MemPool         );
    sm_PdmR5ToLinux_AutoCamStsDaddy_SenderPort            .connectMemPool( sm_PdmR5ToLinux_AutoCamStsDaddy_MemPool            );
    sm_PdmR5ToLinux_DynOverlayStsDaddy_SenderPort         .connectMemPool( sm_PdmR5ToLinux_DynOverlayStsDaddy_MemPool         );
    sm_PdmR5ToLinux_OverlayDistStsDaddy_SenderPort        .connectMemPool( sm_PdmR5ToLinux_OverlayDistStsDaddy_MemPool        );
    sm_PdmR5ToLinux_BasePlateStsDaddy_SenderPort          .connectMemPool( sm_PdmR5ToLinux_BasePlateStsDaddy_MemPool          );
    sm_PdmR5ToLinux_VehTransStsDaddy_SenderPort           .connectMemPool( sm_PdmR5ToLinux_VehTransStsDaddy_MemPool           );
    sm_PdmR5ToLinux_VehColorStsDaddy_SenderPort           .connectMemPool( sm_PdmR5ToLinux_VehColorStsDaddy_MemPool           );

    // Tx PDM
    sm_PdmLinuxToR5_3DCruStsDaddy_SenderPort              .connectMemPool( sm_PdmLinuxToR5_3DCruStsDaddy_MemPool              );
    sm_PdmLinuxToR5_IntegtOpenStsDaddy_SenderPort         .connectMemPool( sm_PdmLinuxToR5_IntegtOpenStsDaddy_MemPool         );
    sm_PdmLinuxToR5_AutoCamStsDaddy_SenderPort            .connectMemPool( sm_PdmLinuxToR5_AutoCamStsDaddy_MemPool            );
    sm_PdmLinuxToR5_DynOverlayStsDaddy_SenderPort         .connectMemPool( sm_PdmLinuxToR5_DynOverlayStsDaddy_MemPool         );
    sm_PdmLinuxToR5_OverlayDistStsDaddy_SenderPort        .connectMemPool( sm_PdmLinuxToR5_OverlayDistStsDaddy_MemPool        );
    sm_PdmLinuxToR5_BasePlateStsDaddy_SenderPort          .connectMemPool( sm_PdmLinuxToR5_BasePlateStsDaddy_MemPool          );
    sm_PdmLinuxToR5_VehTransStsDaddy_SenderPort           .connectMemPool( sm_PdmLinuxToR5_VehTransStsDaddy_MemPool           );
    sm_PdmLinuxToR5_VehColorStsDaddy_SenderPort           .connectMemPool( sm_PdmLinuxToR5_VehColorStsDaddy_MemPool           );

    // CCF
    sm_CCF_SenderPort                                     .connectMemPool( sm_CCF_MemPool );

    // LCF
    sm_LCF_SenderPort                                     .connectMemPool( sm_LCF_MemPool );

    // PDM Variant
    sm_Variant_SenderPort                                 .connectMemPool( sm_Variant_MemPool );

    // Customer specific vehicle lights
    sm_CustomVehicleLights_SenderPort                     .connectMemPool( sm_CustomVehicleLights_MemPool );

    //! Internal View Buffer Status
    CustomDaddyPorts::sm_ViewBufferStatus_SenderPort      .connectMemPool( sm_ViewBufferStatus_MemPool    );

    //! GBC Vehicle Transparency
    sm_GbcVehicleTransparency_SenderPort                  .connectMemPool( sm_GbcVehicleTransparency_MemPool );
    sm_GbcWheelTransparency_SenderPort                    .connectMemPool( sm_GbcWheelTransparency_MemPool );

    //! View Animation
    sm_animationDaddy_SenderPort                          .connectMemPool( sm_animationDaddy_MemPool );

    //! View Animation Completed
    sm_viewAnimationCompleted_SenderPort                  .connectMemPool( sm_viewAnimationCompleted_MemPool );

    sm_diagRoutine_SenderPort                             .connectMemPool( sm_diagRoutine_MemPool );

    //! HMI
    sm_HmiData_SenderPort                                 .connectMemPool( sm_HmiData_MemPool );

    //! Simulate the HU command
    sm_HUCameraCommandsDaddySenderPort                    .connectMemPool( sm_HUCameraCommandsDaddyMemPool );
    sm_HemisphereParameterData_SenderPort                 .connectMemPool( sm_HemisphereParameterData_MemPool );
    sm_groundplane_triangle_SenderPort                    .connectMemPool( sm_groundplane_triangle_MemPool );
    //! free view mode camera position
    sm_CameraPositionDaddySenderPort                      .connectMemPool( sm_CameraPositionDaddyMemPool );

    //! degradation FIDs
    sm_degradationFid_SenderPort                          .connectMemPool( sm_degradationFid_MemPool );

    sm_impostorTransparencySenderPort                     .connectMemPool(sm_impostorTransparencyMemPool);

    //! SVS displayed view
    sm_SVSDisplayedViewDaddy_SenderPort                   .connectMemPool( sm_SVSDisplayedViewDaddy_MemPool );

    //! SVS free view mode status
    sm_SVSFreeModeStDaddy_SenderPort                      .connectMemPool( sm_SVSFreeModeStDaddy_MemPool );

    sm_SVSParkUISpotDataDaddy_SenderPort                  .connectMemPool( sm_SVSParkUISpotDataDaddy_MemPool );

    sm_SVSParkConfirmInterfaceDataDaddy_SenderPort        .connectMemPool( sm_SVSParkConfirmInterfaceDataDaddy_MemPool );

    sm_SVSParkUIAvailableParkingSlotDaddy_SenderPort      .connectMemPool( sm_SVSParkUIAvailableParkingSlotDaddy_MemPool );
    //! VHM raw data
    sm_customVhmAbstRawDataDaddy_SenderPort               .connectMemPool( sm_customVhmAbstRawDataDaddy_MemPool );

    sm_freeparkingDataSenderPort                          .connectMemPool(sm_freeparkingDataMemPool);

    sm_freeparkingRectInfoSenderPort                      .connectMemPool(sm_freeparkingRectInfoMemPool);

    sm_freeparkingSocketSenderPort                        .connectMemPool(sm_freeparkingSocketMemPool);

    sm_ParkFreeParkingActiveDaddy_SenderPort              .connectMemPool( sm_ParkFreeParkingActiveDaddy_MemPool );
    sm_StartPauseConfirmButtonPressDaddy_SenderPort       .connectMemPool(sm_StartPauseConfirmButtonPressDaddy_MemPool);
    sm_ContinueButtonPressDaddy_SenderPort                .connectMemPool(sm_ContinueButtonPressDaddy_MemPool);
    sm_QuitButtonPressDaddy_SenderPort                    .connectMemPool(sm_QuitButtonPressDaddy_MemPool);
    sm_SuspendButtonPressDaddy_SenderPort                 .connectMemPool(sm_SuspendButtonPressDaddy_MemPool);
    sm_parkoutLeftButtonPressDaddy_SenderPort             .connectMemPool(sm_parkoutLeftButtonPressDaddy_MemPool);
    sm_parkoutRightButtonPressDaddy_SenderPort            .connectMemPool(sm_parkoutRightButtonPressDaddy_MemPool);
    sm_FreeParkingButtonPressDaddy_SenderPort             .connectMemPool(sm_FreeParkingButtonPressDaddy_MemPool);

    sm_FreeParkingConfirmButtonStDaddy_SenderPort         .connectMemPool( sm_FreeParkingConfirmButtonStDaddy_MemPool );

    //!cpc Extrinsic Data
    // sm_cpcCornerDetectionDataDaddy_SenderPort             .connectMemPool( sm_cpcCornerDetectionDataDaddy_MemPool );

    //!cpc status to cpc debug overlay
    // sm_cpcStatusDaddy_SenderPort                          .connectMemPool( sm_cpcStatusDaddy_MemPool );

    //! CPC
    sm_cpcToCpcWrapper_SenderPort                         .connectMemPool( sm_cpcToCpcWrapper_MemPool );
    sm_cpcWrapperToCpc_SenderPort                         .connectMemPool( sm_cpcWrapperToCpc_MemPool );
    sm_cpcToSvsOverlay_SenderPort                         .connectMemPool( sm_cpcToSvsOverlay_MemPool );
    sm_svsOverlayToCpc_SenderPort                         .connectMemPool( sm_svsOverlayToCpc_MemPool );

    //! dynamic gear
    sm_dynamicGearStatus_SenderPort                       .connectMemPool(sm_dynamicGearStatus_MemPool);

    //! notice rolling
    sm_noticeRolling_SenderPort                           .connectMemPool(sm_noticeRolling_MemPool);
    sm_planViewEnlargeStatus_SenderPort                   .connectMemPool(sm_planViewEnlargeStatus_MemPool);
    sm_sideViewEnableStatus_SenderPort                    .connectMemPool(sm_sideViewEnableStatus_MemPool);
    sm_topViewEnableStatus_SenderPort                     .connectMemPool(sm_topViewEnableStatus_MemPool);
    sm_camPosAxis2Rq_SenderPort                           .connectMemPool(sm_camPosAxis2Rq_MemPool);

    sm_componentTestSwitch_SenderPort                     .connectMemPool(sm_componentTestSwitch_MemPool);

    //! Virtual Reality
    sm_ParkSlotRefinedDaddy_SenderPort                    .connectMemPool( sm_ParkSlotRefinedDaddy_MemPool );
    sm_ParkSlotDaddy_SenderPort                           .connectMemPool( sm_ParkSlotDaddy_MemPool );

    //! Fusion object
    sm_fusionObject_SenderPort                             .connectMemPool(sm_fusionObject_MemPool);

    //! Sit Ocp
    sm_sitOcp_SenderPort                                   .connectMemPool(sm_sitOcp_MemPool);

    //! Pedestrian
    sm_pedestrianObj_SenderPort                            .connectMemPool(sm_pedestrianObj_MemPool);

    sm_ParkhmiToSvs_SenderPort                             .connectMemPool(sm_ParkhmiToSvs_MemPool);
    sm_SvsToParkhmi_SenderPort                             .connectMemPool(sm_SvsToParkhmi_MemPool);
    sm_FreeParkingSlot_SenderPort                          .connectMemPool(sm_FreeParkingSlot_MemPool);
    sm_FreeParkingSlotInternal_SenderPort                  .connectMemPool(sm_FreeParkingSlotInternal_MemPool);
    sm_SlotSelectedId_SenderPort                           .connectMemPool(sm_SlotSelectedId_MemPool);
    sm_ParkoutSelectedDirection_SenderPort                 .connectMemPool(sm_ParkoutSelectedDirection_MemPool);

    sm_AirSuspensionHeight_SenderPort                      .connectMemPool(sm_AirSuspensionHeight_MemPool);
    sm_ZoomLevel_SenderPort                                .connectMemPool(sm_ZoomLevel_MemPool);
    sm_ZoomLevelIPC_SenderPort                             .connectMemPool(sm_ZoomLevelIPC_MemPool);
    sm_RemoveDistortion_SenderPort                         .connectMemPool(sm_RemoveDistortion_MemPool);
    sm_BirdEyeViewSwitch_SenderPort                        .connectMemPool(sm_BirdEyeViewSwitch_MemPool);
    sm_SurroundViewRotateAngle_SenderPort                  .connectMemPool(sm_SurroundViewRotateAngle_MemPool);
    sm_CrabGuideline_SenderPort                            .connectMemPool(sm_CrabGuideline_MemPool);
    sm_GoldenEmblem_SenderPort                             .connectMemPool(sm_GoldenEmblem_MemPool);

    sm_customerMemoryPoolsAreConnected = true;
  }
}


void CustomDaddyPorts::initPorts()
{
  //! init base ports first
  pc::daddy::BaseDaddyPorts::initPorts();

  if (false == sm_customerMemoryPoolsHaveInitialData)
  {
    //!then init custom ports

    // Initially, view buffer is not available - notify SM
    if ( cc::daddy::CustomDaddyPorts::sm_ViewBufferStatus_SenderPort.isConnected() )
    {
        cc::daddy::NFSM_ViewBufferStatus_t& l_container = cc::daddy::CustomDaddyPorts::sm_ViewBufferStatus_SenderPort.reserve();
        l_container.m_Data.m_ViewBufferStatus_u8 = 0u;
        cc::daddy::CustomDaddyPorts::sm_ViewBufferStatus_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_HURemoteScreenReqDaddy_SenderPort.isConnected())
    {
        auto& l_container = cc::daddy::CustomDaddyPorts::sm_HURemoteScreenReqDaddy_SenderPort.reserve();
        l_container.m_Data = 0u;
        cc::daddy::CustomDaddyPorts::sm_HURemoteScreenReqDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ParkStatusDaddy_SenderPort.isConnected())
    {
        cc::daddy::ParkStatusDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkStatusDaddy_SenderPort.reserve();
        l_container.m_Data = cc::target::common::PARK_Off;
        cc::daddy::CustomDaddyPorts::sm_ParkStatusDaddy_SenderPort.deliver();
    }

    if (BaseDaddyPorts::sm_DoorOpenDaddySenderPort.isConnected())
    {
      using namespace pc::daddy;
      DoorStateDaddy& l_doorStates = BaseDaddyPorts::sm_DoorOpenDaddySenderPort.reserve();
      l_doorStates.m_Data[CARDOOR_FRONT_LEFT]  = CARDOORSTATE_CLOSED;
      l_doorStates.m_Data[CARDOOR_FRONT_RIGHT] = CARDOORSTATE_CLOSED;
      l_doorStates.m_Data[CARDOOR_REAR_LEFT]   = CARDOORSTATE_CLOSED;
      l_doorStates.m_Data[CARDOOR_REAR_RIGHT]  = CARDOORSTATE_CLOSED;
      l_doorStates.m_Data[CARDOOR_TRUNK]       = CARDOORSTATE_CLOSED;
      l_doorStates.m_Data[CARDOOR_HOOD]        = CARDOORSTATE_CLOSED;
      l_doorStates.m_Data[CARDOOR_SPOILER]     = CARDOORSTATE_CLOSED;
      BaseDaddyPorts::sm_DoorOpenDaddySenderPort.deliver();
    }

    if (BaseDaddyPorts::sm_SideMirrorFlapDaddySenderPort.isConnected())
    {
      using namespace pc::daddy;
      MirrorStateDaddy& l_mirrorState = BaseDaddyPorts::sm_SideMirrorFlapDaddySenderPort.reserve();
      l_mirrorState.m_Data[SIDEMIRROR_LEFT] = MIRRORSTATE_NOT_FLAPPED;
      l_mirrorState.m_Data[SIDEMIRROR_RIGHT] = MIRRORSTATE_NOT_FLAPPED;
      BaseDaddyPorts::sm_SideMirrorFlapDaddySenderPort.deliver();
    }

    if (CustomDaddyPorts::sm_HUTransparentModeDaddy_SenderPort.isConnected())
    {
      auto& container = CustomDaddyPorts::sm_HUTransparentModeDaddy_SenderPort.reserve();
      container.m_Data = cc::target::common::ETransparentMode::INVAILD;
      CustomDaddyPorts::sm_HUTransparentModeDaddy_SenderPort.deliver();
    }

    if (CustomDaddyPorts::sm_HUDislayModeView5xDaddy_SenderPort.isConnected())
    {
      auto& container = CustomDaddyPorts::sm_HUDislayModeView5xDaddy_SenderPort.reserve();
      container.m_Data = cc::target::common::EHUDisplayMode5x::INVALID;
      CustomDaddyPorts::sm_HUDislayModeView5xDaddy_SenderPort.deliver();
    }

    if (CustomDaddyPorts::sm_planViewEnlargeStatus_SenderPort.isConnected())
    {
      auto& container = CustomDaddyPorts::sm_planViewEnlargeStatus_SenderPort.reserve();
      container.m_Data = cc::daddy::PlanViewEnlargeStatus::NO_ENLARGE;
      CustomDaddyPorts::sm_planViewEnlargeStatus_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_SdwStatusDaddy_SenderPort.isConnected())
    {
      cc::daddy::SdwStatusDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_SdwStatusDaddy_SenderPort.reserve();
      l_container.m_Data = cc::target::common::ESdwStatus::SDWSTS_Standby;
      cc::daddy::CustomDaddyPorts::sm_SdwStatusDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_PasStatusDaddy_SenderPort.isConnected())
    {
      cc::daddy::PasStatusDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_PasStatusDaddy_SenderPort.reserve();
      l_container.m_Data = cc::target::common::EPasStatus::PAS_FrontRearActive;
      cc::daddy::CustomDaddyPorts::sm_PasStatusDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_SVSRotateStatusDaddy_SenderPort.isConnected())
    {
      cc::daddy::SVSRotateStatusDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_SVSRotateStatusDaddy_SenderPort.reserve();
      l_container.m_Data = 1u; // horizontal
      cc::daddy::CustomDaddyPorts::sm_SVSRotateStatusDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_SVSVehColorAckDaddy_SenderPort.isConnected())
    {
      cc::daddy::SVSVehColorAckDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_SVSVehColorAckDaddy_SenderPort.reserve();
      l_container.m_Data = 2u; // TIME_GRAY
      cc::daddy::CustomDaddyPorts::sm_SVSVehColorAckDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_HUvehTransReqDaddy_SenderPort.isConnected())
    {
      cc::daddy::HUvehTransReq_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUvehTransReqDaddy_SenderPort.reserve();
      l_container.m_Data = 0u;
      cc::daddy::CustomDaddyPorts::sm_HUvehTransReqDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_HUvehTransLevelDaddy_SenderPort.isConnected())
    {
      cc::daddy::HUvehTransLevel_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUvehTransLevelDaddy_SenderPort.reserve();
      l_container.m_Data = 0u;
      cc::daddy::CustomDaddyPorts::sm_HUvehTransLevelDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleAzimuthDaddy_SenderPort.isConnected())
    {
      cc::daddy::HUFreemodeAngleDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleAzimuthDaddy_SenderPort.reserve();
      l_container.m_Data = 0u;
      cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleAzimuthDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleElevationDaddy_SenderPort.isConnected())
    {
      cc::daddy::HUFreemodeAngleDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleElevationDaddy_SenderPort.reserve();
      l_container.m_Data = 0u;
      cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleElevationDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_componentTestSwitch_SenderPort.isConnected())
    {
      cc::daddy::ComponentTestSwitchDaddy& l_container = cc::daddy::CustomDaddyPorts::sm_componentTestSwitch_SenderPort.reserve();
      l_container.m_Data = TEST_NONE;
      cc::daddy::CustomDaddyPorts::sm_componentTestSwitch_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ParkParkngTypeSeldDaddy_SenderPort.isConnected())
    {
      auto& container = cc::daddy::CustomDaddyPorts::sm_ParkParkngTypeSeldDaddy_SenderPort.reserve();
      container.m_Data = cc::target::common::EParkngTypeSeld_::PARKING_NONE;
      cc::daddy::CustomDaddyPorts::sm_ParkParkngTypeSeldDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ParkAPAPARKMODE_SenderPort.isConnected())
    {
      auto& container = cc::daddy::CustomDaddyPorts::sm_ParkAPAPARKMODE_SenderPort.reserve();
      container.m_Data = cc::target::common::EAPAPARKMODE_::APAPARKMODE_IDLE;
      cc::daddy::CustomDaddyPorts::sm_ParkAPAPARKMODE_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ParkFreeParkingActiveDaddy_SenderPort.isConnected())
    {
      auto& container = cc::daddy::CustomDaddyPorts::sm_ParkFreeParkingActiveDaddy_SenderPort.reserve();
      container.m_Data = false;
      cc::daddy::CustomDaddyPorts::sm_ParkFreeParkingActiveDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_FreeParkingButtonPressDaddy_SenderPort.isConnected())
    {
      auto& container = cc::daddy::CustomDaddyPorts::sm_FreeParkingButtonPressDaddy_SenderPort.reserve();
      container.m_Data = false;
      cc::daddy::CustomDaddyPorts::sm_FreeParkingButtonPressDaddy_SenderPort.deliver();
    }

    if (pc::daddy::BaseDaddyPorts::sm_DrivingDirSenderPort.isConnected())
    {
      auto& container = pc::daddy::BaseDaddyPorts::sm_DrivingDirSenderPort.reserve();
      container.m_Data = static_cast<vfc::int32_t>(pc::daddy::EVehMovingDirection::UNKNOWN);
      pc::daddy::BaseDaddyPorts::sm_DrivingDirSenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ParkFinalEndPositionDaddy_SenderPort.isConnected())
    {
      auto& container = cc::daddy::CustomDaddyPorts::sm_ParkFinalEndPositionDaddy_SenderPort.reserve();
      container.m_Data = {0, 0, 0};
      cc::daddy::CustomDaddyPorts::sm_ParkFinalEndPositionDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_dayNightThemeDaddy_SenderPort.isConnected())
    {
      auto& container = cc::daddy::CustomDaddyPorts::sm_dayNightThemeDaddy_SenderPort.reserve();
      container.m_Data = cc::target::common::EThemeTypeDayNight::ETHEME_TYPE_DAYNIGHT_NIGHT;
      cc::daddy::CustomDaddyPorts::sm_dayNightThemeDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ParkDriverIndDaddy_SenderPort.isConnected())
    {
      auto& container = cc::daddy::CustomDaddyPorts::sm_ParkDriverIndDaddy_SenderPort.reserve();
      container.m_Data = cc::target::common::EPARKDriverIndR2L::PARKDRV_NoRequest;
      cc::daddy::CustomDaddyPorts::sm_ParkDriverIndDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ParkDriverIndExtDaddy_SenderPort.isConnected())
    {
      auto& container = cc::daddy::CustomDaddyPorts::sm_ParkDriverIndExtDaddy_SenderPort.reserve();
      container.m_Data = cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_NoRequest;
      cc::daddy::CustomDaddyPorts::sm_ParkDriverIndExtDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ParkRecoverIndDaddy_SenderPort.isConnected())
    {
      auto& container = cc::daddy::CustomDaddyPorts::sm_ParkRecoverIndDaddy_SenderPort.reserve();
      container.m_Data = cc::target::common::EPARKRecoverIndR2L::PARKREC_NoPrompt;
      cc::daddy::CustomDaddyPorts::sm_ParkRecoverIndDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ParkPSDirectionSelectedDaddy_SenderPort.isConnected())
    {
      auto& container = cc::daddy::CustomDaddyPorts::sm_ParkPSDirectionSelectedDaddy_SenderPort.reserve();
      container.m_Data = cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBwdIn_enm;
      cc::daddy::CustomDaddyPorts::sm_ParkPSDirectionSelectedDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ParkDriverIndSearchDaddy_SenderPort.isConnected())
    {
      auto& container = cc::daddy::CustomDaddyPorts::sm_ParkDriverIndSearchDaddy_SenderPort.reserve();
      container.m_Data = cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_NoRequest;
      cc::daddy::CustomDaddyPorts::sm_ParkDriverIndSearchDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ParkQuitIndDaddy_SenderPort.isConnected())
    {
      auto& container = cc::daddy::CustomDaddyPorts::sm_ParkQuitIndDaddy_SenderPort.reserve();
      container.m_Data = cc::target::common::EPARKQuitIndR2L::PARKQUIT_None;
      cc::daddy::CustomDaddyPorts::sm_ParkQuitIndDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ParkRPAAvaliableDaddy_SenderPort.isConnected())
    {
      auto& container = cc::daddy::CustomDaddyPorts::sm_ParkRPAAvaliableDaddy_SenderPort.reserve();
      container.m_Data = cc::target::common::RPAAvailable::RPA_NotAvailable;
      cc::daddy::CustomDaddyPorts::sm_ParkRPAAvaliableDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_noticeRolling_SenderPort.isConnected())
    {
      auto& container = cc::daddy::CustomDaddyPorts::sm_noticeRolling_SenderPort.reserve();
      container.m_Data = false;
      cc::daddy::CustomDaddyPorts::sm_noticeRolling_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.isConnected())
    {
      auto& l_rData = cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.reserve();
      l_rData.m_Data.m_huX = 0u;
      l_rData.m_Data.m_huY = 0u;
      cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_SenderPort.isConnected())
    {
      auto& l_rData = cc::daddy::CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_SenderPort.reserve();
      l_rData.m_Data = 0u;
      cc::daddy::CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_HUTwoFingerTouchEvenTypeDaddy_SenderPort.isConnected())
    {
      auto& l_rData = cc::daddy::CustomDaddyPorts::sm_HUTwoFingerTouchEvenTypeDaddy_SenderPort.reserve();
      l_rData.m_Data = 0u;
      cc::daddy::CustomDaddyPorts::sm_HUTwoFingerTouchEvenTypeDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ParkCartravelDistDesiredDaddy_SenderPort.isConnected())
    {
      auto& container = cc::daddy::CustomDaddyPorts::sm_ParkCartravelDistDesiredDaddy_SenderPort.reserve();
      container.m_Data = 0;
      cc::daddy::CustomDaddyPorts::sm_ParkCartravelDistDesiredDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ParkCarmoveNumberDaddy_SenderPort.isConnected())
    {
      auto& container = cc::daddy::CustomDaddyPorts::sm_ParkCarmoveNumberDaddy_SenderPort.reserve();
      container.m_Data = 0;
      cc::daddy::CustomDaddyPorts::sm_ParkCarmoveNumberDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ParkQuitIndExtDaddy_SenderPort.isConnected())
    {
      auto& container = cc::daddy::CustomDaddyPorts::sm_ParkQuitIndExtDaddy_SenderPort.reserve();
      container.m_Data =cc::target::common::EPARKQuitIndR2LExt::PARKQUITExt_Nofault;
      cc::daddy::CustomDaddyPorts::sm_ParkQuitIndExtDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_FreeParkingParkableDaddy_SenderPort.isConnected())
    {
      cc::daddy::FreeparkingParkable_t& l_container = cc::daddy::CustomDaddyPorts::sm_FreeParkingParkableDaddy_SenderPort.reserve();
      l_container.m_Data = false;
      cc::daddy::CustomDaddyPorts::sm_FreeParkingParkableDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_HURotateStatusDaddy_SenderPort.isConnected())
    {
        auto& container = cc::daddy::CustomDaddyPorts::sm_HURotateStatusDaddy_SenderPort.reserve();
        container.m_Data = 1u;  // horizontal
        cc::daddy::CustomDaddyPorts::sm_HURotateStatusDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_HUDislayModeExpandNewDaddy_SenderPort.isConnected())
    {
        auto& container = cc::daddy::CustomDaddyPorts::sm_HUDislayModeExpandNewDaddy_SenderPort.reserve();
        container.m_Data = 0u;
        cc::daddy::CustomDaddyPorts::sm_HUDislayModeExpandNewDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_StartPauseConfirmButtonPressDaddy_SenderPort.isConnected())
    {
        auto& container = cc::daddy::CustomDaddyPorts::sm_StartPauseConfirmButtonPressDaddy_SenderPort.reserve();
        container.m_Data = false;
        cc::daddy::CustomDaddyPorts::sm_StartPauseConfirmButtonPressDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ContinueButtonPressDaddy_SenderPort.isConnected())
    {
        auto& container = cc::daddy::CustomDaddyPorts::sm_ContinueButtonPressDaddy_SenderPort.reserve();
        container.m_Data = false;
        cc::daddy::CustomDaddyPorts::sm_ContinueButtonPressDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_QuitButtonPressDaddy_SenderPort.isConnected())
    {
        auto& container = cc::daddy::CustomDaddyPorts::sm_QuitButtonPressDaddy_SenderPort.reserve();
        container.m_Data = false;
        cc::daddy::CustomDaddyPorts::sm_QuitButtonPressDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_SuspendButtonPressDaddy_SenderPort.isConnected())
    {
        auto& container = cc::daddy::CustomDaddyPorts::sm_SuspendButtonPressDaddy_SenderPort.reserve();
        container.m_Data = false;
        cc::daddy::CustomDaddyPorts::sm_SuspendButtonPressDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_parkoutLeftButtonPressDaddy_SenderPort.isConnected())
    {
        auto& container = cc::daddy::CustomDaddyPorts::sm_parkoutLeftButtonPressDaddy_SenderPort.reserve();
        container.m_Data = false;
        cc::daddy::CustomDaddyPorts::sm_parkoutLeftButtonPressDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_parkoutRightButtonPressDaddy_SenderPort.isConnected())
    {
        auto& container = cc::daddy::CustomDaddyPorts::sm_parkoutRightButtonPressDaddy_SenderPort.reserve();
        container.m_Data = false;
        cc::daddy::CustomDaddyPorts::sm_parkoutRightButtonPressDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ParkSideDaddy_SenderPort.isConnected())
    {
      auto& container = cc::daddy::CustomDaddyPorts::sm_ParkSideDaddy_SenderPort.reserve();
      container.m_Data = cc::target::common::PARKSIDE_None;
      cc::daddy::CustomDaddyPorts::sm_ParkSideDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_brakePedalAppliedDaddy_SenderPort.isConnected())
    {
      cc::daddy::ParkbrkPedlAppld_t& l_pData = cc::daddy::CustomDaddyPorts::sm_brakePedalAppliedDaddy_SenderPort.reserve();
      l_pData.m_Data = cc::target::common::EBrkPedlAppldFlg::BrkPedl_NotApplied;
      cc::daddy::CustomDaddyPorts::sm_brakePedalAppliedDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_brakePedalBeenReleasedBeforeDaddy_SenderPort.isConnected())
    {
      cc::daddy::ParkBreakPedalBeenReleasedBf_t& l_pData = cc::daddy::CustomDaddyPorts::sm_brakePedalBeenReleasedBeforeDaddy_SenderPort.reserve();
      l_pData.m_Data = false;
      cc::daddy::CustomDaddyPorts::sm_brakePedalBeenReleasedBeforeDaddy_SenderPort.deliver();
    }

    if (pc::daddy::BaseDaddyPorts::sm_OdometryDataDaddySenderPort.isConnected())
    {
      pc::daddy::OdometryDataDaddy& l_odometryDaddy = pc::daddy::BaseDaddyPorts::sm_OdometryDataDaddySenderPort.reserve();
      l_odometryDaddy.m_Data.m_vehMoveDir = pc::daddy::FORWARD;
      pc::daddy::BaseDaddyPorts::sm_OdometryDataDaddySenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_PMA_TravelDistDesired_SenderPort.isConnected())
    {
        auto& container = cc::daddy::CustomDaddyPorts::sm_PMA_TravelDistDesired_SenderPort.reserve();
        container.m_Data.m_APG = {};
        container.m_Data.m_TravelDistDesired = 0.0f;
        for (int i = 0; i <RBP_CC_TPC_MAX_NUM_PATHPOINTS; i++)
        {
            container.m_Data.m_Points_pst[i] = {};
        }
        container.m_Data.m_MoveType_en = 0;
        container.m_Data.m_MoveDir_en = 0;
        cc::daddy::CustomDaddyPorts::sm_PMA_TravelDistDesired_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_customVhmAbstRawDataDaddy_SenderPort.isConnected())
    {
        auto& container = cc::daddy::CustomDaddyPorts::sm_customVhmAbstRawDataDaddy_SenderPort.reserve();
        container.m_Data = {};
        cc::daddy::CustomDaddyPorts::sm_customVhmAbstRawDataDaddy_SenderPort.deliver();
    }


    if (cc::daddy::CustomDaddyPorts::sm_HUCalibrationStartDaddy_SenderPort.isConnected())
    {
        auto& container = cc::daddy::CustomDaddyPorts::sm_HUCalibrationStartDaddy_SenderPort.reserve();
        container.m_Data = 0;
        cc::daddy::CustomDaddyPorts::sm_HUCalibrationStartDaddy_SenderPort.deliver();
    }


    if (pc::daddy::BaseDaddyPorts::sm_gearSenderPort.isConnected())
    {
        auto& l_gearState = pc::daddy::BaseDaddyPorts::sm_gearSenderPort.reserve();
        l_gearState.m_Data = pc::daddy::EGear::GEAR_P;
        pc::daddy::BaseDaddyPorts::sm_gearSenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ParkAPA_ParkSpaceDaddy_SenderPort.isConnected())
    {
        using cc::target::common::EPARKSlotStsR2L;
        using cc::target::common::EFAPAParkSlotType;
        using cc::target::common::rbp_Type_ParkManeuverType_en;
        using cc::target::common::EAPAParkSlotOrder;
        constexpr vfc::uint8_t SIDE = cc::target::common::l_L_ParkSpace_side;
        constexpr vfc::uint8_t SLOT = cc::target::common::l_L_ParkSpace_NumberPerside;
        cc::daddy::ParkAPA_ParkSpace_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkAPA_ParkSpaceDaddy_SenderPort.reserve();
        for (int side = 0; side < SIDE; side++)
        {
            for (int slot = 0; slot < SLOT; slot++)
            {
                l_container.m_Data[side][slot].m_APA_PrkgSlot             = EPARKSlotStsR2L::PARKSLOT_NONE;
                l_container.m_Data[side][slot].m_APA_PSType               = EFAPAParkSlotType::APASLOT_DEFAULT;
                l_container.m_Data[side][slot].m_APA_ParkManeuverType     = rbp_Type_ParkManeuverType_en::rbp_ParkBwdIn_enm;
                l_container.m_Data[side][slot].m_APA_PrkgSlotSta_f32      = 0.0;
                l_container.m_Data[side][slot].m_APA_PSCorner2X_i16       = 0;
                l_container.m_Data[side][slot].m_APA_PSCorner2Y_i16       = 0;
                l_container.m_Data[side][slot].m_APA_PSLength_u16         = 0;
                l_container.m_Data[side][slot].m_APA_PSCorner1X_i16       = 0;
                l_container.m_Data[side][slot].m_APA_PSCorner1Y_i16       = 0;
                l_container.m_Data[side][slot].m_APA_PSOrder_u8           = EAPAParkSlotOrder::ORDER_NONE;
            }
        }
        cc::daddy::CustomDaddyPorts::sm_ParkAPA_ParkSpaceDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort.isConnected())
    {
        using namespace cc::target::common;
        auto& container = cc::daddy::CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort.reserve();
        container.m_Data.m_apaStatus = EApaStatus::PassiveStandBy ;
        container.m_Data.m_parkingStage = EParkingStage::Invalid;
        container.m_Data.m_parkMode = EParkingMode::Invalid;
        container.m_Data.m_parkInDirection = EParkInDirection::Unknown;
        // container.m_Data.m_parkingRealTimeData = {};
        container.m_Data.m_pocEnabledDir.m_FrntLeParallelSts = false;
        container.m_Data.m_pocEnabledDir.m_FrntLeCrossSts = false;
        container.m_Data.m_pocEnabledDir.m_FrntRiParallelSts = false;
        container.m_Data.m_pocEnabledDir.m_FrntRiCrossSts = false;
        container.m_Data.m_pocEnabledDir.m_FrntCrossSts = false;
        container.m_Data.m_pocEnabledDir.m_BackCrossSts = false;
        container.m_Data.m_pocEnabledDir.m_BackLeCrossSts = false;
        container.m_Data.m_pocEnabledDir.m_BackRiCrossSts = false;
        container.m_Data.m_pocRecommandDir = EPocDirSel::None;
        container.m_Data.m_freeParkingIn.m_is360FreeParking = true;
        container.m_Data.m_freeParkingIn.m_isSlotParkable = EFreeParkingSlotState::UNAVAILABLE;
        container.m_Data.m_freeParkingIn.m_parkStage = EFreeParkingStage::None;
        container.m_Data.m_freeParkingIn.m_slotType = EFreeParkingSlotType::HorizontalSlot;
        container.m_Data.m_targetSlotPosition = {};  // target slot in guidance
        cc::daddy::CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_FreeParkingSlot_SenderPort.isConnected())
    {
        using namespace cc::target::common;
//        auto& container = cc::daddy::CustomDaddyPorts::sm_FreeParkingSlot_SenderPort.reserve();
        cc::daddy::CustomDaddyPorts::sm_FreeParkingSlot_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_FreeParkingSlotInternal_SenderPort.isConnected())
    {
        using namespace cc::target::common;
//        auto& container = cc::daddy::CustomDaddyPorts::sm_FreeParkingSlotInternal_SenderPort.reserve();
        cc::daddy::CustomDaddyPorts::sm_FreeParkingSlotInternal_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_SlotSelectedId_SenderPort.isConnected())
    {
        using namespace cc::target::common;
        auto& container = cc::daddy::CustomDaddyPorts::sm_SlotSelectedId_SenderPort.reserve();
        container.m_Data = 0;
        cc::daddy::CustomDaddyPorts::sm_SlotSelectedId_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ParkoutSelectedDirection_SenderPort.isConnected())
    {
        using namespace cc::target::common;
        auto& container = cc::daddy::CustomDaddyPorts::sm_ParkoutSelectedDirection_SenderPort.reserve();
        container.m_Data = EPocDirSel::None;
        cc::daddy::CustomDaddyPorts::sm_ParkoutSelectedDirection_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_ZoomLevel_SenderPort.isConnected())
    {
        auto& container = cc::daddy::CustomDaddyPorts::sm_ZoomLevel_SenderPort.reserve();
        container.m_Data = cc::target::common::E3DZoomLevel::LEVEL3;
        cc::daddy::CustomDaddyPorts::sm_ZoomLevel_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_RemoveDistortion_SenderPort.isConnected())
    {
        auto& container = cc::daddy::CustomDaddyPorts::sm_RemoveDistortion_SenderPort.reserve();
        container.m_Data = false;
        cc::daddy::CustomDaddyPorts::sm_RemoveDistortion_SenderPort.deliver();
    }

    //! Impostor transparency
    ImpostorTransparencyDaddy& l_impostorTransparency = sm_impostorTransparencySenderPort.reserve();
    l_impostorTransparency.m_Data = 0.4f;
    sm_impostorTransparencySenderPort.deliver();

    if (cc::daddy::CustomDaddyPorts::sm_SVSFreeModeStDaddy_SenderPort.isConnected())
    {
        cc::daddy::SVSFreeModeStDaddy_t& l_rSVSFreeModeStContainer =
            cc::daddy::CustomDaddyPorts::sm_SVSFreeModeStDaddy_SenderPort.reserve();
        l_rSVSFreeModeStContainer.m_Data = true;
        cc::daddy::CustomDaddyPorts::sm_SVSFreeModeStDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_HUTrailerButtonDaddy_SenderPort.isConnected())
    {
      cc::daddy::TrailerModeDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUTrailerButtonDaddy_SenderPort.reserve();
      l_container.m_Data = 0u;
      cc::daddy::CustomDaddyPorts::sm_HUTrailerButtonDaddy_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_BirdEyeViewSwitch_SenderPort.isConnected())
    {
      cc::daddy::TrailerModeDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_BirdEyeViewSwitch_SenderPort.reserve();
      l_container.m_Data = false;
      cc::daddy::CustomDaddyPorts::sm_BirdEyeViewSwitch_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_SurroundViewRotateAngle_SenderPort.isConnected())
    {
      auto& l_container = cc::daddy::CustomDaddyPorts::sm_SurroundViewRotateAngle_SenderPort.reserve();
      l_container.m_Data = 0;
      cc::daddy::CustomDaddyPorts::sm_SurroundViewRotateAngle_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_CrabGuideline_SenderPort.isConnected())
    {
      auto& container = cc::daddy::CustomDaddyPorts::sm_CrabGuideline_SenderPort.reserve();
      container.m_Data.m_isVisible = false;
      container.m_Data.m_angle = 0.0f;
      cc::daddy::CustomDaddyPorts::sm_CrabGuideline_SenderPort.deliver();
    }

    if (cc::daddy::CustomDaddyPorts::sm_GoldenEmblem_SenderPort.isConnected())
    {
      auto& container = cc::daddy::CustomDaddyPorts::sm_GoldenEmblem_SenderPort.reserve();
      container.m_Data = false;
      cc::daddy::CustomDaddyPorts::sm_GoldenEmblem_SenderPort.deliver();
    }

    sm_customerMemoryPoolsHaveInitialData = true;
  }
}


} //namespace daddy
} //namespace cc
