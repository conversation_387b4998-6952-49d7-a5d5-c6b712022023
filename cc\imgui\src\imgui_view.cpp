//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/imgui/inc/imgui_impl_opengl3.h"
#include "cc/imgui/inc/implot/implot.h"
// #include "cc/util/logging/inc/LoggingContexts.h"
#include "cc/imgui/inc/imgui_view.h"
#include "cc/imgui/inc/imgui_manager.h"
#include "cc/imgui/inc/imgui_internal.h"

// #include "pc/generic/coding/CodingManager.h"

#define SEND_PORT(port, data) \
    {\
        auto& container = port.reserve(); \
        container.m_Data = (data); \
        port.deliver(); \
    }

namespace cc
{
namespace views
{
namespace imgui
{

static void HelpMarker(const char* desc)
{
    ImGui::TextDisabled("(?)");
    if (ImGui::BeginItemTooltip())
    {
        ImGui::PushTextWrapPos(ImGui::GetFontSize() * 35.0f);
        ImGui::TextUnformatted(desc);
        ImGui::PopTextWrapPos();
        ImGui::EndTooltip();
    }
}

static int ConvertFromOSGKey(int key)
{
  using KEY = osgGA::GUIEventAdapter::KeySymbol;
  switch (key)
  {
    case KEY::KEY_Tab:       return ImGuiKey_Tab;
    case KEY::KEY_Left:      return ImGuiKey_LeftArrow;
    case KEY::KEY_Right:     return ImGuiKey_RightArrow;
    case KEY::KEY_Up:        return ImGuiKey_UpArrow;
    case KEY::KEY_Down:      return ImGuiKey_DownArrow;
    case KEY::KEY_Page_Up:   return ImGuiKey_PageUp;
    case KEY::KEY_Page_Down: return ImGuiKey_PageDown;
    case KEY::KEY_Home:      return ImGuiKey_Home;
    case KEY::KEY_End:       return ImGuiKey_End;
    case KEY::KEY_Delete:    return ImGuiKey_Delete;
    case KEY::KEY_BackSpace: return ImGuiKey_Backspace;
    case KEY::KEY_Return:    return ImGuiKey_Enter;
    case KEY::KEY_Escape:    return ImGuiKey_Escape;
    default:                 return -1;
  }
}


//!
//! ImGuiNewFrameCallback
//!
class ImGuiView::ImGuiNewFrameCallback : public osg::Camera::DrawCallback
{
public:
  ImGuiNewFrameCallback(ImGuiView& handler) : m_handler(handler) {}
  void operator()(osg::RenderInfo& renderInfo) const override { m_handler.newFrame(renderInfo); }

private:
  ImGuiView& m_handler;
};


//!
//! ImGuiRenderCallback
//!
class ImGuiView::ImGuiRenderCallback : public osg::Camera::DrawCallback
{
public:
  ImGuiRenderCallback(ImGuiView& handler) : m_handler(handler) {}
  void operator()(osg::RenderInfo& renderInfo) const override { m_handler.render(renderInfo); }

private:
  ImGuiView& m_handler;
};


//!
//! ImGuiInitOperation
//!
class ImGuiView::ImGuiInitOperation : public osg::Operation
{
public:
  ImGuiInitOperation()
    : osg::Operation("ImGuiInitOperation", false)
    , m_initialized(false)
  {
  }

  void operator() (osg::Object* object) override
  {
    osg::GraphicsContext* context = dynamic_cast<osg::GraphicsContext*>(object);
    if (!context)
      return;
    if (!m_initialized)
    {
      if (!ImGui_ImplOpenGL3_Init("#version 100"))
      {
        std::cout << "ImGui_ImplOpenGL3_Init() Failed\n";
      }
      m_initialized = true;
    }
  }
private:
  bool m_initialized;
};


//!
//! ImGuiView
//!
ImGuiView::ImGuiView(const std::string& f_name, const pc::core::Viewport& f_viewport, const pc::virtcam::VirtualCamera& f_camPos, pc::core::Framework *f_pFramework)
  : pc::core::View(f_name, f_viewport, f_camPos)
  , m_time(0.0f)
  , m_mousePressed{false}
  , m_mouseWheel(0.0f)
  , m_initialized(false)
  , m_pFramework(f_pFramework)
{
  assert(m_pFramework);
  m_pFramework->getView()->addEventHandler(this);


  // work arround for imgui threading issue
  osgViewer::ViewerBase* l_viewer = m_pFramework->getViewerBase();
  l_viewer->setThreadingModel(osgViewer::ViewerBase::SingleThreaded);

  initImgui();

  // add draw callbacks
  setPreDrawCallback(new ImGuiNewFrameCallback(*this));
  setPostDrawCallback(new ImGuiRenderCallback(*this));

  // ImGui render order should be the last
  setRenderOrder(osg::Camera::POST_RENDER, std::numeric_limits<int>::max() - 1);
  l_viewer->setRealizeOperation(new ImGuiInitOperation);
  l_viewer->setReleaseContextAtEndOfFrameHint(false);
  l_viewer->realize();
}

void ImGuiView::initImgui()
{
  // init imgui
  IMGUI_CHECKVERSION();
  ImGui::CreateContext();
  ImPlot::CreateContext();
  ImGuiIO& io = ImGui::GetIO();
  io.DisplaySize = ImVec2(cc::core::g_views->m_usableCanvasViewport.m_size.x(), cc::core::g_views->m_usableCanvasViewport.m_size.y());
  io.ConfigFlags |= ImGuiConfigFlags_DockingEnable;       // Enable Docking
  io.Fonts->AddFontFromFileTTF(CONCAT_PATH("cc/resources/HYQiHei_55S.ttf").c_str(), 25.0f);
  m_initFile = (CONCAT_PATH("cc/resources_sil/imgui.ini").c_str());
  io.IniFilename = m_initFile.c_str();
  io.KeyMap[ImGuiKey_A] = osgGA::GUIEventAdapter::KeySymbol::KEY_A;
  io.KeyMap[ImGuiKey_C] = osgGA::GUIEventAdapter::KeySymbol::KEY_C;
  io.KeyMap[ImGuiKey_V] = osgGA::GUIEventAdapter::KeySymbol::KEY_V;
  io.KeyMap[ImGuiKey_X] = osgGA::GUIEventAdapter::KeySymbol::KEY_X;
  io.KeyMap[ImGuiKey_Y] = osgGA::GUIEventAdapter::KeySymbol::KEY_Y;
  io.KeyMap[ImGuiKey_Z] = osgGA::GUIEventAdapter::KeySymbol::KEY_Z;

  m_initialized = true;
}


void ImGuiView::newFrame()
{
  static osg::Timer_t startTick = osg::Timer::instance()->getStartTick();
  ImGui_ImplOpenGL3_NewFrame();
  ImGuiIO& io = ImGui::GetIO();
  io.DisplaySize = ImVec2(cc::core::g_views->m_usableCanvasViewport.m_size.x(), cc::core::g_views->m_usableCanvasViewport.m_size.y());
  osg::Timer_t currentTick = osg::Timer::instance()->tick();
  io.DeltaTime = osg::Timer::instance()->delta_s(startTick, currentTick) + 0.0000001;
  io.MousePos = m_mousePos;
  for (int i = 0; i < 3; i++) {io.MouseDown[i] = m_mousePressed[i];}
  io.MouseWheel = m_mouseWheel;
  m_mouseWheel = 0.0f;
  ImGui::NewFrame();
}


void ImGuiView::render()
{
  ImGui::Render();
  ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());
}


void ImGuiView::newFrame(osg::RenderInfo& renderInfo)
{
}


void ImGuiView::renderDockspace()
{
  static bool dockspace_open = true;
  static bool opt_fullscreen = true;
  static bool opt_padding = false;
  static ImGuiDockNodeFlags dockspace_flags = ImGuiDockNodeFlags_PassthruCentralNode;

  // We are using the ImGuiWindowFlags_NoDocking flag to make the parent window not dockable into,
  // because it would be confusing to have two docking targets within each others.
  ImGuiWindowFlags window_flags = ImGuiWindowFlags_NoDocking;
  if (getNodeMask() != 0u && false)
  {
    window_flags |= ImGuiWindowFlags_MenuBar;
  }
  if (opt_fullscreen)
  {
      const ImGuiViewport* viewport = ImGui::GetMainViewport();
      ImGui::SetNextWindowPos(viewport->WorkPos);
      ImGui::SetNextWindowSize(viewport->WorkSize);
      ImGui::SetNextWindowViewport(viewport->ID);
      ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 0.0f);
      ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 0.0f);
      window_flags |= ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoMove;
      window_flags |= ImGuiWindowFlags_NoBringToFrontOnFocus | ImGuiWindowFlags_NoNavFocus;
  }
  else
  {
      dockspace_flags &= ~ImGuiDockNodeFlags_PassthruCentralNode;
  }

  // When using ImGuiDockNodeFlags_PassthruCentralNode, DockSpace() will render our background
  // and handle the pass-thru hole, so we ask Begin() to not render a background.
  if (dockspace_flags & ImGuiDockNodeFlags_PassthruCentralNode)
      window_flags |= ImGuiWindowFlags_NoBackground;

  // Important: note that we proceed even if Begin() returns false (aka window is collapsed).
  // This is because we want to keep our DockSpace() active. If a DockSpace() is inactive,
  // all active windows docked into it will lose their parent and become undocked.
  // We cannot preserve the docking relationship between an active window and an inactive docking, otherwise
  // any change of dockspace/settings would lead to windows being stuck in limbo and never being visible.
  if (!opt_padding)
      ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(0.0f, 0.0f));
  ImGui::Begin("DockSpace Demo", &dockspace_open, window_flags);
  if (!opt_padding)
      ImGui::PopStyleVar();

  if (opt_fullscreen)
      ImGui::PopStyleVar(2);

  // Submit the DockSpace
  ImGuiIO& io = ImGui::GetIO();
  if (io.ConfigFlags & ImGuiConfigFlags_DockingEnable)
  {
      ImGuiID dockspace_id = ImGui::GetID("MyDockSpace");
      ImGui::DockSpace(dockspace_id, ImVec2(0.0f, 0.0f), dockspace_flags);
  }
  else
  {
    ImGuiIO& io = ImGui::GetIO();
    ImGui::Text("ERROR: Docking is not enabled! See Demo > Configuration.");
    ImGui::Text("Set io.ConfigFlags |= ImGuiConfigFlags_DockingEnable in your code, or ");
    ImGui::SameLine(0.0f, 0.0f);
    if (ImGui::SmallButton("click here"))
        io.ConfigFlags |= ImGuiConfigFlags_DockingEnable;
  }
  if (getNodeMask() != 0u && false)
  {
    if (ImGui::BeginMenuBar())
    {
      if (ImGui::BeginMenu("Docking Options"))
      {
        // Disabling fullscreen would allow the window to be moved to the front of other windows,
        // which we can't undo at the moment without finer window depth/z control.
        ImGui::MenuItem("Fullscreen", NULL, &opt_fullscreen);
        ImGui::MenuItem("Padding", NULL, &opt_padding);
        ImGui::Separator();

        if (ImGui::MenuItem("Flag: NoDockingOverCentralNode", "", (dockspace_flags & ImGuiDockNodeFlags_NoDockingOverCentralNode) != 0)) { dockspace_flags ^= ImGuiDockNodeFlags_NoDockingOverCentralNode; }
        if (ImGui::MenuItem("Flag: NoDockingSplit",         "", (dockspace_flags & ImGuiDockNodeFlags_NoDockingSplit) != 0))             { dockspace_flags ^= ImGuiDockNodeFlags_NoDockingSplit; }
        if (ImGui::MenuItem("Flag: NoUndocking",            "", (dockspace_flags & ImGuiDockNodeFlags_NoUndocking) != 0))                { dockspace_flags ^= ImGuiDockNodeFlags_NoUndocking; }
        if (ImGui::MenuItem("Flag: NoResize",               "", (dockspace_flags & ImGuiDockNodeFlags_NoResize) != 0))                   { dockspace_flags ^= ImGuiDockNodeFlags_NoResize; }
        if (ImGui::MenuItem("Flag: AutoHideTabBar",         "", (dockspace_flags & ImGuiDockNodeFlags_AutoHideTabBar) != 0))             { dockspace_flags ^= ImGuiDockNodeFlags_AutoHideTabBar; }
        if (ImGui::MenuItem("Flag: PassthruCentralNode",    "", (dockspace_flags & ImGuiDockNodeFlags_PassthruCentralNode) != 0, opt_fullscreen)) { dockspace_flags ^= ImGuiDockNodeFlags_PassthruCentralNode; }
        ImGui::Separator();

        ImGui::EndMenu();
      }
      ImGui::EndMenuBar();
    }
  }
  ImGui::End();
}

void ImGuiView::render(osg::RenderInfo& renderInfo)
{
    static float transparency = 0.5f;
    ImGui::PushStyleColor(ImGuiCol_WindowBg, ImVec4(0.0f, 0.0f, 0.0f, transparency));
    // renderDockspace();
    cc::imgui::ImGuiManager*             l_manager  = cc::imgui::ImGuiManager::getInstance();
    cc::imgui::ImGuiManager::ContextMap* l_contexts = l_manager->getContexts();
    for (auto ltr = l_contexts->begin(); ltr != l_contexts->end(); ++ltr)
    {
        ltr->second->update(renderInfo);
    }
    customCommands();
    parkingCommands();
    testCommands();
    ImGui::Begin("Settings");
    ImGui::Text("mouseMode: %s", m_mouseMode == MouseMode::IMGUI_MODE ? "IMGUI_MODE" : "SVS_MODE");
    ImGui::SliderFloat("Transparency", &transparency, 0.0f, 1.0f);
    ImGui::End();
    // ImGui::ShowDemoWindow();
    // ImPlot::ShowDemoWindow();
    ImGui::PopStyleColor(); // Transparency
    ImGui::Render();
    ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());
}

bool ImGuiView::handle(const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& aa)
{
  using KEY = osgGA::GUIEventAdapter::KeySymbol;
  using cc::target::common::EThemeTypeDayNight;
  if (!m_initialized)
  {
    return false;
  }

  static EThemeTypeDayNight curTheme = static_cast<EThemeTypeDayNight>(-1);
  EThemeTypeDayNight newTheme = EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY;
  if (m_pFramework->asCustomFramework()->m_dayNightThemeDaddy_Receiver .hasData())
  {
    newTheme = m_pFramework->asCustomFramework()->m_dayNightThemeDaddy_Receiver .getData()->m_Data;
  }
  if (newTheme != curTheme)
  {
    if (newTheme == EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
    {
      ImGui::StyleColorsLight();
    }
    else
    {
      ImGui::StyleColorsDark();
    }
    curTheme = newTheme;
  }

  ImGuiIO& io = ImGui::GetIO();
  const bool wantCaptureMouse = io.WantCaptureMouse;
  const bool wantCaptureKeyboard = io.WantCaptureKeyboard;

  IMGUI_LOG("Settings", "m_mousePressed[0]", m_mousePressed[0] ? "TRUE" : "FALSE");
  IMGUI_LOG("Settings", "m_mousePressed[1]", m_mousePressed[1] ? "TRUE" : "FALSE");
  IMGUI_LOG("Settings", "m_mousePressed[2]", m_mousePressed[2] ? "TRUE" : "FALSE");

  IMGUI_LOG("Settings", "MousePos", std::to_string(io.MousePos.x) + " " + std::to_string(io.MousePos.y));
  IMGUI_LOG("Settings", "MousePosPrev", std::to_string(io.MousePosPrev.x) + " " + std::to_string(io.MousePosPrev.y));
  IMGUI_LOG("Settings", "MouseDelta", std::to_string(io.MouseDelta.x) + " " + std::to_string(io.MouseDelta.y));

  // ! check whether top view area is clicked or not
  // if ( m_pFramework->asCustomFramework()->m_hmiDataReceiver.hasNewData() || m_pFramework->asCustomFramework()->m_HUTouchTypeReceiver.hasNewData())
  // {
  //   const cc::daddy::HmiData_Daddy* l_hmiData = m_pFramework->asCustomFramework()->m_hmiDataReceiver.getData();
  //   const cc::daddy::HUtouchEvenTypeDaddy_t* l_touchEventData = m_pFramework->asCustomFramework()->m_HUTouchTypeReceiver.getData();

  //   vfc::uint16_t l_huX = l_hmiData->m_Data.m_huX;
  //   vfc::uint16_t l_huY = l_hmiData->m_Data.m_huY;
  //   vfc::uint8_t  l_touchEvent = l_touchEventData->m_Data;

  //   switch (l_touchEvent)
  //   {
  //     case 0: // NONE
  //     {
  //       IMGUI_LOG("Settings", "Touch Event", "NONE");
  //       break;
  //     }
  //     case 1: // PRESS
  //     {
  //       if (m_mouseMode != MouseMode::SVS_MODE) { return false; }
  //       io.MousePos = ImVec2(l_huX, l_huY);
  //       IMGUI_LOG("Settings", "Touch Event", "PRESS");
  //       return wantCaptureMouse;
  //     }
  //     case 2: // RELEASE
  //     {
  //       if (m_mouseMode != MouseMode::SVS_MODE) { return false; }
  //       io.MousePos = ImVec2(l_huX, l_huY);
  //       IMGUI_LOG("Settings", "Touch Event", "RELEASE");
  //       return wantCaptureMouse;
  //     }
  //     case 3: // MOVE
  //     {
  //       if (m_mouseMode != MouseMode::SVS_MODE) { return false; }
  //       io.MousePos = ImVec2(l_huX, l_huY);
  //       IMGUI_LOG("Settings", "Touch Event", "MOVE");
  //       return wantCaptureMouse;
  //     }
  //   }
  // }
  switch (ea.getEventType())
  {
    case osgGA::GUIEventAdapter::KEYDOWN:
    case osgGA::GUIEventAdapter::KEYUP:
    {
      const bool isKeyDown = ea.getEventType() == osgGA::GUIEventAdapter::KEYDOWN;
      const int c = ea.getKey();
      const int special_key = ConvertFromOSGKey(c);
      if (special_key > 0)
      {
        assert((special_key >= 0 && special_key < 645) && "ImGui KeysMap is an array of 645");

        io.KeysDown[special_key] = isKeyDown;

        io.KeyCtrl  = ea.getModKeyMask() & osgGA::GUIEventAdapter::MODKEY_CTRL;
        io.KeyShift = ea.getModKeyMask() & osgGA::GUIEventAdapter::MODKEY_SHIFT;
        io.KeyAlt   = ea.getModKeyMask() & osgGA::GUIEventAdapter::MODKEY_ALT;
        io.KeySuper = ea.getModKeyMask() & osgGA::GUIEventAdapter::MODKEY_SUPER;
      }
      else if (isKeyDown && c > 0 && c < 0xFF)
      {
        io.AddInputCharacter((unsigned short)c);
      }

      if (ImGuiKey_Tab == special_key && ea.getEventType() == osgGA::GUIEventAdapter::KEYUP)
      {
        auto scene = m_pFramework->getScene();
        if (scene)
        {
          bool enable = scene->getView(cc::core::CustomViews::IMGUI_VIEW)->getNodeMask() != 0u;

          scene->setViewEnabled(cc::core::CustomViews::IMGUI_VIEW, !enable);
        }
      }
      if (KEY::KEY_Space == c && ea.getEventType() == osgGA::GUIEventAdapter::KEYUP)
      {
        if (m_mouseMode == MouseMode::IMGUI_MODE)
        {
          m_mouseMode = MouseMode::SVS_MODE;
        }
        else
        {
          m_mouseMode = MouseMode::IMGUI_MODE;
        }
      }
      return wantCaptureKeyboard;
    }
    case (osgGA::GUIEventAdapter::RELEASE):
    case (osgGA::GUIEventAdapter::PUSH):
    {
      m_mousePos = ImVec2(ea.getX(), io.DisplaySize.y - ea.getY());
      m_mousePressed[0] = ea.getButtonMask() & osgGA::GUIEventAdapter::LEFT_MOUSE_BUTTON;
      m_mousePressed[1] = ea.getButtonMask() & osgGA::GUIEventAdapter::RIGHT_MOUSE_BUTTON;
      m_mousePressed[2] = ea.getButtonMask() & osgGA::GUIEventAdapter::MIDDLE_MOUSE_BUTTON;
      return wantCaptureMouse;
    }
    case (osgGA::GUIEventAdapter::DRAG):
    case (osgGA::GUIEventAdapter::MOVE):
    {
      m_mousePos = ImVec2(ea.getX(), io.DisplaySize.y - ea.getY());
      return wantCaptureMouse;
    }
    case (osgGA::GUIEventAdapter::SCROLL):
    {
      m_mouseWheel = ea.getScrollingMotion() == osgGA::GUIEventAdapter::SCROLL_UP ? 1.0 : -1.0;
      return wantCaptureMouse;
    }
    default:
    {
      return false;
    }
  }
  return false;
}


void ImGuiView::customCommands()
{
    using cc::daddy::CustomDaddyPorts;
    static bool showDemoWindow = true;

    if (ImGui::Begin("Commands"))
    {
        // ZoomLevel
        using namespace cc::target::common;
        static int s_zoomLevel = static_cast<int>(E3DZoomLevel::LEVEL3);
        if (ImGui::SliderInt("ZoomLevel", &s_zoomLevel, 0, static_cast<int>(E3DZoomLevel::LEVEL6)))
        {
            auto& l_rData = cc::daddy::CustomDaddyPorts::sm_ZoomLevel_SenderPort.reserve();
            switch (static_cast<E3DZoomLevel>(s_zoomLevel))
            {
            case E3DZoomLevel::LEVEL0:
                l_rData.m_Data = cc::target::common::E3DZoomLevel::LEVEL0;
                break;
            case E3DZoomLevel::LEVEL1:
                l_rData.m_Data = cc::target::common::E3DZoomLevel::LEVEL1;
                break;
            case E3DZoomLevel::LEVEL2:
                l_rData.m_Data = cc::target::common::E3DZoomLevel::LEVEL2;
                break;
            case E3DZoomLevel::LEVEL3:
                l_rData.m_Data = cc::target::common::E3DZoomLevel::LEVEL3;
                break;
            case E3DZoomLevel::LEVEL4:
                l_rData.m_Data = cc::target::common::E3DZoomLevel::LEVEL4;
                break;
            case E3DZoomLevel::LEVEL5:
                l_rData.m_Data = cc::target::common::E3DZoomLevel::LEVEL5;
                break;
            case E3DZoomLevel::LEVEL6:
                l_rData.m_Data = cc::target::common::E3DZoomLevel::LEVEL6;
                break;
            default:
                l_rData.m_Data = cc::target::common::E3DZoomLevel::LEVEL3;
                break;
            }
            cc::daddy::CustomDaddyPorts::sm_ZoomLevel_SenderPort.deliver();
        }
        // Trailer Flag
        static bool s_trailerMode = false;
        {
            using namespace pc::daddy;
            if (ImGui::Checkbox("TrailerMode", &s_trailerMode))
            {
                auto& l_container  = cc::daddy::CustomDaddyPorts::sm_HUTrailerButtonDaddy_SenderPort.reserve();
                l_container.m_Data = s_trailerMode;
                cc::daddy::CustomDaddyPorts::sm_HUTrailerButtonDaddy_SenderPort.deliver();
            }
        }
        static int s_frontSteering = 0;
        {
            if (ImGui::SliderInt("FrontSteering", &s_frontSteering, -40, 40))
            {
                pc::daddy::SteeringAngleDaddy& l_steeringAngleDaddy =
                    pc::daddy::BaseDaddyPorts::sm_SteeringAngleFrontDaddySenderPort.reserve();
                l_steeringAngleDaddy.m_Data = vfc::CSI::si_degree_f32_t(s_frontSteering);
                pc::daddy::BaseDaddyPorts::sm_SteeringAngleFrontDaddySenderPort.deliver();
            }
        }
        static int s_rearSteering = 0;
        {
            if (ImGui::SliderInt("RearSteering", &s_rearSteering, -40, 40))
            {
                pc::daddy::SteeringAngleDaddy& l_steeringAngleDaddy =
                    pc::daddy::BaseDaddyPorts::sm_SteeringAngleRearDaddySenderPort.reserve();
                l_steeringAngleDaddy.m_Data = vfc::CSI::si_degree_f32_t(s_rearSteering);
                pc::daddy::BaseDaddyPorts::sm_SteeringAngleRearDaddySenderPort.deliver();
            }
        }
        static int s_gear = 0;
        // Gear
        {
            using namespace pc::daddy;
            bool l_dirty = false;
            l_dirty |= ImGui::RadioButton("GearP", &s_gear, static_cast<int>(EGear::GEAR_P)); ImGui::SameLine();
            l_dirty |= ImGui::RadioButton("GearN", &s_gear, static_cast<int>(EGear::GEAR_N)); ImGui::SameLine();
            l_dirty |= ImGui::RadioButton("GearD", &s_gear, static_cast<int>(EGear::GEAR_D)); ImGui::SameLine();
            l_dirty |= ImGui::RadioButton("GearR", &s_gear, static_cast<int>(EGear::GEAR_R));
            if (l_dirty)
            {
                GearDaddy& l_gear = BaseDaddyPorts::sm_gearSenderPort.reserve();
                l_gear.m_Data = s_gear;
                BaseDaddyPorts::sm_gearSenderPort.deliver();
            }
        }
        static int s_radarWallButton;
        static const char* s_radarWallButtonItems[] = {
            "RADARWALL_INVAILD",
            "CLOSERADARWALL",
            "OPENRADARWALL",
            "RADARWALL_RESERVE" };
        if (ImGui::ListBox(
            "ApaStatus",
            &s_radarWallButton,
            s_radarWallButtonItems,
            IM_ARRAYSIZE(s_radarWallButtonItems),
            IM_ARRAYSIZE(s_radarWallButtonItems)))
        {
            auto& l_container  = cc::daddy::CustomDaddyPorts::sm_HURadarWallButtonDaddy_SenderPort.reserve();
            l_container.m_Data = static_cast<cc::target::common::EhuRadarWallButton>(s_radarWallButton);
            cc::daddy::CustomDaddyPorts::sm_HURadarWallButtonDaddy_SenderPort.deliver();
        }

        static bool s_bevScale = false;
        if (ImGui::Checkbox("BirdViewEyeScale", &s_bevScale))
        {
            auto& l_container = cc::daddy::CustomDaddyPorts::CustomDaddyPorts::sm_BirdEyeViewSwitch_SenderPort.reserve();
            l_container.m_Data = s_bevScale;
            cc::daddy::CustomDaddyPorts::CustomDaddyPorts::sm_BirdEyeViewSwitch_SenderPort.deliver();
        }

        static bool removeDistortion = false;
        if (ImGui::Checkbox("RemoveDistortion", &removeDistortion))
        {
            SEND_PORT(CustomDaddyPorts::sm_RemoveDistortion_SenderPort, removeDistortion);
        }
        // if (ImGui::Button("ToggleParkable"))
        // {
        //     static bool parkable = true;
        //     SEND_PORT(CustomDaddyPorts::sm_FreeParkingParkableDaddy_SenderPort, parkable);
        //     parkable = !parkable;
        // }
        // ImGui::SameLine();
        // if (ImGui::Button("ToggleFreeparking"))
        // {
        //     static bool freeparkingActive = true;
        //     SEND_PORT(CustomDaddyPorts::sm_ParkFreeParkingActiveDaddy_SenderPort, freeparkingActive);
        //     freeparkingActive = !freeparkingActive;
        // }

        // if (ImGui::Button("DarkTheme"))
        // {
        //     SEND_PORT(
        //         cc::daddy::CustomDaddyPorts::sm_dayNightThemeDaddy_SenderPort,
        //         cc::target::common::EThemeTypeDayNight::ETHEME_TYPE_DAYNIGHT_NIGHT);
        // }
        // ImGui::SameLine();
        // if (ImGui::Button("LightTheme"))
        // {
        //     SEND_PORT(
        //         cc::daddy::CustomDaddyPorts::sm_dayNightThemeDaddy_SenderPort,
        //         cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY);
        // }
        // if (ImGui::Button("Horizontal"))
        // {
        //     SEND_PORT(
        //         cc::daddy::CustomDaddyPorts::sm_HURotateStatusDaddy_SenderPort,
        //         cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI);
        // }
        // ImGui::SameLine();
        // if (ImGui::Button("Vertical"))
        // {
        //     SEND_PORT(
        //         cc::daddy::CustomDaddyPorts::sm_HURotateStatusDaddy_SenderPort,
        //         cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT);
        // }

        static bool vehTrans = true;
        if (ImGui::Checkbox("VehTrans", &vehTrans))
        {
            SEND_PORT(cc::daddy::CustomDaddyPorts::sm_HUvehTransReqDaddy_SenderPort, vehTrans);
        }

        //! Crab Guideline
        bool crabGuidelineChanged = false;
        static bool s_crabGuidelineVisible = false;
        static bool s_crabGuidelineGearVisible = false;
        static vfc::float32_t s_crabGuidelineAngle = 0.0f;
        static vfc::float32_t s_crabGuidelinemaxAngle = 0.0f;
        static vfc::float32_t s_crabGuidelineleftRearWheelAngle = 0.0f;
        static vfc::float32_t s_crabGuidelinerightRearWheelAngle = 0.0f;

        crabGuidelineChanged |= ImGui::Checkbox("Crab Guideline Visible", &s_crabGuidelineVisible);
        crabGuidelineChanged |= ImGui::Checkbox("Crab Guideline Gear Visible", &s_crabGuidelineGearVisible);
        crabGuidelineChanged |= ImGui::SliderFloat("Crab Guideline Angle", &s_crabGuidelineAngle, -180.0f, 180.0f);
        crabGuidelineChanged |= ImGui::SliderFloat("Crab Guideline Max Angle", &s_crabGuidelinemaxAngle, -180.0f, 180.0f);
        crabGuidelineChanged |= ImGui::SliderFloat("Crab Guideline Left Rear Wheel Angle", &s_crabGuidelineleftRearWheelAngle, -180.0f, 180.0f);
        crabGuidelineChanged |= ImGui::SliderFloat("Crab Guideline Right Rear Wheel Angle", &s_crabGuidelinerightRearWheelAngle, -180.0f, 180.0f);

        if (crabGuidelineChanged)
        {
            auto& l_container              = cc::daddy::CustomDaddyPorts::sm_CrabGuideline_SenderPort.reserve();
            l_container.m_Data.m_isVisible = s_crabGuidelineVisible;
            l_container.m_Data.m_angle     = s_crabGuidelineAngle;
            // l_container.m_Data.m_maxAngle = s_crabGuidelinemaxAngle;
            l_container.m_Data.m_leftRearWheelAngle     = s_crabGuidelineleftRearWheelAngle;
            l_container.m_Data.m_rightRearWheelAngle = s_crabGuidelinerightRearWheelAngle;
            // l_container.m_Data.m_isGearVisible     = s_crabGuidelineGearVisible;
            cc::daddy::CustomDaddyPorts::sm_CrabGuideline_SenderPort.deliver();
        }

        static int vehTransLevel = 0;
        if (ImGui::Button("Level0"))
        {
            vehTransLevel = 0;
            SEND_PORT(cc::daddy::CustomDaddyPorts::sm_HUvehTransReqDaddy_SenderPort, false);
            SEND_PORT(cc::daddy::CustomDaddyPorts::sm_HUvehTransLevelDaddy_SenderPort, vehTransLevel);
        }
        if (ImGui::Button("Level1"))
        {
            vehTransLevel = 1;
            SEND_PORT(cc::daddy::CustomDaddyPorts::sm_HUvehTransReqDaddy_SenderPort, true);
            SEND_PORT(cc::daddy::CustomDaddyPorts::sm_HUvehTransLevelDaddy_SenderPort, vehTransLevel);
        }
        if (ImGui::Button("Level2"))
        {
            vehTransLevel = 2;
            SEND_PORT(cc::daddy::CustomDaddyPorts::sm_HUvehTransReqDaddy_SenderPort, true);
            SEND_PORT(cc::daddy::CustomDaddyPorts::sm_HUvehTransLevelDaddy_SenderPort, vehTransLevel);
        }
        {
            using namespace pc::daddy;
            bool l_dirty = false;
            l_dirty |= ImGui::RadioButton("Level0", &vehTransLevel, 0); ImGui::SameLine();
            l_dirty |= ImGui::RadioButton("Level1", &vehTransLevel, 1); ImGui::SameLine();
            l_dirty |= ImGui::RadioButton("Level2", &vehTransLevel, 2);
            if (l_dirty)
            {
                cc::daddy::HUvehTransLevel_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUvehTransLevelDaddy_SenderPort.reserve();
                l_container.m_Data = static_cast<vfc::uint8_t>(vehTransLevel);
                cc::daddy::CustomDaddyPorts::sm_HUvehTransLevelDaddy_SenderPort.deliver();
            }
        }

        static int  transStatus         = 0;
        const char* transStatusArray[4] = {
            "INVAILD",
            "HALF_TRANSPARENT",
            "COMPLETE_TRANSPARENT",
            "RESERVE",
        };
        const char* transStatus_name =
            (transStatus >= 0 && transStatus < 4) ? transStatusArray[transStatus] : "Unknown";
        if (ImGui::SliderInt("Trans Status", &transStatus, 0, 4 - 1, transStatus_name))
        {
            SEND_PORT(
                cc::daddy::CustomDaddyPorts::sm_HUTransparentModeDaddy_SenderPort,
                static_cast<cc::target::common::ETransparentMode>(transStatus));
        }

        static bool frontLeftDoorOpen  = false;
        static bool frontRightDoorOpen = false;
        static bool rearLeftDoorOpen   = false;
        static bool rearRightDoorOpen  = false;
        static bool trunkOpen          = false;
        static bool hoodOpen           = false;
        static bool spoilerOpen        = false;

        if (ImGui::Checkbox("Front Left Door", &frontLeftDoorOpen) ||
            ImGui::Checkbox("Front Right Door", &frontRightDoorOpen) ||
            ImGui::Checkbox("Rear Left Door", &rearLeftDoorOpen) ||
            ImGui::Checkbox("Rear Right Door", &rearRightDoorOpen) || ImGui::Checkbox("Trunk", &trunkOpen) ||
            ImGui::Checkbox("Hood", &hoodOpen) || ImGui::Checkbox("Spoiler", &spoilerOpen))
        {
            using namespace pc::daddy;
            DoorStateDaddy& l_doorStates             = BaseDaddyPorts::sm_DoorOpenDaddySenderPort.reserve();
            l_doorStates.m_Data[CARDOOR_FRONT_LEFT]  = frontLeftDoorOpen ? CARDOORSTATE_OPEN : CARDOORSTATE_CLOSED;
            l_doorStates.m_Data[CARDOOR_FRONT_RIGHT] = frontRightDoorOpen ? CARDOORSTATE_OPEN : CARDOORSTATE_CLOSED;
            l_doorStates.m_Data[CARDOOR_REAR_LEFT]   = rearLeftDoorOpen ? CARDOORSTATE_OPEN : CARDOORSTATE_CLOSED;
            l_doorStates.m_Data[CARDOOR_REAR_RIGHT]  = rearRightDoorOpen ? CARDOORSTATE_OPEN : CARDOORSTATE_CLOSED;
            l_doorStates.m_Data[CARDOOR_TRUNK]       = trunkOpen ? CARDOORSTATE_OPEN : CARDOORSTATE_CLOSED;
            l_doorStates.m_Data[CARDOOR_HOOD]        = hoodOpen ? CARDOORSTATE_OPEN : CARDOORSTATE_CLOSED;
            l_doorStates.m_Data[CARDOOR_SPOILER]     = spoilerOpen ? CARDOORSTATE_OPEN : CARDOORSTATE_CLOSED;
            BaseDaddyPorts::sm_DoorOpenDaddySenderPort.deliver();
        }

        bool screenDirty = false;
        EScreenID screen = EScreenID_NO_CHANGE;

        if (ImGui::Button("EScreenID_CARB_TURN")) { screenDirty = true; screen = EScreenID_CARB_TURN; }
        // if (ImGui::Button("EScreenID_NO_CHANGE")) { screenDirty = true; screen = EScreenID_NO_CHANGE; }
        // if (ImGui::Button("EScreenID_NO_VIDEO_USER")) { screenDirty = true; screen = EScreenID_NO_VIDEO_USER; }
        // if (ImGui::Button("EScreenID_LSMG")) { screenDirty = true; screen = EScreenID_LSMG; }
        // if (ImGui::Button("EScreenID_CONTEXT_ON_ROAD")) { screenDirty = true; screen = EScreenID_CONTEXT_ON_ROAD; }
        // if (ImGui::Button("EScreenID_CONTEXT_OFF_ROAD")) { screenDirty = true; screen = EScreenID_CONTEXT_OFF_ROAD; }
        // if (ImGui::Button("EScreenID_CONTEXT_TOWING")) { screenDirty = true; screen = EScreenID_CONTEXT_TOWING; }
        // if (ImGui::Button("EScreenID_CONTEXT_JAPANESE")) { screenDirty = true; screen = EScreenID_CONTEXT_JAPANESE; }
        // if (ImGui::Button("EScreenID_CONTEXT_THREAT")) { screenDirty = true; screen = EScreenID_CONTEXT_THREAT; }
        if (ImGui::Button("EScreenID_SINGLE_FRONT_NORMAL")) { screenDirty = true; screen = EScreenID_SINGLE_FRONT_NORMAL; }
        // if (ImGui::Button("EScreenID_SINGLE_STB")) { screenDirty = true; screen = EScreenID_SINGLE_STB; }
        // if (ImGui::Button("EScreenID_SINGLE_FRONT_JUNCTION")) { screenDirty = true; screen = EScreenID_SINGLE_FRONT_JUNCTION; }
        // if (ImGui::Button("EScreenID_SINGLE_REAR_NORMAL_OFF_ROAD")) { screenDirty = true; screen = EScreenID_SINGLE_REAR_NORMAL_OFF_ROAD; }
        // if (ImGui::Button("EScreenID_PARK_ASSIST_FRONT")) { screenDirty = true; screen = EScreenID_PARK_ASSIST_FRONT; }
        // if (ImGui::Button("EScreenID_SINGLE_FRONT_RAW")) { screenDirty = true; screen = EScreenID_SINGLE_FRONT_RAW; }
        if (ImGui::Button("EScreenID_SINGLE_REAR_NORMAL_ON_ROAD")) { screenDirty = true; screen = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD; }
        // if (ImGui::Button("EScreenID_SINGLE_REAR_JUNCTION")) { screenDirty = true; screen = EScreenID_SINGLE_REAR_JUNCTION; }
        // if (ImGui::Button("EScreenID_SINGLE_REAR_RAW_DIAG")) { screenDirty = true; screen = EScreenID_SINGLE_REAR_RAW_DIAG; }
        // if (ImGui::Button("EScreenID_SINGLE_REAR_HITCH")) { screenDirty = true; screen = EScreenID_SINGLE_REAR_HITCH; }
        // if (ImGui::Button("EScreenID_SINGLE_REAR_HITCH_ZOOM")) { screenDirty = true; screen = EScreenID_SINGLE_REAR_HITCH_ZOOM; }
        // if (ImGui::Button("EScreenID_PARK_ASSIST_REAR")) { screenDirty = true; screen = EScreenID_PARK_ASSIST_REAR; }
        // if (ImGui::Button("EScreenID_SINGLE_REAR_TRAILER")) { screenDirty = true; screen = EScreenID_SINGLE_REAR_TRAILER; }
        // if (ImGui::Button("EScreenID_PERSPECTIVE_KL")) { screenDirty = true; screen = EScreenID_PERSPECTIVE_KL; }
        // if (ImGui::Button("EScreenID_PARK_ASSIST_FRONT_JAP")) { screenDirty = true; screen = EScreenID_PARK_ASSIST_FRONT_JAP; }
        // if (ImGui::Button("EScreenID_PARK_ASSIST_REAR_JAP")) { screenDirty = true; screen = EScreenID_PARK_ASSIST_REAR_JAP; }
        // if (ImGui::Button("EScreenID_SINGLE_ML_RAW")) { screenDirty = true; screen = EScreenID_SINGLE_ML_RAW; }
        // if (ImGui::Button("EScreenID_PERSPECTIVE_KR")) { screenDirty = true; screen = EScreenID_PERSPECTIVE_KR; }
        // if (ImGui::Button("EScreenID_SINGLE_LEFT")) { screenDirty = true; screen = EScreenID_SINGLE_LEFT; }
        // if (ImGui::Button("EScreenID_SINGLE_RIGHT")) { screenDirty = true; screen = EScreenID_SINGLE_RIGHT; }
        // if (ImGui::Button("EScreenID_SINGLE_MR_RAW")) { screenDirty = true; screen = EScreenID_SINGLE_MR_RAW; }
        // if (ImGui::Button("EScreenID_THREAT_FRONT")) { screenDirty = true; screen = EScreenID_THREAT_FRONT; }
        // if (ImGui::Button("EScreenID_THREAT_REAR")) { screenDirty = true; screen = EScreenID_THREAT_REAR; }
        // if (ImGui::Button("EScreenID_WHEEL_FRONT_DUAL")) { screenDirty = true; screen = EScreenID_WHEEL_FRONT_DUAL; }
        if (ImGui::Button("EScreenID_PERSPECTIVE_FL")) { screenDirty = true; screen = EScreenID_PERSPECTIVE_FL; }
        if (ImGui::Button("EScreenID_PERSPECTIVE_FR")) { screenDirty = true; screen = EScreenID_PERSPECTIVE_FR; }
        if (ImGui::Button("EScreenID_PERSPECTIVE_RL")) { screenDirty = true; screen = EScreenID_PERSPECTIVE_RL; }
        if (ImGui::Button("EScreenID_PERSPECTIVE_RR")) { screenDirty = true; screen = EScreenID_PERSPECTIVE_RR; }
        // if (ImGui::Button("EScreenID_DUAL_FRONT_ML")) { screenDirty = true; screen = EScreenID_DUAL_FRONT_ML; }
        // if (ImGui::Button("EScreenID_DUAL_FRONT_MR")) { screenDirty = true; screen = EScreenID_DUAL_FRONT_MR; }
        // if (ImGui::Button("EScreenID_PERSPECTIVE_PRE")) { screenDirty = true; screen = EScreenID_PERSPECTIVE_PRE; }
        // if (ImGui::Button("EScreenID_PERSPECTIVE_PLE")) { screenDirty = true; screen = EScreenID_PERSPECTIVE_PLE; }
        // if (ImGui::Button("EScreenID_DUAL_FRONT_JAP")) { screenDirty = true; screen = EScreenID_DUAL_FRONT_JAP; }
        // if (ImGui::Button("EScreenID_CAM_CALIB_ENG")) { screenDirty = true; screen = EScreenID_CAM_CALIB_ENG; }
        // if (ImGui::Button("EScreenID_DUAL_REAR_JAP")) { screenDirty = true; screen = EScreenID_DUAL_REAR_JAP; }
        // if (ImGui::Button("EScreenID_TOW_ASSIST_ENG")) { screenDirty = true; screen = EScreenID_TOW_ASSIST_ENG; }
        // if (ImGui::Button("EScreenID_LSM_ENG")) { screenDirty = true; screen = EScreenID_LSM_ENG; }
        // if (ImGui::Button("EScreenID_WHEEL_REAR_DUAL")) { screenDirty = true; screen = EScreenID_WHEEL_REAR_DUAL; }
        // if (ImGui::Button("EScreenID_LSMG_LSAEB_ENG")) { screenDirty = true; screen = EScreenID_LSMG_LSAEB_ENG; }
        // if (ImGui::Button("EScreenID_FULL_SCREEN")) { screenDirty = true; screen = EScreenID_FULL_SCREEN; }
        // if (ImGui::Button("EScreenID_TRIPLE_ML_FV_MR")) { screenDirty = true; screen = EScreenID_TRIPLE_ML_FV_MR; }
        // if (ImGui::Button("EScreenID_TRIPLE_REAR_ML_MR_TOW_ASSIST")) { screenDirty = true; screen = EScreenID_TRIPLE_REAR_ML_MR_TOW_ASSIST; }
        // if (ImGui::Button("EScreenID_QUAD_RAW")) { screenDirty = true; screen = EScreenID_QUAD_RAW; }
        // if (ImGui::Button("EScreenID_NO_VIDEO_SYSTEM")) { screenDirty = true; screen = EScreenID_NO_VIDEO_SYSTEM; }
        // if (ImGui::Button("EScreenID_PERSPECTIVE_PFR")) { screenDirty = true; screen = EScreenID_PERSPECTIVE_PFR; }
        // if (ImGui::Button("EScreenID_PERSPECTIVE_PRI")) { screenDirty = true; screen = EScreenID_PERSPECTIVE_PRI; }
        // if (ImGui::Button("EScreenID_BACK")) { screenDirty = true; screen = EScreenID_BACK; }
        // if (ImGui::Button("EScreenID_PRK_MODE_SELECT")) { screenDirty = true; screen = EScreenID_PRK_MODE_SELECT; }
        // if (ImGui::Button("EScreenID_PRK_SEARCHING")) { screenDirty = true; screen = EScreenID_PRK_SEARCHING; }
        // if (ImGui::Button("EScreenID_PRK_CONFIRMING")) { screenDirty = true; screen = EScreenID_PRK_CONFIRMING; }
        // if (ImGui::Button("EScreenID_VERT_SINGLE_FRONT")) { screenDirty = true; screen = EScreenID_VERT_SINGLE_FRONT; }
        // if (ImGui::Button("EScreenID_VERT_SINGLE_REAR")) { screenDirty = true; screen = EScreenID_VERT_SINGLE_REAR; }
        // if (ImGui::Button("EScreenID_VERT_SINGLE_FRONT_JUNCTION")) { screenDirty = true; screen = EScreenID_VERT_SINGLE_FRONT_JUNCTION; }
        // if (ImGui::Button("EScreenID_VERT_SINGLE_REAR_JUNCTION")) { screenDirty = true; screen = EScreenID_VERT_SINGLE_REAR_JUNCTION; }
        // if (ImGui::Button("EScreenID_VERT_SINGLE_LEFT")) { screenDirty = true; screen = EScreenID_VERT_SINGLE_LEFT; }
        // if (ImGui::Button("EScreenID_VERT_SINGLE_RIGHT")) { screenDirty = true; screen = EScreenID_VERT_SINGLE_RIGHT; }
        // if (ImGui::Button("EScreenID_VERT_WHEEL_FRONT_DUAL")) { screenDirty = true; screen = EScreenID_VERT_WHEEL_FRONT_DUAL; }
        // if (ImGui::Button("EScreenID_VERT_WHEEL_REAR_DUAL")) { screenDirty = true; screen = EScreenID_VERT_WHEEL_REAR_DUAL; }
        // if (ImGui::Button("EScreenID_VERT_PLANVIEW_WITH_SEPARATOR")) { screenDirty = true; screen = EScreenID_VERT_PLANVIEW_WITH_SEPARATOR; }
        // if (ImGui::Button("EScreenID_VERT_PERSPECTIVE_PFR")) { screenDirty = true; screen = EScreenID_VERT_PERSPECTIVE_PFR; }
        // if (ImGui::Button("EScreenID_VERT_PERSPECTIVE_PRE")) { screenDirty = true; screen = EScreenID_VERT_PERSPECTIVE_PRE; }
        // if (ImGui::Button("EScreenID_VERT_PERSPECTIVE_RL")) { screenDirty = true; screen = EScreenID_VERT_PERSPECTIVE_RL; }
        // if (ImGui::Button("EScreenID_VERT_PERSPECTIVE_RR")) { screenDirty = true; screen = EScreenID_VERT_PERSPECTIVE_RR; }
        // if (ImGui::Button("EScreenID_VERT_PERSPECTIVE_FL")) { screenDirty = true; screen = EScreenID_VERT_PERSPECTIVE_FL; }
        // if (ImGui::Button("EScreenID_VERT_PERSPECTIVE_FR")) { screenDirty = true; screen = EScreenID_VERT_PERSPECTIVE_FR; }
        // if (ImGui::Button("EScreenID_VERT_SINGLE_STB")) { screenDirty = true; screen = EScreenID_VERT_SINGLE_STB; }
        ImGui::Separator();
        if (ImGui::Button("EScreenID_HORI_PARKING_FRONT")) { screenDirty = true; screen = EScreenID_HORI_PARKING_FRONT; }
        ImGui::Separator();
        // if (ImGui::Button("EScreenID_HORI_PARKING_REAR")) { screenDirty = true; screen = EScreenID_HORI_PARKING_REAR; }
        // if (ImGui::Button("EScreenID_VERT_PARKING_FRONT")) { screenDirty = true; screen = EScreenID_VERT_PARKING_FRONT; }
        // if (ImGui::Button("EScreenID_VERT_PARKING_REAR")) { screenDirty = true; screen = EScreenID_VERT_PARKING_REAR; }
        // if (ImGui::Button("EScreenID_PLANVIEW_WITH_SEPARATOR")) { screenDirty = true; screen = EScreenID_PLANVIEW_WITH_SEPARATOR; }
        // if (ImGui::Button("EScreenID_REMOTE_SCREEN_FRONT")) { screenDirty = true; screen = EScreenID_REMOTE_SCREEN_FRONT; }
        // if (ImGui::Button("EScreenID_REMOTE_SCREEN_REAR")) { screenDirty = true; screen = EScreenID_REMOTE_SCREEN_REAR; }
        // if (ImGui::Button("EScreenID_REMOTE_SCREEN_LEFT")) { screenDirty = true; screen = EScreenID_REMOTE_SCREEN_LEFT; }
        // if (ImGui::Button("EScreenID_REMOTE_SCREEN_RIGHT")) { screenDirty = true; screen = EScreenID_REMOTE_SCREEN_RIGHT; }
        // if (ImGui::Button("EScreenID_LEFTRIGHTVIEW_FRONT_VIEW")) { screenDirty = true; screen = EScreenID_LEFTRIGHTVIEW_FRONT_VIEW; }
        // if (ImGui::Button("EScreenID_LEFTRIGHTVIEW_REAR_VIEW")) { screenDirty = true; screen = EScreenID_LEFTRIGHTVIEW_REAR_VIEW; }
        // if (ImGui::Button("EScreenID_DEBUG")) { screenDirty = true; screen = EScreenID_DEBUG; }
        // if (ImGui::Button("EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__LEFT_SIDE")) { screenDirty = true; screen = EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__LEFT_SIDE; }
        // if (ImGui::Button("EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__RIGHT_SIDE")) { screenDirty = true; screen = EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__RIGHT_SIDE; }
        // if (ImGui::Button("EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__LEFT_SIDE")) { screenDirty = true; screen = EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__LEFT_SIDE; }
        // if (ImGui::Button("EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__RIGHT_SIDE")) { screenDirty = true; screen = EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__RIGHT_SIDE; }
        // if (ImGui::Button("EScreenID_VERT_FULLSCREEN")) { screenDirty = true; screen = EScreenID_VERT_FULLSCREEN; }
        // if (ImGui::Button("EScreenID_VERT_FULLSCREEN_FRONT_ENLARGE")) { screenDirty = true; screen = EScreenID_VERT_FULLSCREEN_FRONT_ENLARGE; }
        // if (ImGui::Button("EScreenID_VERT_FULLSCREEN_REAR_ENLARGE")) { screenDirty = true; screen = EScreenID_VERT_FULLSCREEN_REAR_ENLARGE; }
        // if (ImGui::Button("EScreenID_VERT_PLAN__DUAL_FRONT_WHEEL__FRONT_SIDE")) { screenDirty = true; screen = EScreenID_VERT_PLAN__DUAL_FRONT_WHEEL__FRONT_SIDE; }
        // if (ImGui::Button("EScreenID_VERT_PLAN__DUAL_FRONT_WHEEL__REAR_SIDE")) { screenDirty = true; screen = EScreenID_VERT_PLAN__DUAL_FRONT_WHEEL__REAR_SIDE; }
        // if (ImGui::Button("EScreenID_VERT_PLAN__DUAL_REAR_WHEEL__FRONT_SIDE")) { screenDirty = true; screen = EScreenID_VERT_PLAN__DUAL_REAR_WHEEL__FRONT_SIDE; }
        // if (ImGui::Button("EScreenID_VERT_PLAN__DUAL_REAR_WHEEL__REAR_SIDE")) { screenDirty = true; screen = EScreenID_VERT_PLAN__DUAL_REAR_WHEEL__REAR_SIDE; }
        // if (ImGui::Button("EScreenID_VERT_PLAN__DUAL_WHEEL__FRONT_SIDE")) { screenDirty = true; screen = EScreenID_VERT_PLAN__DUAL_WHEEL__FRONT_SIDE; }
        // if (ImGui::Button("EScreenID_VERT_PLAN__DUAL_WHEEL__REAR_SIDE")) { screenDirty = true; screen = EScreenID_VERT_PLAN__DUAL_WHEEL__REAR_SIDE; }
        // if (ImGui::Button("EScreenID_VERT_PERSPECTIVE_FL_R")) { screenDirty = true; screen = EScreenID_VERT_PERSPECTIVE_FL_R; }
        // if (ImGui::Button("EScreenID_VERT_PERSPECTIVE_FL_L")) { screenDirty = true; screen = EScreenID_VERT_PERSPECTIVE_FL_L; }
        // if (ImGui::Button("EScreenID_VERT_PERSPECTIVE_FR_R")) { screenDirty = true; screen = EScreenID_VERT_PERSPECTIVE_FR_R; }
        // if (ImGui::Button("EScreenID_VERT_PERSPECTIVE_FR_L")) { screenDirty = true; screen = EScreenID_VERT_PERSPECTIVE_FR_L; }
        // if (ImGui::Button("EScreenID_VERT_PERSPECTIVE_RL_R")) { screenDirty = true; screen = EScreenID_VERT_PERSPECTIVE_RL_R; }
        // if (ImGui::Button("EScreenID_VERT_PERSPECTIVE_RL_L")) { screenDirty = true; screen = EScreenID_VERT_PERSPECTIVE_RL_L; }
        // if (ImGui::Button("EScreenID_VERT_PERSPECTIVE_RR_R")) { screenDirty = true; screen = EScreenID_VERT_PERSPECTIVE_RR_R; }
        // if (ImGui::Button("EScreenID_VERT_PERSPECTIVE_RR_L")) { screenDirty = true; screen = EScreenID_VERT_PERSPECTIVE_RR_L; }
        // if (ImGui::Button("EScreenID_VERT_2D_FRONTVIEW_RIGHTVIEW")) { screenDirty = true; screen = EScreenID_VERT_2D_FRONTVIEW_RIGHTVIEW; }
        // if (ImGui::Button("EScreenID_VERT_2D_FRONTVIEW_LEFTVIEW")) { screenDirty = true; screen = EScreenID_VERT_2D_FRONTVIEW_LEFTVIEW; }
        // if (ImGui::Button("EScreenID_VERT_2D_REARVIEW_RIGHTVIEW")) { screenDirty = true; screen = EScreenID_VERT_2D_REARVIEW_RIGHTVIEW; }
        // if (ImGui::Button("EScreenID_VERT_2D_REARVIEW_LEFTVIEW")) { screenDirty = true; screen = EScreenID_VERT_2D_REARVIEW_LEFTVIEW; }
        // if (ImGui::Button("EScreenID_MODEL_F_VIEW_ENLARGEMENT")) { screenDirty = true; screen = EScreenID_MODEL_F_VIEW_ENLARGEMENT; }
        // if (ImGui::Button("EScreenID_MODEL_B_VIEW_ENLARGEMENT")) { screenDirty = true; screen = EScreenID_MODEL_B_VIEW_ENLARGEMENT; }
        // if (ImGui::Button("EScreenID_FULLSCREEN")) { screenDirty = true; screen = EScreenID_FULLSCREEN; }
        // if (ImGui::Button("EScreenID_FULLSCREEN_FRONT_ENLARGE")) { screenDirty = true; screen = EScreenID_FULLSCREEN_FRONT_ENLARGE; }
        // if (ImGui::Button("EScreenID_FULLSCREEN_REAR_ENLARGE")) { screenDirty = true; screen = EScreenID_FULLSCREEN_REAR_ENLARGE; }
        if (ImGui::Button("EScreenID_FRONT_BUMPER")) { screenDirty = true; screen = EScreenID_FRONT_BUMPER; }
        if (ImGui::Button("EScreenID_REAR_BUMPER")) { screenDirty = true; screen = EScreenID_REAR_BUMPER; }
        if (ImGui::Button("EScreenID_ULTRA_WIDE_SURROUND_VIEW")) { screenDirty = true; screen = EScreenID_ULTRA_WIDE_SURROUND_VIEW; }
        if (ImGui::Button("EScreenID_PLANETARY_VIEW")) { screenDirty = true; screen = EScreenID_PLANETARY_VIEW; }
        if (ImGui::Button("EscreenID_FULL_SCREEN_3D")) { screenDirty = true; screen = EscreenID_FULL_SCREEN_3D; }
        if (ImGui::Button("EScreenID_NORMAL3D_KEEP")) { screenDirty = true; screen = EScreenID_NORMAL3D_KEEP; }
        if (ImGui::Button("EScreenID_FULLSCREEN")) { screenDirty = true; screen = EScreenID_FULLSCREEN; }
        if (ImGui::Button("EScreenID_FULLSCREEN_FRONT_ENLARGE")) { screenDirty = true; screen = EScreenID_FULLSCREEN_FRONT_ENLARGE; }
        if (ImGui::Button("EScreenID_FULLSCREEN_REAR_ENLARGE")) { screenDirty = true; screen = EScreenID_FULLSCREEN_REAR_ENLARGE; }
        if (ImGui::Button("EScreenID_REMOTE_SCREEN_FRONT")) { screenDirty = true; screen = EScreenID_REMOTE_SCREEN_FRONT; }

        ImGui::Separator();
        if (ImGui::Button("EScreenID_IMAGE_IN_IMAGE_LEFT")) { screenDirty = true; screen = EScreenID_IMAGE_IN_IMAGE_LEFT; }
        if (ImGui::Button("EScreenID_IMAGE_IN_IMAGE_RIGHT")) { screenDirty = true; screen = EScreenID_IMAGE_IN_IMAGE_RIGHT; }
        if (ImGui::Button("EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT")) { screenDirty = true; screen = EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT; }
        if (ImGui::Button("EScreenID_IMAGE_IN_IMAGE_PLANVIEW")) { screenDirty = true; screen = EScreenID_IMAGE_IN_IMAGE_PLANVIEW; }
        if (ImGui::Button("EScreenID_IMAGE_IN_IMAGE_PLANVIEW_FRONT_ENLARGE")) { screenDirty = true; screen = EScreenID_IMAGE_IN_IMAGE_PLANVIEW_FRONT_ENLARGE; }
        if (ImGui::Button("EScreenID_IMAGE_IN_IMAGE_PLANVIEW_REAR_ENLARGE")) { screenDirty = true; screen = EScreenID_IMAGE_IN_IMAGE_PLANVIEW_REAR_ENLARGE; }
        if (ImGui::Button("EScreenID_PARKING_TOP")) { screenDirty = true; screen = EScreenID_PARKING_TOP; }
        if (ImGui::Button("EScreenID_PARKING_FRONT")) { screenDirty = true; screen = EScreenID_PARKING_FRONT; }
        if (ImGui::Button("EScreenID_PARKING_REAR")) { screenDirty = true; screen = EScreenID_PARKING_REAR; }
        if (ImGui::Button("EScreenID_PARKING_FREEPARKING")) { screenDirty = true; screen = EScreenID_PARKING_FREEPARKING; }
        ImGui::Separator();

        if (ImGui::Button("EScreenID_UPVIEWCOFIG_WHEEL_FRONT_DUAL")) { screenDirty = true; screen = EScreenID_UPVIEWCOFIG_WHEEL_FRONT_DUAL; }
        if (ImGui::Button("EScreenID_UPVIEWCOFIG_WHEEL_REAR_DUAL")) { screenDirty = true; screen = EScreenID_UPVIEWCOFIG_WHEEL_REAR_DUAL; }
        if (ImGui::Button("EScreenID_UPVIEWCOFIG_WHEEL_BOTH")) { screenDirty = true; screen = EScreenID_UPVIEWCOFIG_WHEEL_BOTH; }
        if (ImGui::Button("EScreenID_WHEEL_FRONT_DUAL")) { screenDirty = true; screen = EScreenID_WHEEL_FRONT_DUAL; }
        if (ImGui::Button("EScreenID_WHEEL_REAR_DUAL")) { screenDirty = true; screen = EScreenID_WHEEL_REAR_DUAL; }
        if (ImGui::Button("EScreenID_PLANVIEW_WITH_SEPARATOR")) { screenDirty = true; screen = EScreenID_PLANVIEW_WITH_SEPARATOR; }
        // cc::daddy::HUDislayModeSwitchDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.reserve();
        // l_container.m_Data          = static_cast<vfc::uint8_t>(34);
        // cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.deliver();

        if (screenDirty)
        {
            // cc::daddy::SVSDisplayedViewDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_SVSDisplayedViewDaddy_SenderPort.reserve();
            // l_container.m_Data = static_cast<EScreenID>(screen);
            // cc::daddy::CustomDaddyPorts::sm_SVSDisplayedViewDaddy_SenderPort.deliver() ;
            cc::daddy::HUDislayModeSwitchDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.reserve();
            l_container.m_Data          = static_cast<vfc::uint8_t>(screen);
            cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.deliver();
        }

        static const std::array<int, 12> frontIndex = {5, 4, 3, 2, 1, 0, 39, 38, 37, 36, 35, 34};

        ImGui::Separator();
        //! FRONT USS
        static int FrontLeftUss = 150;
        if (ImGui::SliderInt("FrontLeftUss", &FrontLeftUss, 0, 150))
        {
            pc::daddy::UltrasonicDataDaddy& l_ussData =
                pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.reserveLastDelivery();
            std::array<int, 3> frontLeftIndex = {5, 4, 3};
            for (int i = 0; i < 3; i++)
            {
                int l_zoneIndex = frontLeftIndex[i];
                l_ussData.m_Data[l_zoneIndex].setDistance(static_cast<float>(FrontLeftUss) * 0.01f);
                l_ussData.m_Data[l_zoneIndex].setOnPath(false);
                l_ussData.m_Data[l_zoneIndex].setFlags(0);
            }
            pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.deliver();
        }

        static int FrontMiddleLeftUss = 150;
        if (ImGui::SliderInt("FrontMiddleLeftUss", &FrontMiddleLeftUss, 0, 150))
        {
            pc::daddy::UltrasonicDataDaddy& l_ussData =
                pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.reserveLastDelivery();
            std::array<int, 3> FrontRightIndex = {2, 1, 0};
            for (int i = 0; i < 3; i++)
            {
                int l_zoneIndex = FrontRightIndex[i];
                l_ussData.m_Data[l_zoneIndex].setDistance(static_cast<float>(FrontMiddleLeftUss) * 0.01f);
                l_ussData.m_Data[l_zoneIndex].setOnPath(false);
                l_ussData.m_Data[l_zoneIndex].setFlags(0);
            }
            pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.deliver();
        }

        static int FrontMiddleRightUss = 150;
        if (ImGui::SliderInt("FrontMiddleRightUss", &FrontMiddleRightUss, 0, 150))
        {
            pc::daddy::UltrasonicDataDaddy& l_ussData =
                pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.reserveLastDelivery();
            std::array<int, 3> FrontRightIndex = {39, 38, 37};
            for (int i = 0; i < 3; i++)
            {
                int l_zoneIndex = FrontRightIndex[i];
                l_ussData.m_Data[l_zoneIndex].setDistance(static_cast<float>(FrontMiddleRightUss) * 0.01f);
                l_ussData.m_Data[l_zoneIndex].setOnPath(false);
                l_ussData.m_Data[l_zoneIndex].setFlags(0);
            }
            pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.deliver();
        }

        static int FrontRightUss = 150;
        if (ImGui::SliderInt("FrontRightUss", &FrontRightUss, 0, 150))
        {
            pc::daddy::UltrasonicDataDaddy& l_ussData =
                pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.reserveLastDelivery();
            std::array<int, 3> FrontRightIndex = {36, 35, 34};
            for (int i = 0; i < 3; i++)
            {
                int l_zoneIndex = FrontRightIndex[i];
                l_ussData.m_Data[l_zoneIndex].setDistance(static_cast<float>(FrontRightUss) * 0.01f);
                l_ussData.m_Data[l_zoneIndex].setOnPath(false);
                l_ussData.m_Data[l_zoneIndex].setFlags(0);
            }
            pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.deliver();
        }

        ImGui::Separator();
        static const std::array<int, 12> rearIndex = {14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25};
        //! REAR USS
        static int RearLeftUss = 150;
        if (ImGui::SliderInt("RearLeftUss", &RearLeftUss, 0, 150))
        {
            pc::daddy::UltrasonicDataDaddy& l_ussData =
                pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.reserveLastDelivery();
            std::array<int, 3> RearLeftIndex = {14, 15, 16};
            for (int i = 0; i < 3; i++)
            {
                int l_zoneIndex = RearLeftIndex[i];
                l_ussData.m_Data[l_zoneIndex].setDistance(static_cast<float>(RearLeftUss) * 0.01f);
                l_ussData.m_Data[l_zoneIndex].setOnPath(false);
                l_ussData.m_Data[l_zoneIndex].setFlags(0);
            }
            pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.deliver();
        }

        static int RearMiddleLeftUss = 150;
        if (ImGui::SliderInt("RearMiddleLeftUss", &RearMiddleLeftUss, 0, 150))
        {
            pc::daddy::UltrasonicDataDaddy& l_ussData =
                pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.reserveLastDelivery();
            std::array<int, 3> RearRightIndex = {17, 18, 19};
            for (int i = 0; i < 3; i++)
            {
                int l_zoneIndex = RearRightIndex[i];
                l_ussData.m_Data[l_zoneIndex].setDistance(static_cast<float>(RearMiddleLeftUss) * 0.01f);
                l_ussData.m_Data[l_zoneIndex].setOnPath(false);
                l_ussData.m_Data[l_zoneIndex].setFlags(0);
            }
            pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.deliver();
        }

        static int RearMiddleRightUss = 150;
        if (ImGui::SliderInt("RearMiddleRightUss", &RearMiddleRightUss, 0, 150))
        {
            pc::daddy::UltrasonicDataDaddy& l_ussData =
                pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.reserveLastDelivery();
            std::array<int, 3> RearRightIndex = {20, 21, 22};
            for (int i = 0; i < 3; i++)
            {
                int l_zoneIndex = RearRightIndex[i];
                l_ussData.m_Data[l_zoneIndex].setDistance(static_cast<float>(RearMiddleRightUss) * 0.01f);
                l_ussData.m_Data[l_zoneIndex].setOnPath(false);
                l_ussData.m_Data[l_zoneIndex].setFlags(0);
            }
            pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.deliver();
        }

        static int RearRightUss = 150;
        if (ImGui::SliderInt("RearRightUss", &RearRightUss, 0, 150))
        {
            pc::daddy::UltrasonicDataDaddy& l_ussData =
                pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.reserveLastDelivery();
            std::array<int, 3> RearRightIndex = {23, 24, 25};
            for (int i = 0; i < 3; i++)
            {
                int l_zoneIndex = RearRightIndex[i];
                l_ussData.m_Data[l_zoneIndex].setDistance(static_cast<float>(RearRightUss) * 0.01f);
                l_ussData.m_Data[l_zoneIndex].setOnPath(false);
                l_ussData.m_Data[l_zoneIndex].setFlags(0);
            }
            pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.deliver();
        }

        ImGui::Separator();
        //! RIGHT USS
        static int RightLeftUss = 150;
        if (ImGui::SliderInt("RightLeftUss", &RightLeftUss, 0, 150))
        {
            pc::daddy::UltrasonicDataDaddy& l_ussData =
                pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.reserveLastDelivery();
            std::array<int, 2> RightLeftIndex = {26, 27};
            for (int i = 0; i < 2; i++)
            {
                int l_zoneIndex = RightLeftIndex[i];
                l_ussData.m_Data[l_zoneIndex].setDistance(static_cast<float>(RightLeftUss) * 0.01f);
                l_ussData.m_Data[l_zoneIndex].setOnPath(false);
                l_ussData.m_Data[l_zoneIndex].setFlags(0);
            }
            pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.deliver();
        }

        static int RightMiddleLeftUss = 150;
        if (ImGui::SliderInt("RightMiddleLeftUss", &RightMiddleLeftUss, 0, 150))
        {
            pc::daddy::UltrasonicDataDaddy& l_ussData =
                pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.reserveLastDelivery();
            std::array<int, 2> RightRightIndex = {28, 29};
            for (int i = 0; i < 2; i++)
            {
                int l_zoneIndex = RightRightIndex[i];
                l_ussData.m_Data[l_zoneIndex].setDistance(static_cast<float>(RightMiddleLeftUss) * 0.01f);
                l_ussData.m_Data[l_zoneIndex].setOnPath(false);
                l_ussData.m_Data[l_zoneIndex].setFlags(0);
            }
            pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.deliver();
        }

        static int RightMiddleRightUss = 150;
        if (ImGui::SliderInt("RightMiddleRightUss", &RightMiddleRightUss, 0, 150))
        {
            pc::daddy::UltrasonicDataDaddy& l_ussData =
                pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.reserveLastDelivery();
            std::array<int, 2> RightRightIndex = {30, 31};
            for (int i = 0; i < 2; i++)
            {
                int l_zoneIndex = RightRightIndex[i];
                l_ussData.m_Data[l_zoneIndex].setDistance(static_cast<float>(RightMiddleRightUss) * 0.01f);
                l_ussData.m_Data[l_zoneIndex].setOnPath(false);
                l_ussData.m_Data[l_zoneIndex].setFlags(0);
            }
            pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.deliver();
        }

        static int RightRightUss = 150;
        if (ImGui::SliderInt("RightRightUss", &RightRightUss, 0, 150))
        {
            pc::daddy::UltrasonicDataDaddy& l_ussData =
                pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.reserveLastDelivery();
            std::array<int, 2> RightRightIndex = {32, 33};
            for (int i = 0; i < 2; i++)
            {
                int l_zoneIndex = RightRightIndex[i];
                l_ussData.m_Data[l_zoneIndex].setDistance(static_cast<float>(RightRightUss) * 0.01f);
                l_ussData.m_Data[l_zoneIndex].setOnPath(false);
                l_ussData.m_Data[l_zoneIndex].setFlags(0);
            }
            pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.deliver();
        }

        ImGui::Separator();
        //! LEFT USS
        static int LeftLeftUss = 150;
        if (ImGui::SliderInt("LeftLeftUss", &LeftLeftUss, 0, 150))
        {
            pc::daddy::UltrasonicDataDaddy& l_ussData =
                pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.reserveLastDelivery();
            std::array<int, 2> LeftLeftIndex = {6, 7};
            for (int i = 0; i < 2; i++)
            {
                int l_zoneIndex = LeftLeftIndex[i];
                l_ussData.m_Data[l_zoneIndex].setDistance(static_cast<float>(LeftLeftUss) * 0.01f);
                l_ussData.m_Data[l_zoneIndex].setOnPath(false);
                l_ussData.m_Data[l_zoneIndex].setFlags(0);
            }
            pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.deliver();
        }

        static int LeftMiddleLeftUss = 150;
        if (ImGui::SliderInt("LeftMiddleLeftUss", &LeftMiddleLeftUss, 0, 150))
        {
            pc::daddy::UltrasonicDataDaddy& l_ussData =
                pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.reserveLastDelivery();
            std::array<int, 2> LeftIndex = {8, 9};
            for (int i = 0; i < 2; i++)
            {
                int l_zoneIndex = LeftIndex[i];
                l_ussData.m_Data[l_zoneIndex].setDistance(static_cast<float>(LeftMiddleLeftUss) * 0.01f);
                l_ussData.m_Data[l_zoneIndex].setOnPath(false);
                l_ussData.m_Data[l_zoneIndex].setFlags(0);
            }
            pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.deliver();
        }

        static int LeftMiddleRightUss = 150;
        if (ImGui::SliderInt("LeftMiddleRightUss", &LeftMiddleRightUss, 0, 150))
        {
            pc::daddy::UltrasonicDataDaddy& l_ussData =
                pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.reserveLastDelivery();
            std::array<int, 2> LeftIndex = {10, 11};
            for (int i = 0; i < 2; i++)
            {
                int l_zoneIndex = LeftIndex[i];
                l_ussData.m_Data[l_zoneIndex].setDistance(static_cast<float>(LeftMiddleRightUss) * 0.01f);
                l_ussData.m_Data[l_zoneIndex].setOnPath(false);
                l_ussData.m_Data[l_zoneIndex].setFlags(0);
            }
            pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.deliver();
        }

        static int LeftRightUss = 150;
        if (ImGui::SliderInt("LeftRightUss", &LeftRightUss, 0, 150))
        {
            pc::daddy::UltrasonicDataDaddy& l_ussData =
                pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.reserveLastDelivery();
            std::array<int, 2> LeftIndex = {12, 13};
            for (int i = 0; i < 2; i++)
            {
                int l_zoneIndex = LeftIndex[i];
                l_ussData.m_Data[l_zoneIndex].setDistance(static_cast<float>(LeftRightUss) * 0.01f);
                l_ussData.m_Data[l_zoneIndex].setOnPath(false);
                l_ussData.m_Data[l_zoneIndex].setFlags(0);
            }
            pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.deliver();
        }

        static int s_azimuth = 0;
        if (ImGui::SliderInt("Azimuth", &s_azimuth, 0, 360))
        {
            cc::daddy::HUFreemodeAngleDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleAzimuthDaddy_SenderPort.reserve();
            l_container.m_Data          = static_cast<vfc::uint32_t>(s_azimuth);
            cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleAzimuthDaddy_SenderPort.deliver();
        }

        static int s_elevation = 0;
        if (ImGui::SliderInt("elevation", &s_elevation, 0, 360))
        {
            cc::daddy::HUFreemodeAngleDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleElevationDaddy_SenderPort.reserve();
            l_container.m_Data          = static_cast<vfc::uint32_t>(s_elevation);
            cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleElevationDaddy_SenderPort.deliver();
        }


        //! Vehicle Color
        if (ImGui::TreeNode("VehicleColor"))
        {
            static int selectedColor = -1;
            const char* colorArray[] = {
                "SILVERSAND_BLACK",
                "XUANKONG_BLACK",
                "AZURE",
                "WISDOM_BLUE",
                "TIME_GRAY",
                "MOUNTAIN_ASH",
                "MUSHAN_PINK",
                "QIANSHAN_CUI",
                "RED_EMPEROR",
                "SNOWY_WHITE",
                "SILVER_GLAZE_WHITE",
                "DU_DU_WHITE"
            };
            static const int numColors = IM_ARRAYSIZE(colorArray);
            bool dirty = false;
            for (int i = 0; i < numColors; i++)
            {
                if (ImGui::Selectable(colorArray[i], selectedColor == i))
                {
                    selectedColor = i;
                    dirty = true;
                }
            }
            if (dirty)
            {
                cc::daddy::EColorCode color = cc::daddy::INVALID;
                switch (selectedColor)
                {
                    default: color = cc::daddy::TIME_GRAY; break;
                    case 0:  color = cc::daddy::SILVERSAND_BLACK; break;
                    case 1:  color = cc::daddy::XUANKONG_BLACK; break;
                    case 2:  color = cc::daddy::AZURE; break;
                    case 3:  color = cc::daddy::WISDOM_BLUE; break;
                    case 4:  color = cc::daddy::TIME_GRAY; break;
                    case 5:  color = cc::daddy::MOUNTAIN_ASH; break;
                    case 6:  color = cc::daddy::MUSHAN_PINK; break;
                    case 7:  color = cc::daddy::QIANSHAN_CUI; break;
                    case 8:  color = cc::daddy::RED_EMPEROR; break;
                    case 9:  color = cc::daddy::SNOWY_WHITE; break;
                    case 10: color = cc::daddy::SILVER_GLAZE_WHITE; break;
                    case 11: color = cc::daddy::DU_DU_WHITE; break;
                }

                cc::daddy::ColorIndexDaddy& l_container = cc::daddy::CustomDaddyPorts::sm_VehicleDiffuseColorIndex_SenderPort.reserve();
                l_container.m_Data = static_cast<vfc::uint8_t>(color);
                cc::daddy::CustomDaddyPorts::sm_VehicleDiffuseColorIndex_SenderPort.deliver();
            }
            ImGui::TreePop();
        }

    }
    ImGui::End();
}

void ImGuiView::parkingCommands()
{
    using cc::daddy::CustomDaddyPorts;
    if (ImGui::Begin("ParkingCommands"))
    {
        bool               dirty              = false;

        static int s_ApaStatus;
        {
            static const char* s_ApaStatusItems[] = {
                "PassiveStandBy",
                "Searching",
                "GuidanceActive",
                "GuidanceSuspend",
                "GuidanceTerminate",
                "GuidanceCompleted",
                "AutomaticParkingIsNotAvailable",
                "ParkAssistStandby"};
            dirty |= ImGui::ListBox(
                "ApaStatus",
                &s_ApaStatus,
                s_ApaStatusItems,
                IM_ARRAYSIZE(s_ApaStatusItems),
                IM_ARRAYSIZE(s_ApaStatusItems));
        }
        static int s_specialParking = 0;
        {
            using namespace pc::daddy;
            dirty |= ImGui::RadioButton("SpecialParking_OFF", &s_specialParking, static_cast<int>(cc::target::common::ESpecialParkingStatus::OFF)); ImGui::SameLine();
            dirty |= ImGui::RadioButton("SpecialParking_ON", &s_specialParking, static_cast<int>(cc::target::common::ESpecialParkingStatus::ON));
        }
        static int s_rotation = 0;
        {
            using namespace pc::daddy;
            dirty |= ImGui::RadioButton("LF_ANTI", &s_rotation, static_cast<int>(cc::target::common::ERotationDirection::LEFT_FRONT_WHEEL_ANTICLOCKWISE)); ImGui::SameLine();
            dirty |= ImGui::RadioButton("RF_CLOCK", &s_rotation, static_cast<int>(cc::target::common::ERotationDirection::RIGHT_FRONT_WHEEL_CLOCKWISE)); ImGui::SameLine();
            dirty |= ImGui::RadioButton("LR_CLOCK", &s_rotation, static_cast<int>(cc::target::common::ERotationDirection::LEFT_REAR_WHEEL_CLOCKWISE)); ImGui::SameLine();
            dirty |= ImGui::RadioButton("RR_ANTI", &s_rotation, static_cast<int>(cc::target::common::ERotationDirection::RIGHT_REAR_WHEEL_ANTICLOCKWISE)); ImGui::SameLine();
            dirty |= ImGui::RadioButton("Right_Parallel", &s_rotation, static_cast<int>(cc::target::common::ERotationDirection::RIGHT_PARALLEL));

        }
        static int s_ParkingStage;
        {
            static const char* s_ParkingStageItems[] = {
                "Invalid",
                "ParkIn",
                "ParkOut",
                "FreeParking"
            };
            dirty |= ImGui::ListBox(
                "ParkingStage",
                &s_ParkingStage,
                s_ParkingStageItems,
                IM_ARRAYSIZE(s_ParkingStageItems),
                IM_ARRAYSIZE(s_ParkingStageItems));
        }
        static int s_ParkingMode;
        {
            static const char* s_ParkingModeItems[] = {
                "Invalid",
                "Apa",
                "Hpa",
                "Avp"
            };
            dirty |= ImGui::ListBox(
                "ParkingMode",
                &s_ParkingMode,
                s_ParkingModeItems,
                IM_ARRAYSIZE(s_ParkingModeItems),
                IM_ARRAYSIZE(s_ParkingModeItems));
        }
        static int s_ParkInDirection;
        {
            static const char* s_ParkInDirectionItems[] = {
                "Unknown",
                "Left",
                "Right",
                "Vertical"
            };
            dirty |= ImGui::ListBox(
                "ParkInDirection",
                &s_ParkInDirection,
                s_ParkInDirectionItems,
                IM_ARRAYSIZE(s_ParkInDirectionItems),
                IM_ARRAYSIZE(s_ParkInDirectionItems));
        }
        static bool s_FrntLeParallelSts = false;
        static bool s_FrntLeCrossSts = false;
        static bool s_FrntRiParallelSts = false;
        static bool s_FrntRiCrossSts = false;
        static bool s_FrntCrossSts = false;
        static bool s_BackCrossSts = false;
        static bool s_BackLeCrossSts = false;
        static bool s_BackRiCrossSts = false;
        {
            dirty |= ImGui::Checkbox("FrntLeParallelSts", &s_FrntLeParallelSts);
            dirty |= ImGui::Checkbox("FrntLeCrossSts", &s_FrntLeCrossSts);
            dirty |= ImGui::Checkbox("FrntRiParallelSts", &s_FrntRiParallelSts);
            dirty |= ImGui::Checkbox("FrntRiCrossSts", &s_FrntRiCrossSts);
            dirty |= ImGui::Checkbox("FrntCrossSts", &s_FrntCrossSts);
            dirty |= ImGui::Checkbox("BackCrossSts", &s_BackCrossSts);
            dirty |= ImGui::Checkbox("BackLeCrossSts", &s_BackLeCrossSts);
            dirty |= ImGui::Checkbox("BackRiCrossSts", &s_BackRiCrossSts);
        }
        static int s_PocRecommandDir;
        {
            static const char* s_PocRecommandDirItems[] = {
                "None",
                "FrontLeftCross",
                "FrontLeftParallel",
                "FrontOut",
                "FrontRightCross",
                "FrontRightParallel",
                "BackOut",
                "BackLeftCross",
                "BackRightCross",
                "Reserved",
            };
            dirty |= ImGui::ListBox(
                "PocRecommandDir",
                &s_PocRecommandDir,
                s_PocRecommandDirItems,
                IM_ARRAYSIZE(s_PocRecommandDirItems),
                IM_ARRAYSIZE(s_PocRecommandDirItems));
        }

        //! FREE PARKING
        // static bool s_FreeParkingIs360FreeParking = false;
        static int  s_FreeParkingIsSlotState = 0;
        static vfc::int32_t ctr = 0;
            // dirty |= ImGui::Checkbox("FreeParkingIs360FreeParking", &s_FreeParkingIs360FreeParking);
            static const char* s_FreeParkingIsSlotStateItems[] = {
                "UNAVAILABLE",
                "AVAILABLE",
                "UNAVAILABLE_BARRIER",
            };
            dirty |= ImGui::ListBox(
                "FreeParkingSlotState",
                &s_FreeParkingIsSlotState,
                s_FreeParkingIsSlotStateItems,
                IM_ARRAYSIZE(s_FreeParkingIsSlotStateItems),
                IM_ARRAYSIZE(s_FreeParkingIsSlotStateItems));
        
        // static int s_FreeParkingStage;
        // {
        //     static const char* s_FreeParkingStageItems[] = {
        //         "None",
        //         "InFpStandstill",
        //         "GuidanceStart",
        //         "GuidanceFinish",
        //         "InFpMoving"
        //     };
        //     dirty |= ImGui::ListBox(
        //         "FreeParkingStage",
        //         &s_FreeParkingStage,
        //         s_FreeParkingStageItems,
        //         IM_ARRAYSIZE(s_FreeParkingStageItems),
        //         IM_ARRAYSIZE(s_FreeParkingStageItems));
        // }
        // static int s_FreeParkingSlotType;
        // {
        //     static const char* s_FreeParkingSlotTypeItems[] = {
        //         "HorizontalSlot",
        //         "VerticalSlot",
        //         "DiagonalSlot"
        //     };
        //     dirty |= ImGui::ListBox(
        //         "FreeParkingSlotType",
        //         &s_FreeParkingSlotType,
        //         s_FreeParkingSlotTypeItems,
        //         IM_ARRAYSIZE(s_FreeParkingSlotTypeItems),
        //         IM_ARRAYSIZE(s_FreeParkingSlotTypeItems));
        // }
        if (dirty)
        {
            if (cc::daddy::CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort.isConnected())
            {
                using namespace cc::target::common;
                auto& container = cc::daddy::CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort.reserveLastDelivery();
                // container.m_Data.m_apaStatus = static_cast<EApaStatus>(s_ApaStatus);
                // container.m_Data.m_parkingStage = static_cast<EParkingStage>(s_ParkingStage);
                // container.m_Data.m_parkMode = static_cast<EParkingMode>(s_ParkingMode);
                // container.m_Data.m_parkInDirection = static_cast<EParkInDirection>(s_ParkInDirection);
                // container.m_Data.m_parkingRealTimeData = {};
                // container.m_Data.m_pocEnabledDir.m_FrntLeParallelSts = s_FrntLeParallelSts;
                // container.m_Data.m_pocEnabledDir.m_FrntLeCrossSts = s_FrntLeCrossSts;
                // container.m_Data.m_pocEnabledDir.m_FrntRiParallelSts = s_FrntRiParallelSts;
                // container.m_Data.m_pocEnabledDir.m_FrntRiCrossSts = s_FrntRiCrossSts;
                // container.m_Data.m_pocEnabledDir.m_FrntCrossSts = s_FrntCrossSts;
                // container.m_Data.m_pocEnabledDir.m_BackCrossSts = s_BackCrossSts;
                // container.m_Data.m_pocEnabledDir.m_BackLeCrossSts = s_BackLeCrossSts;
                // container.m_Data.m_pocEnabledDir.m_BackRiCrossSts = s_BackRiCrossSts;
                // container.m_Data.m_pocRecommandDir = static_cast<EPocDirSel>(s_PocRecommandDir);
                // container.m_Data.m_freeParkingIn.m_is360FreeParking = s_FreeParkingIs360FreeParking;
                container.m_Data.m_freeParkingIn.m_isSlotParkable = static_cast<cc::target::common::EFreeParkingSlotState>(s_FreeParkingIsSlotState);
                container.m_Data.m_freeParkingIn.m_ctr = ++ctr;
                // container.m_Data.m_freeParkingIn.m_parkStage = static_cast<EFreeParkingStage>(s_FreeParkingStage);
                // container.m_Data.m_freeParkingIn.m_slotType = static_cast<EFreeParkingSlotType>(s_FreeParkingSlotType);
                container.m_Data.m_specialParkingStatus = static_cast<ESpecialParkingStatus>(s_specialParking);
                container.m_Data.m_rotationDirection = static_cast<ERotationDirection>(s_rotation);
                // container.m_Data.m_targetSlotPosition = {};  // target slot in guidance
                cc::daddy::CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort.deliver();
            }
        }

        static int s_AVMParkMode;
        {
            static const char* s_AVMParkModeItems[] = {
                "defalut",
                "AVM_PARK_360",
                "AVM_PARK_LIMITED_HORIZONTAL",
                "AVM_PARK_LIMITED_VERTICAL",
                "AVM_PARK_LIMITED_OBLIQUE",
                "AVM_PARK_PARKING_START",
                "AVM_PARK_PARKING_FINISH",
                "AVM_PARK_NON_STATIONARY"
            };
            dirty |= ImGui::ListBox(
                "AVMParkMode",
                &s_AVMParkMode,
                s_AVMParkModeItems,
                IM_ARRAYSIZE(s_AVMParkModeItems),
                IM_ARRAYSIZE(s_AVMParkModeItems));
        }
        if (dirty)
        {
            if (cc::daddy::CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort.isConnected())
            {
                using namespace cc::target::common;
                auto& container = cc::daddy::CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort.reserveLastDelivery();
                switch (s_AVMParkMode)
                {
                case 1:
                    container.m_Data.m_freeParkingIn.m_parkStage =
                        cc::target::common::EFreeParkingStage::InFpStandstill;
                    container.m_Data.m_parkingStage = cc::target::common::EParkingStage::FreeParking;
                    container.m_Data.m_freeParkingIn.m_is360FreeParking = true;
                    break;
                case 2:
                    container.m_Data.m_freeParkingIn.m_parkStage =
                        cc::target::common::EFreeParkingStage::InFpStandstill;
                    container.m_Data.m_parkingStage = cc::target::common::EParkingStage::FreeParking;
                    container.m_Data.m_freeParkingIn.m_is360FreeParking = false;
                    container.m_Data.m_freeParkingIn.m_slotType =
                        cc::target::common::EFreeParkingSlotType::HorizontalSlot;
                    break;
                case 3:
                    container.m_Data.m_freeParkingIn.m_parkStage =
                        cc::target::common::EFreeParkingStage::InFpStandstill;
                    container.m_Data.m_parkingStage = cc::target::common::EParkingStage::FreeParking;
                    container.m_Data.m_freeParkingIn.m_is360FreeParking = false;
                    container.m_Data.m_freeParkingIn.m_slotType =
                        cc::target::common::EFreeParkingSlotType::VerticalSlot;
                    break;
                case 4:
                    container.m_Data.m_freeParkingIn.m_parkStage =
                        cc::target::common::EFreeParkingStage::InFpStandstill;
                    container.m_Data.m_parkingStage = cc::target::common::EParkingStage::FreeParking;
                    container.m_Data.m_freeParkingIn.m_is360FreeParking = false;
                    container.m_Data.m_freeParkingIn.m_slotType =
                        cc::target::common::EFreeParkingSlotType::DiagonalSlot;
                    break;
                case 5:
                    container.m_Data.m_parkingStage = cc::target::common::EParkingStage::FreeParking;
                    container.m_Data.m_freeParkingIn.m_parkStage =
                        cc::target::common::EFreeParkingStage::GuidanceStart;
                    break;
                case 6:
                    container.m_Data.m_parkingStage = cc::target::common::EParkingStage::FreeParking;
                    container.m_Data.m_freeParkingIn.m_parkStage =
                        cc::target::common::EFreeParkingStage::GuidanceFinish;
                    break;
                case 7:
                    container.m_Data.m_parkingStage = cc::target::common::EParkingStage::FreeParking;
                    container.m_Data.m_freeParkingIn.m_parkStage =
                        cc::target::common::EFreeParkingStage::InFpMoving;
                    break;
                default:
                case 0:
                    container.m_Data.m_parkingStage = cc::target::common::EParkingStage::Invalid;
                    container.m_Data.m_freeParkingIn.m_parkStage = cc::target::common::EFreeParkingStage::None;
                    container.m_Data.m_freeParkingIn.m_is360FreeParking = true;
                    break;
                }
                cc::daddy::CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort.deliver();
            }
        }

        static int s_ParkingSpaceStatus;
        {
            static const char* s_ParkingSpaceStatusItems[] = {
                "NoInformation",
                "ParkingSpaceOccupied",
                "ParkingSpaceInvacant",
                "ParkingSpaceVacant",
                "ParkingSpaceHighlight",
            };
            dirty |= ImGui::ListBox(
                "ParkingSpaceStatus",
                &s_ParkingSpaceStatus,
                s_ParkingSpaceStatusItems,
                IM_ARRAYSIZE(s_ParkingSpaceStatusItems),
                IM_ARRAYSIZE(s_ParkingSpaceStatusItems));
        }
    }
    ImGui::End();
}

void sendViewId(vfc::int32_t f_viewId)
{
    cc::daddy::HUDislayModeSwitchDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.reserve();
    l_container.m_Data          = static_cast<vfc::uint8_t>(f_viewId);
    cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.deliver();
}

void sendGear(pc::daddy::EGear f_gear)
{
    using cc::daddy::CustomDaddyPorts;
    using namespace pc::daddy;
    GearDaddy& l_gear = BaseDaddyPorts::sm_gearSenderPort.reserve();
    l_gear.m_Data = f_gear;
    BaseDaddyPorts::sm_gearSenderPort.deliver();
}

void ImGuiView::testCommands()
{
    using cc::daddy::CustomDaddyPorts;
    if (ImGui::Begin("TestCommands"))
    {
        // if (ImGui::Button("TestTriggerFrontBumper"))
        // {
        //     sendGear(pc::daddy::EGear::GEAR_D);
        //     sendViewId(EScreenID_FRONT_BUMPER);
        // }
        // if (ImGui::Button("TestTriggerRearBumper"))
        // {
        //     sendGear(pc::daddy::EGear::GEAR_R);
        //     sendViewId(EScreenID_REAR_BUMPER);
        // }

        // static int s_rotateAngle = 0;
        // ImGui::SliderInt("RotateAngleSlider", &s_rotateAngle, -360, 360);
        // if (ImGui::Button("RotateAngle"))
        // {
        //     auto& l_container = cc::daddy::CustomDaddyPorts::sm_SurroundViewRotateAngle_SenderPort.reserve();
        //     l_container.m_Data = s_rotateAngle;
        //     cc::daddy::CustomDaddyPorts::sm_SurroundViewRotateAngle_SenderPort.deliver();
        // }

        auto sendScreenId = [](EScreenID f_screenId)
        {
            cc::daddy::HUDislayModeSwitchDaddy_t& l_container =
                cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.reserve();
            l_container.m_Data = static_cast<vfc::uint8_t>(f_screenId);
            cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.deliver();
        };
        auto sendBev = [](bool f_bev)
        {
            auto& l_container = cc::daddy::CustomDaddyPorts::CustomDaddyPorts::sm_BirdEyeViewSwitch_SenderPort.reserve();
            l_container.m_Data = f_bev;
            cc::daddy::CustomDaddyPorts::CustomDaddyPorts::sm_BirdEyeViewSwitch_SenderPort.deliver();
        };

        if (ImGui::Button("Fullscreen With BeV"))
        {
            sendScreenId(EScreenID_FULLSCREEN);
            sendBev(true);
        }
        if (ImGui::Button("Fullscreen Without BeV"))
        {
            sendScreenId(EScreenID_FULLSCREEN);
            sendBev(false);
        }
        if (ImGui::Button("Normal With BeV"))
        {
            sendScreenId(EScreenID_SINGLE_FRONT_NORMAL);
            sendBev(true);
        }
        if (ImGui::Button("Normal Without BeV"))
        {
            sendScreenId(EScreenID_SINGLE_FRONT_NORMAL);
            sendBev(false);
        }
    }
    ImGui::End();
}


ImGuiView::~ImGuiView()
{
  ImPlot::DestroyContext();
  ImGui::DestroyContext();
}


} // namespace imgui
} // namespace views
} // namespace cc