//
// File: ViewModexViewStateMachine_R2015b_types.h
//
// Code generated for Simulink model 'ViewModexViewStateMachine_R2015b'.
//
// Model version                  : 1.3177
// Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
// C/C++ source code generated on : Tue Jul 16 23:14:22 2024
//
// Target selection: ert.tlc
// Embedded hardware selection: ARM Compatible->ARM Cortex
// Code generation objectives:
//    1. Execution efficiency
//    2. RAM efficiency
//    3. ROM efficiency
//    4. MISRA C:2012 guidelines
//    5. Debugging
//    6. Safety precaution
// Validation result: Passed (28), Warnings (2), Error (0)
//
#ifndef RTW_HEADER_ViewModexViewStateMachine_R2015b_types_h_
#define RTW_HEADER_ViewModexViewStateMachine_R2015b_types_h_
#include "rtwtypes.h"
#ifndef DEFINED_TYPEDEF_FOR_EParkTypeIn_
#define DEFINED_TYPEDEF_FOR_EParkTypeIn_

typedef uint8_T EParkTypeIn;

// enum EParkTypeIn
const EParkTypeIn EParkTypeIn_None = 0U;// Default value
const EParkTypeIn EParkTypeIn_RPA = 1U;
const EParkTypeIn EParkTypeIn_APA = 2U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_HmiArray_
#define DEFINED_TYPEDEF_FOR_HmiArray_

typedef struct
{
  uint16_T x;
  uint16_T y;
}
HmiArray;

#endif

#ifndef DEFINED_TYPEDEF_FOR_HmiLayout_
#define DEFINED_TYPEDEF_FOR_HmiLayout_

typedef struct
{
  HmiArray iconCenter;
  HmiArray responseArea;
}
HmiLayout;

#endif

#ifndef DEFINED_TYPEDEF_FOR_HmiUIElements_
#define DEFINED_TYPEDEF_FOR_HmiUIElements_

typedef struct
{
  HmiLayout swVersionShowSwitchB;
  HmiLayout swVersionShowSwitchC;
  HmiLayout cpcOverlaySwitch;
  HmiLayout settingSearchingRearInButtonPress;
  HmiLayout settingSearchingFrontInButtonPress;
  HmiLayout settingPauseButtonPress;
  HmiLayout settingFreeParkingSpaceTypeCrossButtonPress;
  HmiLayout settingFreeParkingSpaceTypeParallelButtonPress;
  HmiLayout settingFreeParkingSpaceTypeDiagonalButtonPress;
  HmiLayout settingFreeParkingConfirmButtonPress;
  HmiLayout settingStarkParkButtonPress;
  HmiLayout settingSuspendContinueButtonPress;
  HmiLayout settingAutoCamActiv;
  HmiLayout parkingOutAPAConfirm;
  HmiLayout parkingRPASelect;
  HmiLayout parkingAPASelect;
  HmiLayout camSvFr;
  HmiLayout camSvRe;
  HmiLayout camFwLe;
  HmiLayout camFwRi;
  HmiLayout camRwLe;
  HmiLayout camRwRi;
  HmiLayout camPvFL;
  HmiLayout camPvFR;
  HmiLayout camPvRL;
  HmiLayout camPvRR;
  HmiLayout settingQuit;
  HmiLayout settingPP;
  HmiLayout settingVM;
  HmiLayout settingPARKIconSearching;
  HmiLayout settingPARKIcon;
  HmiLayout functionSelectionParkin;
  HmiLayout functionSelectionParkout;
  HmiLayout functionSelectionFreeParking;
  HmiLayout ParkOutSideLeftButton;
  HmiLayout ParkOutSideRightButton;
}
HmiUIElements;

#endif

#ifndef DEFINED_TYPEDEF_FOR_APA_ParkSpace_
#define DEFINED_TYPEDEF_FOR_APA_ParkSpace_

typedef struct
{
  uint8_T APA_ParkSlotManeuverType_1L;
  uint8_T APA_ParkSlotManeuverType_2L;
  uint8_T APA_ParkSlotManeuverType_3L;
  uint8_T APA_ParkSlotManeuverType_4L;
  uint8_T APA_ParkSlotManeuverType_1R;
  uint8_T APA_ParkSlotManeuverType_2R;
  uint8_T APA_ParkSlotManeuverType_3R;
  uint8_T APA_ParkSlotManeuverType_4R;
  real_T APA_ParkSlotAng_1L;
  real_T APA_ParkSlotAng_2L;
  real_T APA_ParkSlotAng_3L;
  real_T APA_ParkSlotAng_1R;
  real_T APA_ParkSlotAng_2R;
  real_T APA_ParkSlotAng_3R;
  real_T APA_ParkSlotAng_4L;
  real_T APA_ParkSlotAng_4R;
  real_T APA_ParkSlotX_1L;
  real_T APA_ParkSlotX_2L;
  real_T APA_ParkSlotX_3L;
  real_T APA_ParkSlotX_1R;
  real_T APA_ParkSlotX_2R;
  real_T APA_ParkSlotX_3R;
  uint8_T APA_ParkSlotSt_1L;
  uint8_T APA_ParkSlotSt_1R;
  uint8_T APA_ParkSlotSt_2L;
  uint8_T APA_ParkSlotSt_2R;
  uint8_T APA_ParkSlotSt_3L;
  uint8_T APA_ParkSlotSt_3R;
  uint8_T APA_ParkSlotSt_4L;
  uint8_T APA_ParkSlotSt_4R;
}
APA_ParkSpace;

#endif

#ifndef DEFINED_TYPEDEF_FOR_HmiUISlot_
#define DEFINED_TYPEDEF_FOR_HmiUISlot_

typedef struct
{
  HmiLayout parkingSlot_4R;
  HmiLayout parkingSlot_3R;
  HmiLayout parkingSlot_2R;
  HmiLayout parkingSlot_1R;
  HmiLayout parkingSlot_4L;
  HmiLayout parkingSlot_3L;
  HmiLayout parkingSlot_2L;
  HmiLayout parkingSlot_1L;
}
HmiUISlot;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EScreenID_
#define DEFINED_TYPEDEF_FOR_EScreenID_

typedef enum
{
  EScreenID_NO_CHANGE = 0,             // Default value
  EScreenID_NO_VIDEO_USER = 1,
  EScreenID_LSMG = 2,
  EScreenID_CONTEXT_ON_ROAD = 3,
  EScreenID_CONTEXT_OFF_ROAD = 4,
  EScreenID_CONTEXT_TOWING = 5,
  EScreenID_CONTEXT_JAPANESE = 6,
  EScreenID_CONTEXT_THREAT = 7,
  EScreenID_SINGLE_FRONT_NORMAL = 8,
  EScreenID_SINGLE_STB = 9,
  EScreenID_SINGLE_FRONT_JUNCTION = 10,
  EScreenID_SINGLE_REAR_NORMAL_OFF_ROAD = 11,
  EScreenID_PARK_ASSIST_FRONT = 12,
  EScreenID_SINGLE_FRONT_RAW = 13,
  EScreenID_SINGLE_REAR_NORMAL_ON_ROAD = 14,
  EScreenID_SINGLE_REAR_JUNCTION = 15,
  EScreenID_SINGLE_REAR_RAW_DIAG = 16,
  EScreenID_SINGLE_REAR_HITCH = 17,
  EScreenID_SINGLE_REAR_HITCH_ZOOM = 18,
  EScreenID_PARK_ASSIST_REAR = 19,
  EScreenID_SINGLE_REAR_TRAILER = 20,
  EScreenID_PERSPECTIVE_KL = 21,
  EScreenID_PARK_ASSIST_FRONT_JAP = 22,
  EScreenID_PARK_ASSIST_REAR_JAP = 23,
  EScreenID_SINGLE_ML_RAW = 24,
  EScreenID_PERSPECTIVE_KR = 25,
  EScreenID_SINGLE_LEFT = 26,
  EScreenID_SINGLE_RIGHT = 27,
  EScreenID_SINGLE_MR_RAW = 28,
  EScreenID_THREAT_FRONT = 29,
  EScreenID_THREAT_REAR = 30,
  EScreenID_WHEEL_FRONT_DUAL = 31,
  EScreenID_PERSPECTIVE_FL = 32,
  EScreenID_PERSPECTIVE_FR = 33,
  EScreenID_PERSPECTIVE_RL = 34,
  EScreenID_PERSPECTIVE_RR = 35,
  EScreenID_DUAL_FRONT_ML = 36,
  EScreenID_DUAL_FRONT_MR = 37,
  EScreenID_PERSPECTIVE_PRE = 38,
  EScreenID_PERSPECTIVE_PLE = 39,
  EScreenID_DUAL_FRONT_JAP = 40,
  EScreenID_CAM_CALIB_ENG = 41,
  EScreenID_DUAL_REAR_JAP = 42,
  EScreenID_TOW_ASSIST_ENG = 43,
  EScreenID_LSM_ENG = 44,
  EScreenID_WHEEL_REAR_DUAL = 45,
  EScreenID_LSMG_LSAEB_ENG = 46,
  EScreenID_FULL_SCREEN = 47,
  EScreenID_TRIPLE_ML_FV_MR = 48,
  EScreenID_TRIPLE_REAR_ML_MR_TOW_ASSIST = 49,
  EScreenID_QUAD_RAW = 50,
  EScreenID_NO_VIDEO_SYSTEM = 51,
  EScreenID_PERSPECTIVE_PFR = 52,
  EScreenID_PERSPECTIVE_PRI = 53,
  EScreenID_BACK = 54,
  EScreenID_PRK_MODE_SELECT = 55,
  EScreenID_PRK_SEARCHING = 56,
  EScreenID_PRK_CONFIRMING = 57,
  EScreenID_VERT_SINGLE_FRONT = 58,
  EScreenID_VERT_SINGLE_REAR = 59,
  EScreenID_VERT_SINGLE_FRONT_JUNCTION = 60,
  EScreenID_VERT_SINGLE_REAR_JUNCTION = 61,
  EScreenID_VERT_SINGLE_LEFT = 62,
  EScreenID_VERT_SINGLE_RIGHT = 63,
  EScreenID_VERT_WHEEL_FRONT_DUAL = 64,
  EScreenID_VERT_WHEEL_REAR_DUAL = 65,
  EScreenID_VERT_PLANVIEW_WITH_SEPARATOR = 66,
  EScreenID_VERT_PERSPECTIVE_PFR = 67,
  EScreenID_VERT_PERSPECTIVE_PRE = 68,
  EScreenID_VERT_PERSPECTIVE_RL = 69,
  EScreenID_VERT_PERSPECTIVE_RR = 70,
  EScreenID_VERT_PERSPECTIVE_FL = 71,
  EScreenID_VERT_PERSPECTIVE_FR = 72,
  EScreenID_VERT_SINGLE_STB = 73,
  EScreenID_HORI_PARKING_FRONT = 74,
  EScreenID_HORI_PARKING_REAR = 75,
  EScreenID_VERT_PARKING_FRONT = 76,
  EScreenID_VERT_PARKING_REAR = 77,
  EScreenID_PLANVIEW_WITH_SEPARATOR = 78,
  EScreenID_REMOTE_SCREEN_FRONT = 79,
  EScreenID_REMOTE_SCREEN_REAR = 80,
  EScreenID_REMOTE_SCREEN_LEFT = 81,
  EScreenID_REMOTE_SCREEN_RIGHT = 82,
  EScreenID_LEFTRIGHTVIEW_FRONT_VIEW = 84,
  EScreenID_DEBUG = 83,
  EScreenID_LEFTRIGHTVIEW_REAR_VIEW = 85,
  EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__LEFT_SIDE = 86,
  EScreenID_VERT_PLAN_FRONT_ENLARGE__FRONT_MAIN__RIGHT_SIDE = 87,
  EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__LEFT_SIDE = 88,
  EScreenID_VERT_PLAN_REAR_ENLARGE__REAR_MAIN__RIGHT_SIDE = 89,
  EScreenID_VERT_FULLSCREEN = 90,
  EScreenID_VERT_FULLSCREEN_FRONT_ENLARGE = 91,
  EScreenID_VERT_FULLSCREEN_REAR_ENLARGE = 92,
  EScreenID_VERT_PLAN__DUAL_FRONT_WHEEL__FRONT_SIDE = 93,
  EScreenID_VERT_PLAN__DUAL_FRONT_WHEEL__REAR_SIDE = 94,
  EScreenID_VERT_PLAN__DUAL_REAR_WHEEL__FRONT_SIDE = 95,
  EScreenID_VERT_PLAN__DUAL_REAR_WHEEL__REAR_SIDE = 96,
  EScreenID_VERT_PLAN__DUAL_WHEEL__FRONT_SIDE = 97,
  EScreenID_VERT_PLAN__DUAL_WHEEL__REAR_SIDE = 98,
  EScreenID_VERT_PERSPECTIVE_FL_R = 99,
  EScreenID_VERT_PERSPECTIVE_FL_L = 100,
  EScreenID_VERT_PERSPECTIVE_FR_R = 101,
  EScreenID_VERT_PERSPECTIVE_FR_L = 102,
  EScreenID_VERT_PERSPECTIVE_RL_R = 103,
  EScreenID_VERT_PERSPECTIVE_RL_L = 104,
  EScreenID_VERT_PERSPECTIVE_RR_R = 105,
  EScreenID_VERT_PERSPECTIVE_RR_L = 106,
  EScreenID_VERT_2D_FRONTVIEW_RIGHTVIEW = 107,
  EScreenID_VERT_2D_FRONTVIEW_LEFTVIEW = 108,
  EScreenID_VERT_2D_REARVIEW_RIGHTVIEW = 109,
  EScreenID_VERT_2D_REARVIEW_LEFTVIEW = 110,
  EScreenID_MODEL_F_VIEW_ENLARGEMENT = 111,
  EScreenID_MODEL_B_VIEW_ENLARGEMENT = 112,
  EScreenID_FULLSCREEN = 113,
  EScreenID_FULLSCREEN_FRONT_ENLARGE = 114,
  EScreenID_FULLSCREEN_REAR_ENLARGE = 115,
  EScreenID_FRONT_BUMPER = 116,
  EScreenID_REAR_BUMPER = 117,
  EScreenID_ULTRA_WIDE_SURROUND_VIEW = 118,
  EScreenID_PLANETARY_VIEW = 119,
  EScreenID_SUPER_TRANSPARENT_VIEW = 120,
  EScreenID_SINGLE_REAR_NORMAL_ON_ROAD_SGHL_L = 121,
  EScreenID_MODEL_B_VIEW_ENLARGEMENT_SGHL_L = 122,
  EScreenID_IMAGE_IN_IMAGE_LEFT = 123,
  EScreenID_IMAGE_IN_IMAGE_RIGHT = 124,
  EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT = 125,
  EScreenID_IMAGE_IN_IMAGE_PLANVIEW = 126,
  EscreenID_FULL_SCREEN_3D = 128,
  EScreenID_AVM3D_VIEW_FULL_SCREEN_2D_VOT = 127,
  EScreenID_IMAGE_IN_IMAGE_PLANVIEW_FRONT_ENLARGE = 129,
  EScreenID_IMAGE_IN_IMAGE_PLANVIEW_REAR_ENLARGE = 130,
  EScreenID_UPVIEWCOFIG_WHEEL_REAR_DUAL = 131,
  EScreenID_UPVIEWCOFIG_WHEEL_FRONT_DUAL = 132,
  EScreenID_UPVIEWCOFIG_WHEEL_BOTH = 133,
  EScreenID_PARKING_FREEPARKING = 134,
  EScreenID_PARKING_TOP = 135,
  EScreenID_PARKING_FRONT = 136,
  EScreenID_PARKING_REAR = 137,
  EScreenID_SINGLE_FRONT_NORMAL_LEFT = 138,
  EScreenID_SINGLE_REAR_NORMAL_ON_ROAD_LEFT = 139,
  EScreenID_PERSPECTIVE_RL_LEFT = 140,
  EScreenID_PERSPECTIVE_RR_LEFT = 141,
  EScreenID_PERSPECTIVE_PRE_LEFT = 142,
  EScreenID_PERSPECTIVE_PLE_LEFT = 143,
  EScreenID_PERSPECTIVE_PRI_LEFT = 144,
  EScreenID_PERSPECTIVE_FL_LEFT  = 145,
  EScreenID_PERSPECTIVE_FR_LEFT  = 146,
  EScreenID_PERSPECTIVE_PFR_LEFT  = 147,
  EScreenID_WHEEL_FRONT_DUAL_REAR = 148,
  EScreenID_PLANVIEW_WITH_SEPARATOR_REAR = 149,
  EScreenID_WHEEL_REAR_DUAL_REAR = 150,
  EScreenID_UPVIEWCOFIG_WHEEL_FRONT_DUAL_REAR = 151,
  EScreenID_UPVIEWCOFIG_WHEEL_BOTH_REAR = 152,
  EScreenID_UPVIEWCOFIG_WHEEL_REAR_DUAL_REAR = 153,
  EScreenID_FRONT_BUMPER_RIGHT = 154,
  EScreenID_REAR_BUMPER_RIGHT = 155,
  EScreenID_NORMAL3D_KEEP = 156,
  AVM3D_VIEW_FULL_SCREEN_2D_VSW = 157,
  EScreenID_APA_FRONT_HUBVIEW = 158,
  EScreenID_COMPASS_TURN = 159,
  EScreenID_CARB_TURN = 160,
  EScreenID_WHEEL_FRONT_DUAL_ENLARGED = 161,
  EScreenID_PLANVIEW_WITH_SEPARATOR_ENLARGED = 162,
  EScreenID_WHEEL_REAR_DUAL_ENLARGED = 163,
  EScreenID_SINGLE_LEFT_REAR = 164,
  EScreenID_SINGLE_RIGHT_REAR = 165,
  EScreenID_IMAGE_IN_IMAGE_LEFT_REAR = 166,
  EScreenID_IMAGE_IN_IMAGE_RIGHT_REAR = 167,
  EScreenID_IMAGE_IN_IMAGE_LEFT_RIGHT_REAR = 168,
  EScreenID_WHEEL_FRONT_SINGLE_FRONT_DUAL = 169,
  EScreenID_WHEEL_REAR_SINGLE_FRONT_DUAL = 170,
  EScreenID_WHEEL_FRONT_SINGLE_REAR_DUAL = 171,
  EScreenID_WHEEL_REAR_SINGLE_REAR_DUAL = 172,
  EScreenID_WHEEL_FRONT_SINGLE_FRONT_DUAL_WITHOUT_PLAN = 173,
  EScreenID_WHEEL_REAR_SINGLE_FRONT_DUAL_WITHOUT_PLAN = 174,
  EScreenID_WHEEL_FRONT_SINGLE_REAR_DUAL_WITHOUT_PLAN = 175,
  EScreenID_WHEEL_REAR_SINGLE_REAR_DUAL_WITHOUT_PLAN = 176,
  EScreenID_FIND_CAR_VIEW = 177
}
EScreenID;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EAnimationState_
#define DEFINED_TYPEDEF_FOR_EAnimationState_

typedef enum
{
  EAnimationState_ANIM_INIT = 0,       // Default value
  EAnimationState_ANIM_ONGOING,
  EAnimationState_ANIM_FINISHED
}
EAnimationState;

#endif

#ifndef DEFINED_TYPEDEF_FOR_AnimationState_
#define DEFINED_TYPEDEF_FOR_AnimationState_

typedef struct
{
  EScreenID screenID;
  EAnimationState state;
}
AnimationState;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EIndicatorBlickStatus_
#define DEFINED_TYPEDEF_FOR_EIndicatorBlickStatus_

typedef enum
{
  EIndicatorBlickStatus_INDICATE_OFF = 0,// Default value
  EIndicatorBlickStatus_INDICATE_ACTIVE
}
EIndicatorBlickStatus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_CustomLightStatus_
#define DEFINED_TYPEDEF_FOR_CustomLightStatus_

typedef struct
{
  EIndicatorBlickStatus LeftIndicatorBlinkState;
  EIndicatorBlickStatus RightIndicatorBlinkState;
}
CustomLightStatus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EHuRotateStatus_
#define DEFINED_TYPEDEF_FOR_EHuRotateStatus_

typedef enum
{
  ROTATE_INVALID = 0,                  // Default value
  ROTATE_HORIZONTAL,
  ROTATE_VERTICAL,
  ROTATE_RESERVED
}
EHuRotateStatus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EHuImageWorkMode_
#define DEFINED_TYPEDEF_FOR_EHuImageWorkMode_

typedef enum
{
  WORK_MODE_2DMODE = 0,                // Default value
  WORK_MODE_FULLSCREEN,
  WORK_MODE_RESERVED,
  WORK_MODE_SMALLWIDGET,
  WORK_MODE_REVERSE_FRONTRIGHT,
  WORK_MODE_REVERSE,
  WORK_MODE_3DMODE,
  WORK_MODE_RESERVED_OTHERS,
  WORK_MODE_INVALID
}
EHuImageWorkMode;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EHuDisplayModeSwitch_
#define DEFINED_TYPEDEF_FOR_EHuDisplayModeSwitch_

typedef enum
{
  DISPLAY_MODE_INVALID = 0,            // Default value
  DISPLAY_MODE_TURNOFF,
  DISPLAY_MODE_FRONTVIEW,
  DISPLAY_MODE_BACKVIEW,
  DISPLAY_MODE_LEFTVIEW,
  DISPLAY_MODE_RIGHTVIEW,
  DISPLAY_MODE_LOOKDOWN,
  DISPLAY_MODE_STARTCAL,
  DISPLAY_MODE_FRONTLEFT,
  DISPLAY_MODE_FRONTRIGHT,
  DISPLAY_MODE_BACKLEFT,
  DISPLAY_MODE_BACKRIGHT,
  DISPLAY_MODE_FRONTWIDE,
  DISPLAY_MODE_BACKWIDE,
  DISPLAY_MODE_WIDTHLIMIT,
  DISPLAY_MODE_RESERVED
}
EHuDisplayModeSwitch;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EHuDisplayModeExpand_
#define DEFINED_TYPEDEF_FOR_EHuDisplayModeExpand_

typedef enum
{
  DISPLAY_EXP_INVALID = 0,             // Default value
  DISPLAY_EXP_2DFRONTWIDELEFT,
  DISPLAY_EXP_2DFRONTWIDERIGHT,
  DISPLAY_EXP_2DREARWIDELEFT,
  DISPLAY_EXP_2DREARWIDERIGHT,
  DISPLAY_EXP_2DLIMITEDWIDTHLEFT,
  DISPLAY_EXP_2DLIMITEDWIDTHRIGHT,
  DISPLAY_EXP_RESERVED_1,
  DISPLAY_EXP_RESERVED_2,
  DISPLAY_EXP_3DLEFT_2DLEFT,
  DISPLAY_EXP_3DLEFT_2DRIGHT,
  DISPLAY_EXP_3DRIGHT_2DLEFT,
  DISPLAY_EXP_3DRIGHT_2DRIGHT,
  DISPLAY_EXP_LEFT_FRONT,
  DISPLAY_EXP_RIGHT_FRONT,
  DISPLAY_EXP_RESERVED_3
}
EHuDisplayModeExpand;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EHuDisplayModeExpandNew_
#define DEFINED_TYPEDEF_FOR_EHuDisplayModeExpandNew_

typedef enum
{
  DISPLAY_EXPNEW_INVALID = 0,          // Default value
  DISPLAY_EXPNEW_3DLEFTFRONT_2DLEFT,
  DISPLAY_EXPNEW_3DLEFTFRONT_2DRIGHT,
  DISPLAY_EXPNEW_3DRIGHTFRONT_2DLEFT,
  DISPLAY_EXPNEW_3DRIGHTFRONT_2DRIGHT,
  DISPLAY_DOUBLE_FRONT,
  DISPLAY_DOUBLE_REAR,
  DISPLAY_3D_BONNET_VIEW
}
EHuDisplayModeExpandNew;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EPdmSetting_
#define DEFINED_TYPEDEF_FOR_EPdmSetting_

typedef enum
{
  EPdmSetting_NONE = 0,                // Default value
  EPdmSetting_ENABLED,
  EPdmSetting_DISABLED,
  EPdmSetting_INHIBIT
}
EPdmSetting;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EPARKDriverIndSearch_
#define DEFINED_TYPEDEF_FOR_EPARKDriverIndSearch_

typedef uint8_T EPARKDriverIndSearch;

// enum EPARKDriverIndSearch
const EPARKDriverIndSearch EPARKDriverIndSearch_PARKDRVSEARCH_NoRequest = 0U;// Default value
const EPARKDriverIndSearch EPARKDriverIndSearch_PARKDRVSEARCH_SlotSearching = 1U;
const EPARKDriverIndSearch
  EPARKDriverIndSearch_PARKDRVSEARCH_WaitingForVehicleStop = 2U;
const EPARKDriverIndSearch
  EPARKDriverIndSearch_PARKDRVSEARCH_WaitForVehicleSlowdown = 3U;
const EPARKDriverIndSearch
  EPARKDriverIndSearch_PARKDRVSEARCH_WaitForDriverOperateGear = 4U;
const EPARKDriverIndSearch
  EPARKDriverIndSearch_PARKDRVSEARCH_WaitForVehicleDriverConfirmPark = 5U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EAPARPAparkmode_
#define DEFINED_TYPEDEF_FOR_EAPARPAparkmode_

typedef uint8_T EAPARPAparkmode;

// enum EAPARPAparkmode
const EAPARPAparkmode EAPARPAparkmode_APAPARKMODE_IDLE = 0U;// Default value
const EAPARPAparkmode EAPARPAparkmode_APAPARKMODE_APA = 1U;
const EAPARPAparkmode EAPARPAparkmode_APAPARKMODE_RPA = 2U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EParkModeDisp2TouchButton_
#define DEFINED_TYPEDEF_FOR_EParkModeDisp2TouchButton_

typedef uint8_T EParkModeDisp2TouchButton;

// enum EParkModeDisp2TouchButton
const EParkModeDisp2TouchButton
  EParkModeDisp2TouchButton_PARK_MODE_DISP2TOUCH_NOT_AVAILABLE = 0U;// Default value
const EParkModeDisp2TouchButton
  EParkModeDisp2TouchButton_PARK_MODE_DISP2TOUCH_PARKIN_PARKOUT_FREEPARKING_AVAILABLE
  = 1U;
const EParkModeDisp2TouchButton
  EParkModeDisp2TouchButton_PARK_MODE_DISP2TOUCH_APA_RPA_AVAILABLE = 2U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EParkDisp2TouchButton_
#define DEFINED_TYPEDEF_FOR_EParkDisp2TouchButton_

typedef uint8_T EParkDisp2TouchButton;

// enum EParkDisp2TouchButton
const EParkDisp2TouchButton EParkDisp2TouchButton_PARK_DISP2TOUCH_NOT_AVAILABLE =
  0U;                                  // Default value
const EParkDisp2TouchButton EParkDisp2TouchButton_PARK_DISP2TOUCH_AVAILABLE = 1U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ButtonDisplaySts_
#define DEFINED_TYPEDEF_FOR_ButtonDisplaySts_

typedef struct
{
  EParkModeDisp2TouchButton ButtonParkModeDispSts;
  EParkDisp2TouchButton ButtonParkStartDispSts;
  EParkDisp2TouchButton ButtonParkContinueDispSts;
  EParkDisp2TouchButton ButtonParkPauseDispSts;
  EParkDisp2TouchButton ButtonParkQuitDispSts;
  EParkDisp2TouchButton ButtonParkOutDirectionDispSts;
  EParkDisp2TouchButton ButtonFreeParkingConfirmDispSts;
  EParkDisp2TouchButton ButtonFreeParkingSpaceTypeDispSts;
  EParkDisp2TouchButton ButtonParkSlotsSelectingDispSts;
  EParkDisp2TouchButton ButtonParkInTypeDispSts;
}
ButtonDisplaySts;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EVehColor_
#define DEFINED_TYPEDEF_FOR_EVehColor_

typedef uint8_T EVehColor;

// enum EVehColor
const EVehColor EVehColor_INVALID = 0U;
const EVehColor EVehColor_SMOKY_CRYSTAL_BLACK = 1U;// Default value
const EVehColor EVehColor_TIME_GRAY = 2U;
const EVehColor EVehColor_RED_EMPEROR = 3U;
const EVehColor EVehColor_SNOWY_WHITE = 4U;
const EVehColor EVehColor_TIANSHAN_WHITE = 5U;
const EVehColor EVehColor_TEHRAN = 6U;
const EVehColor EVehColor_MOUNTAIN_ASH = 7U;
const EVehColor EVehColor_INKSTONE_BLUE = 8U;
const EVehColor EVehColor_SILVERSAND_BLACK = 9U;
const EVehColor EVehColor_TITANIUM_SILVER = 10U;
const EVehColor EVehColor_STRATEGIC_BLUE = 11U;
const EVehColor EVehColor_TITANIUM_EMPTY_GRAY = 12U;
const EVehColor EVehColor_CRYSTAL_WHITE = 13U;
const EVehColor EVehColor_FLANGE_RED = 14U;
const EVehColor EVehColor_SKY_BLUE = 15U;
const EVehColor EVehColor_VIBRANT_ORANGE = 16U;
const EVehColor EVehColor_WISDOM_BLUE = 17U;
const EVehColor EVehColor_HONEY_ORANGE = 18U;
const EVehColor EVehColor_RUSTLE_GREEN = 19U;
const EVehColor EVehColor_PUFFFAN = 20U;
const EVehColor EVehColor_SPARKLING_BLUE = 21U;
const EVehColor EVehColor_SURFBUE = 22U;
const EVehColor EVehColor_XUANKONG_BLACK = 23U;
const EVehColor EVehColor_QIANSHAN_CUI = 24U;
const EVehColor EVehColor_AZURE = 25U;
const EVehColor EVehColor_CYAN_SMOKE = 26U;
const EVehColor EVehColor_DOME_WHITE = 27U;
const EVehColor EVehColor_AURORA_WHITE = 28U;
const EVehColor EVehColor_ROSEMARY_GRAY = 29U;
const EVehColor EVehColor_LOSTATLANTIS = 30U;
const EVehColor EVehColor_BLUEOCEANLENS = 31U;
const EVehColor EVehColor_MERCURY_BLUE = 32U;
const EVehColor EVehColor_TROLLGRASS_GREEN = 33U;
const EVehColor EVehColor_ROCKY_GREEN = 34U;
const EVehColor EVehColor_DEMON_BlACK = 35U;
const EVehColor EVehColor_SEA_BLUE = 36U;
const EVehColor EVehColor_ROSE_GOLD = 37U;
const EVehColor EVehColor_SANDALWOOD_PURPLE = 38U;
const EVehColor EVehColor_SUNRISE_GOLD = 39U;
const EVehColor EVehColor_DOME_BLUE = 40U;
const EVehColor EVehColor_RESERVED_COLOR1 = 41U;
const EVehColor EVehColor_RESERVED_COLOR2 = 42U;
const EVehColor EVehColor_MUSHAN_PINK = 43U;
const EVehColor EVehColor_SILVER_GLAZE_WHITE = 74U;
const EVehColor EVehColor_DU_DU_WHITE = 75U;
const EVehColor EVehColor_JUN_WARE_GRAY = 100U;
const EVehColor EVehColor_RESERVED_COLOR = MAX_uint8_T;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ELCFVehicleModel_
#define DEFINED_TYPEDEF_FOR_ELCFVehicleModel_

typedef uint8_T ELCFVehicleModel;

// enum ELCFVehicleModel
const ELCFVehicleModel SM_MREC = 2U;   // Default value
const ELCFVehicleModel SM_MRHC = 3U;
const ELCFVehicleModel SM_STEX = 6U;
const ELCFVehicleModel SM_STHX = 8U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ERemoteScreenMode_
#define DEFINED_TYPEDEF_FOR_ERemoteScreenMode_

typedef uint8_T ERemoteScreenMode;

// enum ERemoteScreenMode
const ERemoteScreenMode ERemoteScreenMode_NO_CALL = 0U;// Default value
const ERemoteScreenMode ERemoteScreenMode_NEED_TO_CALL = 1U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EHuDisplayMode5x_
#define DEFINED_TYPEDEF_FOR_EHuDisplayMode5x_

typedef enum
{
  INVALID = 0,                         // Default value
  MODEL_F_VIEW_ENLARGEMENT_F_VIEW_L_VIEW,
  MODEL_F_VIEW_ENLARGEMENT_F_VIEW_R_VIEW,
  MODEL_B_VIEW_ENLARGEMENT_B_VIEW_L_VIEW,
  MODEL_B_VIEW_ENLARGEMENT_B_VIEW_R_VIEW,
  FULLSCREEN_MODEL_ENLARGEMENT,
  FULLSCREEN_MODEL_F_VIEW_ENLARGEMENT,
  FULLSCREEN_MODEL_B_VIEW_ENLARGEMENT,
  TOP_VIEW_F_DOUBLE_VIEW_F_VIEW,
  TOP_VIEW_F_DOUBLE_VIEW_B_VIEW,
  TOP_VIEW_B_DOUBLE_VIEW_F_VIEW,
  TOP_VIEW_B_DOUBLE_VIEW_B_VIEW,
  TOP_VIEW_ALL_DOUBLE_VIEW_F_VIEW,
  TOP_VIEW_ALL_DOUBLE_VIEW_B_VIEW,
  MODEL_F_VIEW_ENLARGEMENT,
  MODEL_B_VIEW_ENLARGEMENT,
  TOP_VIEW_F_VIEW_L_F_VIEW_R_F_VIEW,
  TOP_VIEW_B_VIEW_L_B_VIEW_R_B_VIEW,
  RESERVED
}
EHuDisplayMode5x;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ETouchStatus_
#define DEFINED_TYPEDEF_FOR_ETouchStatus_

typedef enum
{
  ETouchStatus_NONE = 0,               // Default value
  ETouchStatus_Press,
  ETouchStatus_Release,
  ETouchStatus_Slither
}
ETouchStatus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ESVSViewMode_
#define DEFINED_TYPEDEF_FOR_ESVSViewMode_

typedef enum
{
  ESVSViewMode_VM_Default = 0,         // Default value
  ESVSViewMode_VM_Standard,
  ESVSViewMode_VM_Perspective,
  ESVSViewMode_VM_Parking
}
ESVSViewMode;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EIndicator_
#define DEFINED_TYPEDEF_FOR_EIndicator_

typedef enum
{
  EIndicator_INDICATE_OFF = 0,         // Default value
  EIndicator_INDICATE_LEFT,
  EIndicator_INDICATE_RIGHT,
  EIndicator_INDICATE_WARNING
}
EIndicator;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EGear_
#define DEFINED_TYPEDEF_FOR_EGear_

typedef uint8_T EGear;

// enum EGear
const EGear EGear_Init = 0U;           // Default value
const EGear EGear_Gear_1 = 1U;
const EGear EGear_Gear_2 = 2U;
const EGear EGear_Gear_3 = 3U;
const EGear EGear_Gear_4 = 4U;
const EGear EGear_Gear_5 = 5U;
const EGear EGear_Gear_6 = 6U;
const EGear EGear_Gear_7 = 7U;
const EGear EGear_Gear_8 = 8U;
const EGear EGear_Gear_9 = 9U;
const EGear EGear_N = 10U;
const EGear EGear_R = 11U;
const EGear EGear_P = 12U;
const EGear EGear_D = 13U;
const EGear EGear_Gear_Invalid = 14U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ESettingSts_
#define DEFINED_TYPEDEF_FOR_ESettingSts_

typedef enum
{
  ESettingSts_Set_Default = 0,         // Default value
  ESettingSts_Set_ON,
  ESettingSts_Set_OFF
}
ESettingSts;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EPARKDriverInd_
#define DEFINED_TYPEDEF_FOR_EPARKDriverInd_

typedef uint8_T EPARKDriverInd;

// enum EPARKDriverInd
const EPARKDriverInd EPARKDriverInd_DRV_NoRequest = 0U;// Default value
const EPARKDriverInd EPARKDriverInd_DRV_ExpandedMirror = 1U;
const EPARKDriverInd EPARKDriverInd_DRV_GearD = 2U;
const EPARKDriverInd EPARKDriverInd_DRV_SlowDown = 3U;
const EPARKDriverInd EPARKDriverInd_DRV_SearchingProcess = 4U;
const EPARKDriverInd EPARKDriverInd_DRV_Stop = 5U;
const EPARKDriverInd EPARKDriverInd_DRV_ConnectPhone = 6U;
const EPARKDriverInd EPARKDriverInd_DRV_EPBApplied = 7U;
const EPARKDriverInd EPARKDriverInd_DRV_LeaveCar = 8U;
const EPARKDriverInd EPARKDriverInd_DRV_CloseTrunk = 9U;
const EPARKDriverInd EPARKDriverInd_DRV_SmallParkSlot = 10U;
const EPARKDriverInd EPARKDriverInd_DRV_CloseDoor = 11U;
const EPARKDriverInd EPARKDriverInd_DRV_SeatBelt = 12U;
const EPARKDriverInd EPARKDriverInd_DRV_SurroundView = 13U;
const EPARKDriverInd EPARKDriverInd_DRV_PressBrakePedal = 14U;
const EPARKDriverInd EPARKDriverInd_DRV_Confirm_Press_DM = 15U;
const EPARKDriverInd EPARKDriverInd_DRV_ReleaseBrake = 16U;
const EPARKDriverInd EPARKDriverInd_DRV_ProcessBar = 17U;
const EPARKDriverInd EPARKDriverInd_DRV_FunctionOff = 18U;
const EPARKDriverInd EPARKDriverInd_DRV_POCDirecSelect = 19U;
const EPARKDriverInd EPARKDriverInd_DRV_NoFrontObjectDetected = 20U;
const EPARKDriverInd EPARKDriverInd_DRV_PSSelection = 21U;
const EPARKDriverInd EPARKDriverInd_DRV_ResponseTimeout = 22U;
const EPARKDriverInd EPARKDriverInd_DRV_ExternalECUError = 23U;
const EPARKDriverInd EPARKDriverInd_DRV_CloseHood = 24U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EParkStatus_
#define DEFINED_TYPEDEF_FOR_EParkStatus_

typedef uint8_T EParkStatus;

// enum EParkStatus
const EParkStatus EParkStatus_Off = 0U;// Default value
const EParkStatus EParkStatus_Standby = 1U;
const EParkStatus EParkStatus_Searching = 2U;
const EParkStatus EParkStatus_Guidance_Active = 3U;
const EParkStatus EParkStatus_Guidance_Suspend = 4U;
const EParkStatus EParkStatus_Terminated = 5U;
const EParkStatus EParkStatus_Completed = 6U;
const EParkStatus EParkStatus_Failure = 7U;
const EParkStatus EParkStatus_AssistStandby = 8U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EParkMode_
#define DEFINED_TYPEDEF_FOR_EParkMode_

typedef uint8_T EParkMode;

// enum EParkMode
const EParkMode EParkMode_EPARKMODE_NONE = 0U;// Default value
const EParkMode EParkMode_EPARKMODE_PARK_IN = 1U;
const EParkMode EParkMode_EPARKMODE_PARK_OUT = 2U;
const EParkMode EParkMode_EPARKMODE_FREEPARKING = 3U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EPowerMode_
#define DEFINED_TYPEDEF_FOR_EPowerMode_

typedef uint8_T EPowerMode;

// enum EPowerMode
const EPowerMode EPowerMode_KEY_OUT = 0U;// Default value
const EPowerMode EPowerMode_KEY_RECENTLY_OUT = 1U;
const EPowerMode EPowerMode_KEY_APPROVED = 2U;
const EPowerMode EPowerMode_POST_ACCESSORY = 3U;
const EPowerMode EPowerMode_ACCESSORY = 4U;
const EPowerMode EPowerMode_POST_IGNITION = 5U;
const EPowerMode EPowerMode_IGNITION_ON = 6U;
const EPowerMode EPowerMode_RUNNING = 7U;
const EPowerMode EPowerMode_CRANK = 8U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EHuVehTransReq_
#define DEFINED_TYPEDEF_FOR_EHuVehTransReq_

typedef enum
{
  EHuVehTransReq_INVALID = 0,          // Default value
  EHuVehTransReq_HALF_TRANSPARENT,
  EHuVehTransReq_FULL_TRANSPARENT,
  EHuVehTransReq_RESERVED
}
EHuVehTransReq;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ESystem_
#define DEFINED_TYPEDEF_FOR_ESystem_

typedef enum
{
  ESystem_Unavailable = 0,             // Default value
  ESystem_Available,
  ESystem_PartiallyAvailable
}
ESystem;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EShowReqMode_
#define DEFINED_TYPEDEF_FOR_EShowReqMode_

typedef enum
{
  SHOWREQ_NONE = 0,                    // Default value
  SHOWREQ_SVS,
  SHOWREQ_PARK
}
EShowReqMode;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EPasWarnTone_
#define DEFINED_TYPEDEF_FOR_EPasWarnTone_

typedef enum
{
  EPasWarnTone_SOUND_OFF = 0,          // Default value
  EPasWarnTone_SOUND_LONG_BEEP,
  EPasWarnTone_SOUND_Fast,
  EPasWarnTone_SOUND_Medium,
  EPasWarnTone_SOUND_Slow,
  EPasWarnTone_SOUND_Mute
}
EPasWarnTone;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EShowSuspend_
#define DEFINED_TYPEDEF_FOR_EShowSuspend_

typedef enum
{
  EShowSuspend_Request_None = 0,       // Default value
  EShowSuspend_Request_Suspend
}
EShowSuspend;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EPARKRecoverInd_
#define DEFINED_TYPEDEF_FOR_EPARKRecoverInd_

typedef uint8_T EPARKRecoverInd;

// enum EPARKRecoverInd
const EPARKRecoverInd EPARKRecoverInd_REC_NoPrompt = 0U;// Default value
const EPARKRecoverInd EPARKRecoverInd_REC_PauseCommand = 1U;
const EPARKRecoverInd EPARKRecoverInd_REC_ObjectOnPath = 2U;
const EPARKRecoverInd EPARKRecoverInd_REC_DoorOpen = 3U;
const EPARKRecoverInd EPARKRecoverInd_REC_BrakePadal = 4U;
const EPARKRecoverInd EPARKRecoverInd_REC_MirrorFold = 5U;
const EPARKRecoverInd EPARKRecoverInd_REC_SeatBelt = 6U;
const EPARKRecoverInd EPARKRecoverInd_REC_BlueTooth = 7U;
const EPARKRecoverInd EPARKRecoverInd_REC_HoodOpen = 8U;
const EPARKRecoverInd EPARKRecoverInd_REC_TrunkOpen = 9U;
const EPARKRecoverInd EPARKRecoverInd_REC_RearSideCommingCar = 10U;
const EPARKRecoverInd EPARKRecoverInd_REC_FrontSideCommingCar = 11U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EParkTypeVariant_
#define DEFINED_TYPEDEF_FOR_EParkTypeVariant_

typedef uint8_T EParkTypeVariant;

// enum EParkTypeVariant
const EParkTypeVariant PARKTYPECONFIG_NONE = 0U;
const EParkTypeVariant PARKTYPECONFIG_APA = 1U;
const EParkTypeVariant PARKTYPECONFIG_RPA = 2U;
const EParkTypeVariant PARKTYPECONFIG_APA_RPA = 3U;// Default value

#endif

#ifndef DEFINED_TYPEDEF_FOR_EPARKDriverIndExt_
#define DEFINED_TYPEDEF_FOR_EPARKDriverIndExt_

typedef uint8_T EPARKDriverIndExt;

// enum EPARKDriverIndExt
const EPARKDriverIndExt EPARKDriverIndExt_PARKDRVEXT_NoRequest = 0U;// Default value
const EPARKDriverIndExt EPARKDriverIndExt_PARKDRVEXT_PayAttentionToSurrounding =
  1U;
const EPARKDriverIndExt EPARKDriverIndExt_PARKDRVEXT_ParkingHasBeenCompleted =
  2U;
const EPARKDriverIndExt EPARKDriverIndExt_PARKDRVEXT_ConfirmTheParkingDirection =
  3U;
const EPARKDriverIndExt
  EPARKDriverIndExt_PARKDRVEXT_ParkingOutPayAttentionToSurrounding = 4U;
const EPARKDriverIndExt EPARKDriverIndExt_PARKDRVEXT_FrontIsClear = 5U;
const EPARKDriverIndExt EPARKDriverIndExt_PARKDRVEXT_Reserved = 6U;
const EPARKDriverIndExt EPARKDriverIndExt_PARKDRVEXT_SelectTheParkingMode = 7U;
const EPARKDriverIndExt EPARKDriverIndExt_PARKDRVEXT_ConnectToBluetooth = 8U;
const EPARKDriverIndExt EPARKDriverIndExt_PARKDRVEXT_CleanTheCamera = 9U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EPARKConfirmButton_
#define DEFINED_TYPEDEF_FOR_EPARKConfirmButton_

typedef uint8_T EPARKConfirmButton;

// enum EPARKConfirmButton
const EPARKConfirmButton EPARKConfirmButton_NOT_PRESS = 0U;// Default value
const EPARKConfirmButton EPARKConfirmButton_PRESS = 1U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EParkSlot_
#define DEFINED_TYPEDEF_FOR_EParkSlot_

typedef enum
{
  NONE = 0,                            // Default value
  LEFT_FIRST,
  LEFT_SECOND,
  LEFT_THIRD,
  LEFT_FOURTH,
  RIGHT_FIRST,
  RIGHT_SECOND,
  RIGHT_THIRD,
  RIGHT_FOURTH
}
EParkSlot;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EParkTypeOut_
#define DEFINED_TYPEDEF_FOR_EParkTypeOut_

typedef uint8_T EParkTypeOut;

// enum EParkTypeOut
const EParkTypeOut EParkTypeOut_NONE = 0U;// Default value
const EParkTypeOut EParkTypeOut_APA = 1U;
const EParkTypeOut EParkTypeOut_RPA = 2U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EParkOutSideButton_
#define DEFINED_TYPEDEF_FOR_EParkOutSideButton_

typedef uint8_T EParkOutSideButton;

// enum EParkOutSideButton
const EParkOutSideButton EParkOutSideButton_EPARK_OUT_SIDE_NONE = 0U;// Default value
const EParkOutSideButton EParkOutSideButton_EPARK_OUT_SIDE_LEFT = 1U;
const EParkOutSideButton EParkOutSideButton_EPARK_OUT_SIDE_RIGHT = 2U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EFreeParkingSpaceTypeButton_
#define DEFINED_TYPEDEF_FOR_EFreeParkingSpaceTypeButton_

typedef uint8_T EFreeParkingSpaceTypeButton;

// enum EFreeParkingSpaceTypeButton
const EFreeParkingSpaceTypeButton
  EFreeParkingSpaceTypeButton_IPS_PS_TYPE_UNKNOWN = 0U;// Default value
const EFreeParkingSpaceTypeButton EFreeParkingSpaceTypeButton_IPS_PS_TYPE_CROSS =
  1U;
const EFreeParkingSpaceTypeButton
  EFreeParkingSpaceTypeButton_IPS_PS_TYPE_PARALLEL = 2U;
const EFreeParkingSpaceTypeButton
  EFreeParkingSpaceTypeButton_IPS_PS_TYPE_DIAGONAL = 3U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EVRFailSt_
#define DEFINED_TYPEDEF_FOR_EVRFailSt_

typedef uint8_T EVRFailSt;

// enum EVRFailSt
const EVRFailSt EVRFailSt_No_Fail = 0U;// Default value
const EVRFailSt EVRFailSt_Fail = 1U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ESvsVehTransSts_
#define DEFINED_TYPEDEF_FOR_ESvsVehTransSts_

typedef enum
{
  ESvsVehTransSts_INVALID = 0,         // Default value
  ESvsVehTransSts_OPEN,
  ESvsVehTransSts_CLOSE,
  ESvsVehTransSts_RESERVED
}
ESvsVehTransSts;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EFreeParkingConfirmButton_
#define DEFINED_TYPEDEF_FOR_EFreeParkingConfirmButton_

typedef uint8_T EFreeParkingConfirmButton;

// enum EFreeParkingConfirmButton
const EFreeParkingConfirmButton EFreeParkingConfirmButton_FP_CONFIRM_NOT_PRESS =
  0U;                                  // Default value
const EFreeParkingConfirmButton EFreeParkingConfirmButton_FP_CONFIRM_PRESSED =
  1U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EFreeParkingReturnButton_
#define DEFINED_TYPEDEF_FOR_EFreeParkingReturnButton_

typedef enum
{
  FP_RETURN_NOT_PRESS = 0,             // Default value
  FP_RETURN_PRESSED
}
EFreeParkingReturnButton;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EParkDirection_
#define DEFINED_TYPEDEF_FOR_EParkDirection_

typedef enum
{
  DIRECTION_NONE = 0,                  // Default value
  DIRECTION_FRONTIN,
  DIRECTION_REARIN
}
EParkDirection;

#endif

// Forward declaration for rtModel
typedef struct tag_RTM RT_MODEL;

#endif                  // RTW_HEADER_ViewModexViewStateMachine_R2015b_types_h_

//
// File trailer for generated code.
//
// [EOF]
//
