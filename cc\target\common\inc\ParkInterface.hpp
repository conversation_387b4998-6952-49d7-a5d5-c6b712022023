#ifndef CC_TARGET_COMMON_PARK_INTERFACE_HPP_INCLUDED
#define CC_TARGET_COMMON_PARK_INTERFACE_HPP_INCLUDED

#include "vfc/core/vfc_types.hpp"
#include <string>

#include "pc/generic/util/coding/inc/CodingManager.h"

namespace cc
{
namespace target
{
namespace common
{

// class ParkInterfaceSettings : public pc::util::coding::ISerializable
// {
// public:
//     SERIALIZABLE(ParkInterfaceSettings)
//     {
//         ADD_STRING_MEMBER(vehicleType);
//     }

//     std::string m_vehicleType{"sghl"};
// };

// static pc::util::coding::Item<ParkInterfaceSettings> g_parkSettings("ParkSettings");

constexpr vfc::uint8_t NUM_OBJECT          = 50u;
constexpr vfc::uint8_t NUM_SLOT            = 50u;
constexpr vfc::uint8_t NUM_GUIDELINE_POINT = 120u;

enum class EApaStatus : vfc::uint8_t
{
    PassiveStandBy = 0,
    Searching,
    GuidanceActive,
    GuidanceSuspend,
    GuidanceTerminate,
    GuidanceCompleted,
    AutomaticParkingIsNotAvailable,
    ParkAssistStandby
};

enum class EParkingStage : vfc::uint8_t
{
    Invalid = 0u,
    ParkIn,
    ParkOut,
    FreeParking
};

enum class EParkingMode : vfc::uint8_t
{
    Invalid = 0u,
    Apa,
    Hpa,
    Avp
};

enum class EParkInDirection : vfc::uint8_t
{
    Unknown = 0u,
    Left,
    Right,
    Vertical
};

enum class EParkingSpaceStatus : vfc::uint8_t
{
    NoInformation = 0,
    ParkingSpaceOccupied, // slot is not free
    ParkingSpaceInvacant, // slot is free, but not released
    ParkingSpaceVacant,   // slot is free, and released
    ParkingSpaceHighlight // slot is choosen, or selected
};

enum class EParkingSpaceType : vfc::uint8_t
{
    NoInformation = 0,
    GeneralParkingSpace,
    NoParkingSpace,
    DisabledParkingOnly,
    MechanicalParkingSpace,
    E4ParkingSpace,
    LadyParkingOnly,
    ChargerParkingSpace
};

enum class ObjectType : vfc::uint8_t
{
    NoInformation = 0, // not valid object, not show
    Car,
    Truck,
    Bus,
    Pedestrian,
    BicyclistWithMan, // 5
    TricyclistWithMan,
    TrafficBarrierCone,
    SpeedBumps,
    TrafficBarrierParkingLock,
    LimitedBlock, // 10
    WaterHorse,
    ShoppingTrolleyWithMan,
    ParmWithMan,
    TrafficBarrierWarnningTriangle,
    TrafficBarrierBollardSleeve, // 15
    MotorcycleWithMan,
    PedestrianChild,
    FireHydrant,
    FireBox,
    TrashCan,           // 20
    SmallSweeper,       // A small sweeper
    ThinRoundPile,      // Fine round pile
    SphericalStonePier, // Spherical stone piers
    PillarStonePier,    // Pillar stone pier
    SquareStonePier,    // 25 // Square stone pier
    Trees,
    Cat,
    Dogs,
    GroundMarchingArrowReticle,
    Gridlines, // 30
    Pillars,
    Crosswalks,
    ChargingPile, // Charging pile or charging mark
    BasementArea,
    SUV, // 35
    Bucket,
    Wall,
    BicyclistWithoutMan,
    MotorcycleWithoutMan,
    TricyclistWithoutMan, // 40
    TrafficBarrierWarningTrianglesForMotorVehicle,
    Fence,
    BasementGate,
    EntranceExitOfBasement,
    ShoppingTrolleyWithoutMan, // 45
    ParmWithoutMan,
    BasementGateOpen,
    GreenBelt
};

enum class ECrashRisk : vfc::uint8_t
{
    NoRisk = 0,
    Warning,
    RiskLevel1,
    RiskLevel2,
    RiskLevel3,
    Highlight,
    Reserved
};

enum class EMoveST : vfc::uint8_t
{
    NotValid = 0,
    Standstill,
    MoveLevel1,
    MoveLevel2,
    Reserved
};

enum class ETurnSignalLampSt : vfc::uint8_t
{
    Off = 0,
    LeftOn,
    RightOn,
    BothOn,
    Reserved
};

enum class EHighLowBeamLampsSt : vfc::uint8_t
{
    Off = 0,
    LowBeamOn,
    HighBeamOn,
    Reserved
};

enum class EBrakeLightSt : vfc::uint8_t
{
    Off = 0,
    On,
    Reserved
};

enum class EReversingLightSt : vfc::uint8_t
{
    Off = 0,
    On,
    Reserved
};

enum class EPocDirSel : vfc::uint8_t
{
    None = 0,
    FrontLeftCross,
    FrontLeftParallel,
    FrontOut,
    FrontRightCross,
    FrontRightParallel, // 5
    BackOut,
    BackLeftCross,
    BackRightCross,
    Reserved
};

struct Point2D
{
    Point2D() = default;
    Point2D(vfc::float32_t f_x, vfc::float32_t f_y)
        : m_x(f_x)
        , m_y(f_y)
    {
    }

    void reset()
    {
        m_x = 0.0f;
        m_y = 0.0f;
    }
    vfc::float32_t m_x {0.0f};
    vfc::float32_t m_y {0.0f};
};

struct Point3D
{
    vfc::float32_t m_x {0.0f};
    vfc::float32_t m_y {0.0f};
    vfc::float32_t m_z {0.0f};
};

struct Angle3D
{
    vfc::float32_t m_roll  {0.0f};
    vfc::float32_t m_yaw   {0.0f};
    vfc::float32_t m_pitch {0.0f};
};

struct ParkingObject
{
    vfc::int32_t        m_objectId           {0};
    vfc::float32_t      m_shapeHeight        {0.0f};
    vfc::float32_t      m_shapeLength        {0.0f};
    vfc::float32_t      m_shapeWidth         {0.0f};
    Point3D             m_position           {};
    vfc::float32_t      m_heading            {0.0f}; // -pi to +pi
    ObjectType          m_typeInfo           {ObjectType::NoInformation};
    ECrashRisk          m_crashRisk          {ECrashRisk::NoRisk};
    EMoveST             m_moveST             {EMoveST::NotValid};
    vfc::float32_t      m_absoluteVelocity   {0.0f};
    ETurnSignalLampSt   m_turnSignalLampSt   {ETurnSignalLampSt::Off};
    EHighLowBeamLampsSt m_highLowBeamLampsSt {EHighLowBeamLampsSt::Off};
    EBrakeLightSt       m_brakeLightSt       {EBrakeLightSt::Off};
    EReversingLightSt   m_reversingLightSt   {EReversingLightSt::Off};
};

struct ParkingObjectInfo
{
    vfc::int32_t  m_checksum {0};
    vfc::int32_t  m_counter  {0};
    std::string   m_timestamp;
    ParkingObject m_objectArray[NUM_OBJECT];
};

struct ParkingSlotInfo
{
    vfc::int32_t        m_parkngSpcID  {0};
    EParkingSpaceStatus m_parkngSpcSts {EParkingSpaceStatus::NoInformation};
    vfc::uint8_t        m_parkngSpcCode; // 1-6
    EParkingSpaceType   m_parkngSpcType {EParkingSpaceType::NoInformation};
    vfc::int16_t        m_parkngSpcNum;
    Point2D             m_corner1;
    Point2D             m_corner2;
    Point2D             m_corner3;
    Point2D             m_corner4;
    vfc::int16_t        m_e4CornerMark {0}; // TODO: not defined in the pdf
};

struct EgoPoseInfo
{
    Point3D m_position {};
    Angle3D m_angle    {};
};

struct GuideLineInfo
{
    vfc::int32_t   m_trackPointID  {0};
    Point2D        m_pointLocation {};
    vfc::float32_t m_heading       {0.0f};
};

struct TrajectoryInfo // TODO, not defined in the pdf & data trace
{
};

struct ParkingStaticInfo
{
    vfc::float32_t m_parkingDistanceLeft    {0.0f};
    vfc::float32_t m_cruisingDistanceLeft   {0.0f};
    vfc::float32_t m_learningDistance       {0.0f};
    vfc::int32_t   m_pathVeriRate           {0};
    vfc::uint16_t  m_avoidPedestriansNumber {0u};
    vfc::uint16_t  m_avoidVehiclesNumber    {0u};
    vfc::uint16_t  m_pathLearnFailDisp      {0u};
    vfc::uint16_t  m_speedBumpNumber        {0u};
};

struct ParkingRealTimeData
{
    ParkingSlotInfo   m_parkingSlotInfo[NUM_SLOT]          {};
    ParkingObjectInfo m_parkingObjectInfo                  {};
    GuideLineInfo     m_GuideLineInfo[NUM_GUIDELINE_POINT] {};
    EgoPoseInfo       m_egoPoseInfo;
    TrajectoryInfo    m_trajectoryInfo;
    ParkingStaticInfo m_parkingStaticInfo;
    vfc::int16_t      m_newViewAngleReq {0};
};

struct PocState
{
    bool m_FrntLeParallelSts {false}; // true: show; false: not show.
    bool m_FrntLeCrossSts    {false};
    bool m_FrntRiParallelSts {false};
    bool m_FrntRiCrossSts    {false};
    bool m_FrntCrossSts      {false};
    bool m_BackCrossSts      {false};
    bool m_BackLeCrossSts    {false};
    bool m_BackRiCrossSts    {false};
};

// freeparking
struct FreeParkingSlot // output
{
    vfc::float32_t m_x {0.0f};   // cm, position of freeparking slot picture center, origin is ego car rear axel middle
                                 // point (front:+x, left:+y)
    vfc::float32_t m_y   {0.0f}; // cm
    vfc::float32_t m_yaw {0.0f}; // degree
};

struct SlotCorner
{
    Point2D m_point1{}; // cm
    Point2D m_point2{}; // cm
    Point2D m_point3{}; // cm
    Point2D m_point4{}; // cm
};

enum class EFreeParkingStage : vfc::uint8_t
{
    None = 0u,      // not show any slot
    InFpStandstill, // show freeparking slot
    GuidanceStart,  // show target slot during guidance
    GuidanceFinish, // not show any slot
    InFpMoving      // not show any slot
};

enum class EFreeParkingSlotType : vfc::uint8_t
{
    HorizontalSlot = 0u,
    VerticalSlot,
    DiagonalSlot
};

enum class EFreeParkingSlotState : vfc::uint8_t
{
    UNAVAILABLE = 0u,
    AVAILABLE,
    UNAVAILABLE_BARRIER
};

struct FreeParkingIn
{
    EFreeParkingStage m_parkStage      {EFreeParkingStage::None}; // this is state
    EFreeParkingSlotState              m_isSlotParkable {EFreeParkingSlotState::UNAVAILABLE};                   // this is state
    /** @brief
     * true: support 360deg freeparking, the picture could be rotate in 360deg, below
     * m_slotType is not useful; false: not support 360deg, only support 20deg rotate,
     * below m_slotType is useful.  */
    bool                 m_is360FreeParking {true};
    EFreeParkingSlotType m_slotType         {EFreeParkingSlotType::HorizontalSlot};
    vfc::int32_t        m_ctr;
};


enum class ESpecialParkingStatus : vfc::uint8_t
{
    OFF = 0,
    ON = 1
};

constexpr bool isSpecialParkingStatusValid(int value) {
    return value == 0 || value == 1;
};

enum class ERotationDirection : vfc::uint8_t
{
    INVAILD = 0,
    CENTROID_ANTICLOCKWISE = 1, // Counterclockwise around the centroid (from the vehicle center)
    CENTROID_CLOCKWISE = 2, // Clockwise around the centroid (from the center of the vehicle)
    REAR_AXLE_CENTER_ANTICLOCKWISE = 3, // Counterclockwise around the center of the rear axle
    REAR_AXLE_CENTER_CLOCKWISE = 4, // Clockwise around the center of the rear axle
    LEFT_FRONT_WHEEL_ANTICLOCKWISE = 5, // Rotate around the left front wheel counterclockwise
    LEFT_FRONT_WHEEL_CLOCKWISE = 6, //Clockwise around the left front wheel
    RIGHT_FRONT_WHEEL_ANTICLOCKWISE = 7, // Counterclockwise around the right front wheel;
    RIGHT_FRONT_WHEEL_CLOCKWISE = 8, //Clockwise around the right front wheel;
    LEFT_REAR_WHEEL_ANTICLOCKWISE = 9, // Rotate counterclockwise around the left rear wheel
    LEFT_REAR_WHEEL_CLOCKWISE = 10, //Clockwise around the left rear wheel
    RIGHT_REAR_WHEEL_ANTICLOCKWISE = 11, // Counterclockwise around the right rear wheel;
    RIGHT_REAR_WHEEL_CLOCKWISE = 12, // Clockwise around the right rear wheel
    RIGHT_PARALLEL = 13,
    LEFT_PARALLEL = 14
};

struct ParkhmiToSvs
{
    EApaStatus              m_apaStatus;
    EParkingStage           m_parkingStage;
    EParkingMode            m_parkMode;
    EParkInDirection        m_parkInDirection;
    ParkingRealTimeData     m_parkingRealTimeData;
    PocState                m_pocEnabledDir;
    EPocDirSel              m_pocRecommandDir;
    FreeParkingIn           m_freeParkingIn;
    SlotCorner              m_targetSlotPosition; // target slot in guidance
    ESpecialParkingStatus   m_specialParkingStatus;
    ERotationDirection      m_rotationDirection;
};

struct SvsToParkhmi
{
    vfc::int16_t    m_parkngSpcIDSelected;
    EPocDirSel      m_pocDirSelected;
    FreeParkingSlot m_freeParkingOut;
    vfc::int32_t    m_zoomLevel;
};


} // namespace common
} // namespace target
} // namespace cc

#endif // CC_TARGET_COMMON_PARK_INTERFACE_HPP_INCLUDED
