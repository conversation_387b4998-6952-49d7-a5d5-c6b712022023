//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: LIA1LR David Liepelt (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomDataSync.h
/// @brief
//=============================================================================

#ifndef CC_TARGET_LINUX_CUSTOMDATASYNC_H
#define CC_TARGET_LINUX_CUSTOMDATASYNC_H

#include <cstdint> //! required by pp/rpb_* headers
#include "cc/target/common/inc/pp/rbp_platform_types.h" // required for linuxIpcPSInterface
#include "cc/target/common/inc/CustomPSInterface.h"
#include "pc/svs/util/math/inc/Math2D.h"

#include "cc/daddy/inc/CustomDaddyPorts.h"
#include <iomanip>
#include "cc/target/common/inc/freeparking_types.h"
#include "cc/target/common/inc/linux_svs_interface.hpp"
#include "pc/generic/util/logging/inc/Logging.h"
#include "vfc/core/vfc_siunits.hpp"

#define HEX_OUTPUT_FORMAT     "0x" << std::setfill ('0') << std::setw (2) << std::hex

namespace cc
{
namespace target
{
namespace linux
{

inline float cm2m(float value_cm)  { return (value_cm * 0.01f); }

class CustomDataSyncSettings;
extern pc::util::coding::Item<CustomDataSyncSettings> g_customDataSyncSettings;

class CustomDataSync
{
public:

  CustomDataSync();

  ~CustomDataSync();

    void readVHMSignals(const cc::target::common::CVhmAbstOutputStripped& f_vhmAbstOutput);
    void readLSMGSignals(const cc::target::common::LSMGDataSVS_st& f_LSMG_in);
    void readPasAPPSignals(const cc::target::common::PasAPPDataSVS_st& f_pasAPP_in);
    void readParkhmiSignals( const cc::target::common::ParkhmiToSvs& f_parkhmi_in );
    // void readAPGSignals(const cc::daddy::PMA_TravelDistDesired& f_data_in);
    void readPfValSignals(const cc::target::common::CValInOutputPfStripped& f_data_in);
    void readCpjValSignals(const cc::target::common::StrippedCpjVal_st& f_StrippedCpjVal_in);
    void readDoorSignals(const cc::target::common::CValInOutputPfStripped& f_data_in);
    void inputVhmSignals(const cc::target::common::CValInOutputPfStripped& f_data_in);
    void readTimeshowSignals(const cc::target::common::TimeShowStyle& f_data_in);
    
    bool writeDataFromSvs(cc::target::common::DataContainerFromSvs& f_dataFromSvs);
    // bool writeFreeparkingInfo( cc::target::common::CAnywhereParkingRectStripped& f_freeparkingInfo);

    //! Compensate Odo
    static void compensateOdo(pc::daddy::OdometryDataDaddy& f_container);

private:

    //! Copy constructor is not permitted.
    CustomDataSync (const CustomDataSync& other) = delete;
    //! Copy assignment operator is not permitted.
    CustomDataSync& operator=(const CustomDataSync& other) = delete;

    ::daddy::TLatestReceiverPort<cc::daddy::CameraPositionDaddy_t> m_CameraPositionIPCReceiver;

    ::daddy::TLatestReceiverPort<cc::daddy::EViewAnimationDaddy> m_animationDaddy_IPCReceiver;

    //! 3D Free mode
    ::daddy::TLatestReceiverPort<cc::daddy::CamPosAxis2RqDaddy> m_camPosAxis2Rq_IPCReceiver;
    ::daddy::TLatestReceiverPort<cc::daddy::SvsToParkhmi_t> m_SvsToParkhmi_IPCReceiver;
    ::daddy::TLatestReceiverPort<cc::daddy::FreeParkingSlot_t> m_FreeParkingSlot_IPCReceiverPort;
    ::daddy::TLatestReceiverPort<cc::daddy::SlotSelectedId_t> m_SlotSelectedId_IPCReceiverPort;
    ::daddy::TLatestReceiverPort<cc::daddy::ParkoutSelectedDirection_t> m_ParkoutSelectedDirection_IPCReceiverPort;
    ::daddy::TLatestReceiverPort<cc::daddy::ZoomLevel_t> m_ZoomLevel_IPCReceiverPort;

    pc::daddy::EGear m_convertedGear = pc::daddy::GEAR_INIT;

    pc::c2w::SatCamArray m_camArr;
};

} // namespace linux
} // namespace target
} // namespace cc

#endif // CC_TARGET_LINUX_CUSTOMIPCSYNC_H
