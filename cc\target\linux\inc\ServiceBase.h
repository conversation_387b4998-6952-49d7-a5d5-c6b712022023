#ifndef SVSAPP_SERVICEBASE_H
#define SVSAPP_SERVICEBASE_H

#include <vector>
#include <string>
#include <memory>
#include <list>
//#include "SVSApis.hpp"
#include "ServiceCallBack.h"
#include "ComDataStructure.h"

class ServiceBase {
public:
    ServiceBase() = default;
    virtual ~ServiceBase() {}
    virtual int init(std::shared_ptr<ServiceCallBack> callBack, std::shared_ptr<ServiceConfig> cfg) {return 0;}
    virtual int deInit() { return  0;}

    virtual int setCameraIntrinsicParameters(CameraParameter *pCameraParam, int size) {return 1;}

    virtual int setAvmView(int viewId, EWindowId windowId = E_WINDOW_ID__MAIN) {return 1;}
    virtual int OnTouchEvent(AVMTouchEventType action, int x, int y, float panVelocity) {return -1;}
    virtual int setSlideCallback(SlideCallback callback) {return 1;}

    virtual int startCalibration(CalibrationMode mode, CalibrationCallback callback) {return 1;}
    virtual int stopCalibration() {return 1;}

    virtual int setCarSpeed(float speed) {return 1;}
    virtual void setCarGear(CarGearStatus gears) {}
    virtual int SetVehicleWheelPulse(unsigned char *wheelSpeedPulse, int size) { return 1;}
    virtual void setVehicleWheelSpeed(unsigned char *speed) {}
    virtual void setVehicleWheelDirection(char *direction, int length) {}
    virtual void setVehicleSteerAngleArray(char *steerAngle, int length) {}
    virtual int setRearWheelAngle(bool valid, int left, int right) { return 1;}

    virtual int setFrontRearRadarDistance(int *distance, int length) {return 1;}
    virtual int setLeftRightRadarDistance(int *distance, int length) {return 1;}

    virtual void setCarTransparent(bool transparent) {}
    virtual void setCarTransparentMode(AVMTransparentMode mode) {}
    virtual void setCarColor(CarColor carColor) {}

    virtual int setLightStatus(AVMLightType light, AVMState state) {return 1;}
    virtual int setAirSuspensionHeight(int mode, int leftFront, int rightFront, int leftRear, int rightRear) {return 1;}

    virtual void setDoorStatus(AVMDoorType door, AVMState status) {}
    virtual void showDoorWarningWidth(bool show, float angle, int carDoorClassify) {}
    virtual void setDoorAngle(AVMDoorType door,float angle) {}
    virtual int setDoorWidthIndicationStatus(AVMDoorType door, AVMState state) {return 1;}

    virtual void setGuidelineDisplay (CarGuideLine mode) {};
    virtual int setRadarWallVisible(bool visible) {return 1;}
    virtual int setTrailerLine(bool flag) {return 1;}

    virtual int setParkMode(AVMParkMode mode) { return 1;}
    virtual int setParkingSpaceSelectedCb(ParkingSpaceSelectedCallback callback){ return 1;}
    virtual int setSpaceSlotState(AVMParkSlotState state) { return 1;}
    virtual int setParkingRealTimeDataServiceData(AVMParkingSlotVertex vertexPos) {return 1;}

    virtual int setCarRotateAngle(float hAngle, float vAngle) {}
    virtual int getCarRotateAngle(float *hAngle, float *vAngle) {}
    virtual int setSurround(int angle) {return 1;}
    virtual void set3DCarZoomLevel(AVMZoomLevel zoomLevel){}

    virtual int setBevFOV(bool fovScale) {return 1;}
    virtual int setRemoveDistortion( bool bRemoveDis) {return 1;}

    virtual int getSDKVersion(char *sdkVersion, int len) {return 1;}
    virtual void setLogEnable(bool enable){}
    virtual void setLogCallback(CB_FUNC_LOG_PRINT callback){}

    virtual int setFrontWheelGuideLine(bool visible, char angleHigh, char angleLow) {return 1;}
    virtual int setBackWheelGuideLine( bool visible, char angleHigh, char angleLow) {return 1;}

    // below interface is not used
    // start
    virtual void setTrailLineDisplay(bool display) {}
    virtual void setVin(std::string vin) {}
    virtual void setCpcDumpPath(std::string fPath) {}
    virtual int calPhysicalDistanceByMovecar(int x, int y) { return 1;}
    virtual int* calFourPointPixelByPhysicalDistance(int l ,int t,int r,int b) { return nullptr;}
    virtual void setWheelRailMode(WheelRailMode mode) {}
    virtual void setMoveAreaView(CanMoveView canMoveView) {}
    virtual void setRadarStopVisible(bool visible) {}
    virtual void setRadarLineVisible(bool visible) {}
    virtual void setFreeModeAngle(int angle) {}
    virtual void setRadarVisible(bool isVisible) {}
    virtual void changeObserveDistance(CanChangeSizeView view, float enlargeScale) {}
    virtual void setCameraOrder(int first, int second, int third, int fourth) {}
    virtual void setCamStatus(int state) {}
    virtual void setTcEnable(int enable) {}
    virtual void setDoorHandleStatus(bool open) {}
    virtual void setDoorHandleStatus(CarDoorId carDoorId, bool open)  {}
    virtual void setDoorStatus(CarDoorId door,CarDoorStatus status) {}
    virtual void setDoorStatus(CarDoorId door, float angle) {}
    virtual void setDoorWarning(int leftWarning, int rightWarning, int rearWarning) {}
    virtual void addLayoutListener(std::shared_ptr<LayoutListener> listener) {}  // not used in linux
    virtual void addFreeModeAngleListener(std::shared_ptr<FreeModeAngleListener> listener) {}  // not used in linux
    virtual void addAvmStatusListener(std::shared_ptr<onStatusChangeCb> onStatusChange,  // not used in linux
                                    std::shared_ptr<onMagicChangedCb> onMagicChanged){}
    virtual void setTransitionAnimationListener(std::shared_ptr<TransitionAnimationListener> listener) {}  // not used in linux
    virtual void startOptionalSlot(std::shared_ptr<OptionslotListener> listener, OptionalSlotType type) {}  // freeparking not used in linux
    virtual void resetOptionalSlot() {}  // freeparking not used in linux
    virtual void exitOptionalSlot() {}  // freeparking not used in linux
    virtual void setOptionSlotVisible(bool visible) {}  // freeparking not used in linux
    virtual int setLayout(vehicleModelView view,  bool showAPA) { return 0;}
    virtual std::vector<float> getOverLookCarPosition() { return std::vector<float>();}
    virtual std::vector<float>  getOverLookPosition() { return std::vector<float>();}
    virtual std::vector<float>  getSingleViewPosition() { return std::vector<float>();}
    virtual std::vector<float>  getSingleViewExtraPosition() { return std::vector<float>();}
    virtual void setParklineDisplay (int mode) {}
    virtual bool setSensorDetectData(std::string detectData) { return false;}
    virtual bool setSensorDetectE4Data(std::string detectData){ return false;}
    virtual void setApaLayout(bool isFullScreen) {}
    virtual void setE4Status(int status) {}
    virtual void setE4CarRotationAngle(float rotationAngle) {}
    virtual void setE4CarRotatedAngle(float rotatedAngle)  {}
    virtual void setRotationCenterMode(int status)  {}
    virtual void setEnableParkingDirection(std::list<std::string>& directions)  {}
    virtual void setRecommendedParkingDirection(std::string direction)  {}
    virtual bool setHpaData(std::string data) { return false;}
    virtual void hideApa() {}
    virtual void showApa() {}
    virtual int setLeopardSwivelCircleRadius(int name, float value) { return 1;}
    virtual int setLeopardSwivelCircleCenter(float centerX, float centerY) { return 1;}
    virtual int setLeopardSwivelView(int view, int swivelStatus) { return 1;}
    virtual int setLeopardSwivelActiveStatus(int activeStatus) { return 1;}
    virtual int setLeopardSwivelEnable(bool enable) { return 1;}
    virtual void setViewCamLensValue(std::string interior, std::string type) {}

    virtual int get3DCarZoomLevel(int *zoomLevel) { return 1; };

    virtual int onTwoFingersTouchEvent(AVMTouchEventType action, int x1, int y1, int x2, int y2) { return 1; };
    virtual int getPhysicalXDistance(int *distance, int x, int y) {return 1; };
    virtual int getWarningBoundCoordinate(BoundCoordinate *pBoundCoord, int left, int top, int right, int bottom) {return 1; };
    virtual int calPixelByPhysicalDistance(int *pixel, int distance) {return 1; };
    virtual void notifyAppStatus(bool status){}
    virtual int setCrabAngles(CrabAngle angles) {}
    virtual int setCrabShowGuideLine(int isShow) {}
    // virtual int setCrabShowGear(int isShow) {}
    virtual void setRearViewMirrorStatus(AVMRearViewMirrorState status){}
    virtual void setVotState(VotState status){}
    virtual void setVotRequestRotationDirection(VotDirRotation requestRotationDirection){}
    virtual void setGoldLogo(bool isGold){}
    virtual void setTimeMarkVisible(bool visible) {}
    virtual void setTimeMarkFontSize(int fontSize){}
    virtual void setTimeMarkPosition(int x, int y) {}
    // end
};

#endif //SVSAPP_SERVICEBASE_H
