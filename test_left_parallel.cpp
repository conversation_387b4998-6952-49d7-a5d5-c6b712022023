// 测试 LEFT_PARALLEL 支持的简单验证代码
#include <iostream>

// 模拟枚举定义
enum class ERotationDirection : unsigned char
{
    INVAILD = 0,
    CENTROID_ANTICLOCKWISE = 1,
    CENTROID_CLOCKWISE = 2,
    REAR_AXLE_CENTER_ANTICLOCKWISE = 3,
    REAR_AXLE_CENTER_CLOCKWISE = 4,
    LEFT_FRONT_WHEEL_ANTICLOCKWISE = 5,
    LEFT_FRONT_WHEEL_CLOCKWISE = 6,
    RIGHT_FRONT_WHEEL_ANTICLOCKWISE = 7,
    RIGHT_FRONT_WHEEL_CLOCKWISE = 8,
    LEFT_REAR_WHEEL_ANTICLOCKWISE = 9,
    LEFT_REAR_WHEEL_CLOCKWISE = 10,
    RIGHT_REAR_WHEEL_ANTICLOCKWISE = 11,
    RIGHT_REAR_WHEEL_CLOCKWISE = 12,
    LEFT_PARALLEL = 13,
    RIGHT_PARALLEL = 14
};

// 模拟轨迹线绘制逻辑测试
void testTrajectoryDrawing(ERotationDirection direction)
{
    std::cout << "Testing trajectory drawing for direction: " << static_cast<int>(direction) << std::endl;
    
    if (direction != ERotationDirection::RIGHT_PARALLEL &&
        direction != ERotationDirection::LEFT_PARALLEL)
    {
        std::cout << "  Drawing curved trajectory (arc-based)" << std::endl;
    }
    else if (direction == ERotationDirection::RIGHT_PARALLEL)
    {
        std::cout << "  Drawing RIGHT_PARALLEL trajectory" << std::endl;
        std::cout << "    - X position: positive offset" << std::endl;
        std::cout << "    - Arrow direction: pointing left (negative X)" << std::endl;
    }
    else // LEFT_PARALLEL
    {
        std::cout << "  Drawing LEFT_PARALLEL trajectory" << std::endl;
        std::cout << "    - X position: negative offset (mirrored)" << std::endl;
        std::cout << "    - Arrow direction: pointing right (positive X)" << std::endl;
    }
}

// 模拟箭头绘制逻辑测试
void testArrowDrawing(ERotationDirection direction)
{
    std::cout << "Testing arrow drawing for direction: " << static_cast<int>(direction) << std::endl;
    
    if (direction != ERotationDirection::RIGHT_PARALLEL &&
        direction != ERotationDirection::LEFT_PARALLEL)
    {
        std::cout << "  Drawing curved arrow (tangent-based)" << std::endl;
    }
    else if (direction == ERotationDirection::RIGHT_PARALLEL)
    {
        std::cout << "  Drawing RIGHT_PARALLEL arrow" << std::endl;
        std::cout << "    - Arrow X: positive position" << std::endl;
        std::cout << "    - Arrow width: extends to negative X" << std::endl;
    }
    else // LEFT_PARALLEL
    {
        std::cout << "  Drawing LEFT_PARALLEL arrow" << std::endl;
        std::cout << "    - Arrow X: negative position (mirrored)" << std::endl;
        std::cout << "    - Arrow width: extends to positive X (mirrored)" << std::endl;
    }
}

int main()
{
    std::cout << "=== Testing LEFT_PARALLEL Support ===" << std::endl;
    std::cout << std::endl;
    
    // 测试不同方向的轨迹线绘制
    std::cout << "1. Testing trajectory drawing:" << std::endl;
    testTrajectoryDrawing(ERotationDirection::CENTROID_CLOCKWISE);
    testTrajectoryDrawing(ERotationDirection::RIGHT_PARALLEL);
    testTrajectoryDrawing(ERotationDirection::LEFT_PARALLEL);
    std::cout << std::endl;
    
    // 测试不同方向的箭头绘制
    std::cout << "2. Testing arrow drawing:" << std::endl;
    testArrowDrawing(ERotationDirection::CENTROID_CLOCKWISE);
    testArrowDrawing(ERotationDirection::RIGHT_PARALLEL);
    testArrowDrawing(ERotationDirection::LEFT_PARALLEL);
    std::cout << std::endl;
    
    std::cout << "=== Test completed ===" << std::endl;
    std::cout << "LEFT_PARALLEL support has been successfully added!" << std::endl;
    
    return 0;
}
